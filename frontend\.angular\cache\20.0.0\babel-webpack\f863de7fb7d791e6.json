{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { LoginComponent } from '../../components/auth/login/login.component';\nimport { RegisterComponent } from '../../components/auth/register/register.component';\nimport { EmailVerificationComponent } from '../../components/auth/email-verification/email-verification.component';\nimport { ForgotPasswordComponent } from '../../components/auth/forgot-password/forgot-password.component';\nimport { ResetPasswordComponent } from '../../components/auth/reset-password/reset-password.component';\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'verify-email',\n  component: EmailVerificationComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}];\nlet AuthModule = class AuthModule {};\nAuthModule = __decorate([NgModule({\n  declarations: [LoginComponent, RegisterComponent, EmailVerificationComponent, ForgotPasswordComponent, ResetPasswordComponent],\n  imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes),\n  // Angular Material\n  MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule]\n})], AuthModule);\nexport { AuthModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}