=== Page 29 ===

29•templates/update.html
Remember we made 2 controller methods so let's attach them to a route here:
That should be it! We can now update our posts.
Let's expand a bit and make a delete method.<form action="/post/{{ post.id }}/update" method="POST">
    {{ csrf_field }}
    <label for="">Title</label>
    <input type="text" name="title" value="{{ post.title }}"><br>
    <label>Body</label>
    <textarea name="body">{{ post.body }}</textarea><br>
    <input type="submit" value="Update">
</form>
routes/web.py
Route.get('/post/@id/update', 'PostController@update'),
Route.post('/post/@id/update', 'PostController@store'),
app/controllers/PostController.pyCreate The Routes:
Delete Method6/12/25, 3:02 AM Masonite Documentation