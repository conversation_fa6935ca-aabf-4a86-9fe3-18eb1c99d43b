=== Page 261 ===

261Now instead of returning the generic errors, the error message returned will be the one
you supplied.
Leaving out a message will result in the generic one still being returned for that value.
By default, Masonite will not throw exceptions when it encounters failed validations. You
can force Masonite to raise a ValueError when it hits a failed validation:"""
{
  'terms': 'off',
  'active': 'on',
}
"""
validate.accepted(['terms', 'active'], messages = {
    'terms': 'You must check the terms box on the bottom',
    'active': 'Make sure you are active'
})
Exceptions6/12/25, 3:02 AM Masonite Documentation