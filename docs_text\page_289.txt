=== Page 290 ===

290Y<PERSON> may want to collect specific kinds of objects from the container based on the key.
For example we may want all objects that start with "Exception" and end with "Hook" or
want all keys that end with "ExceptionHook" if we are building an exception handler.
We can easily collect all objects based on a key:
This will return a dictionary of all objects that are binded to the container that start with
anything and end with "ExceptionHook" such as "SentryExceptionHook" or
"AwesomeExceptionHook".
We can also do the opposite and collect everything that starts with a specific key:
This will collect all keys that start with "Sentry" such as "SentryWebhook" or
"SentryExceptionHandler."
Lastly, we may want to collect things that start with "Sentry" and end with "Hook"
This will get keys like "SentryExceptionHook" and "SentryHandlerHook"'request' in app
app.collect('*ExceptionHook')
app.collect('Sentry*')
app.collect('Sentry*Hook')Collecting
Collect By Key
Collecting By Object6/12/25, 3:02 AM Masonite Documentation