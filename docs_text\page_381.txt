=== Page 382 ===

382Read more in the Uploading documentation.
Masonite 2 removes the bland error codes such as 404 and 500 errors and replaces them
with a cleaner view. This also allows you to add custom error pages.
Read more in the Status Codes documentation.
Providers are now explicitly imported at the top of the file and added to your PROVIDERS
list which is now located in config/providers.py. This completely removes the need
for string providers and boosts the performance of the application sustantiallyDRIVERS = {
  'disk': {
    'uploads': 'storage/uploads',
    'profiles': 'storage/static/users/profiles/images'
  },
  ...
}
Added Status Code Provider
Added Explicitly Imported Providers6/12/25, 3:02 AM Masonite Documentation