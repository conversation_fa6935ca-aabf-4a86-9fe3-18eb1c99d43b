=== Page 130 ===

130Authorizes class can be added to your User model to allow quick permission checks:
A fluent authorization api will now be available on User instances:
All of those methods receive the gate name as first argument and then some additional
arguments if required.
You can use the for_user() method on the Gate facade to make the verification
against a given user instead of the authenticated user.response = Gate.inspect("update-post", post)
if response.allowed():
     # do something
else:
     # not authorized and we can access message
     Session.flash("errors", response.message())
from masonite.authentication import Authenticates
from masonite.authorization import Authorizes
class User(Model, Authenticates, Authorizes):
    #..
user.can("delete-post", post)
user.cannot("access-admin")
user.can_any(["delete-post", "force-delete-post"], post)
from masonite.facades import Gate
user = User.find(1)
Gate.for_user(user).allows("create-post")Via the User Model
With a given user6/12/25, 3:02 AM Masonite Documentation