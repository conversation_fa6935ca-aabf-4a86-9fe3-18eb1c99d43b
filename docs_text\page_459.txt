=== Page 460 ===

460to this:
To manage the guards (and register new guards) there is the new 
AuthenticationProvider that needs to be added to your providers list.AUTH={
    'driver': env('AUTH_DRIVER', 'cookie'),
    'model': User,
}
AUTH = {
    'defaults': {
        'guard': 'web'
    },
    'guards': {
        'web': {
            'driver': 'cookie',
            'model': User,
            'drivers': { # 'cookie', 'jwt'
                'jwt': {
                    'reauthentication': True,
                    'lifetime': '5 minutes'
                }
            }
        },
    }
}
from masonite.providers import AuthenticationProvider
PROVIDERS = [
    # Framework Providers
    # ...
    AuthenticationProvider,
    # Third Party Providers
    #...
]Add The Authentication Provider6/12/25, 3:02 AM Masonite Documentation