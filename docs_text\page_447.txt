=== Page 448 ===

448In Masonite 2.1, route helpers were deprecated and you likely started receiving
deprecation warnings. In Masonite 2.2, these were removed. You may have had routes
that looks like this:
You will now need to remove all these and use the class based ones. To make this easier
we can just import the get and post helpers and alias them like this:
Impact: MEDIUM
Masonite 2.2 completely removes the validation library that shipped with Masonite in
favor of a brand new one that was built specifically for Masonite.
You'll need to add a new validation provider if you want your application to have the new
validation features.
Add it by importing it into config/providers.py and add it to your PROVIDERS list:from masonite.helpers.routes import get, post
ROUTES = [
    get('/url/home').name('home')
]
from masonite.routes import Get as get, Post as post
ROUTES = [
    get('/url/home').name('home')
]Removing route helpers
Impact: MEDIUM
Changed Validation
Validation Provider6/12/25, 3:02 AM Masonite Documentation