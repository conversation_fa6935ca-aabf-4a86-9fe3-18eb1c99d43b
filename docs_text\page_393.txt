=== Page 394 ===

394Now you can setup a way to send email verifications into your user signup workflow
simply but inherting a class to your User model.
Now all redirections set the status code implicitly instead of explicitly needing to set
them.
Now you can use craft middleware MiddlewareName in order to scaffold middleware
like other classes.
All views can optionally use dot notation instead of foward slashes:
is the same as:
We can now do container swapping which is swapping out a class when it is resolved. In
other words we may want to change what objects are returned when certain objects arereturn view.render('some/template/here')
return view.render('some.template.here')Added ability to set email verification
Request redirection set status codes
Added craft middleware command
View can use dot notation
Added Swap to container6/12/25, 3:02 AM Masonite Documentation