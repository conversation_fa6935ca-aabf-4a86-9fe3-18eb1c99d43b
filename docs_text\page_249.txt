=== Page 250 ===

250Now the rule is created we can use it in 1 of 2 ways.
Importing our rule
We can either import directly into our controller method:
or we can register our rule and use it with the Validator class as normal.
Register the ruledef passes(self, attribute, key, dictionary):
    """The passing criteria for this rule.
    ...
    """
    return attribute == 'Masonite'
def message(self, key):
    return '{} must be equal to Masonite'.format(key)
def negated_message(self, key):
    return '{} must not be equal to Masonite'.format(key)
from masonite.validation import Validator
from app.rules.equals_masonite import equals_masonite
def show(self, request: Request, validate: Validator):
    """
    Incoming Input: {
        'user': 'username123',
        'company': 'Masonite'
    }
    """
    valid = request.validate(
        validate.required(['user', 'company']),
        equals_masonite('company')
    )Registering our Rule6/12/25, 3:02 AM Masonite Documentation