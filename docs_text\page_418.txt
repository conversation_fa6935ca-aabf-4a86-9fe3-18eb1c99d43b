=== Page 419 ===

419duplication of some jobs running via supervisor you may need to specify the option that
offsets when supervisor commands are started.
In the past we had route helpers which were functions that wrapped some route logic.
These were located in the masonite.helpers.routes namespace. These were
deprecated in Masonite 2.3 and now removed in Masonite 3.0.
Obviously the biggest change is dropping Orator and picking up Masonite ORM. This new
ORM is designed to be a drop in replacement for Or<PERSON>. I have upgraded several projects
to use the new ORM and I had to change very minimal code to get it to work.
Theres a few resons we decided to drop support of Or<PERSON> but the main one was that
<PERSON><PERSON><PERSON><PERSON> (the creator of Or<PERSON>) has his time occupied by other packages like Pendulum
(which Masonite ORM still uses) as well as Poetry. These are great packages and more
popular than <PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> does not know if he will pick <PERSON><PERSON> back up but the issues
and bugs have been piling up and the codebase was not up to my standard of being
maintained. My<PERSON> and a few maintainers have taken the time to create a new ORM
project called Masonite ORM.
Another reason is that we now have completely creative control over the ORM side of
Masonite. We don't have to go through someone who has complete control. Releases can
now be scheduled whenever we want and we can add whatever features we want. This is
a huge deal for Masonite.Dropped route helpers
Dropping Orator6/12/25, 3:02 AM Masonite Documentation