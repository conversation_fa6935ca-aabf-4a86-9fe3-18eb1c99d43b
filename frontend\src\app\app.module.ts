import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Angular Material
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';

// App Components
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { RateLimitNotificationComponent } from './components/rate-limit-notification/rate-limit-notification.component';
import { CrawlerComponent } from './crawler/crawler.component';

// Interceptors
import { AuthInterceptor } from './interceptors/auth.interceptor';

// Services
import { AuthService } from './services/auth.service';
import { PaymentService } from './services/payment.service';
import { TwoFactorService } from './services/two-factor.service';
import { LoadingService } from './services/loading.service';
import { RateLimitService } from './services/rate-limit.service';
import { CrawlerService } from './services/crawler.service';
import { DocumentGeneratorService } from './services/document-generator.service';

@NgModule({
  declarations: [
    AppComponent,
    CrawlerComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    ReactiveFormsModule,
    FormsModule,
    AppRoutingModule,
    RateLimitNotificationComponent,
    
    // Angular Material
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    AuthService,
    PaymentService,
    TwoFactorService,
    LoadingService,
    RateLimitService,
    CrawlerService,
    DocumentGeneratorService
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
