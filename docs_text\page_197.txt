=== Page 198 ===

198Sending SMS notifications in Masonite is powered by Vonage (formerly Nexmo). Before
you can send notifications via Von<PERSON>, you need to install the vonage Python client.
Then you should configure the VONAGE_KEY and VONAGE_SECRET credentials in 
notifications.py configuration file:
You can also define (globally) sms_from which is the phone number or name that your
SMS will be sent from. You can generate a phone number for your application in the
Vonage dashboard.
You should define a to_vonage method on the notification class to specify how to build
the sms notification content.
If the SMS notification contains unicode characters, you should call the unicode method
when constructing the notification
$ pip install vonage
# config/notifications.py
VONAGE = {
  "key": env("VONAGE_KEY"),
  "secret": env("VONAGE_SECRET"),
  "sms_from": "1234567890"
}
from masonite.notification.components import Sms
class Welcome(Notification):
    def to_vonage(self, notifiable):
        return Sms().text("Welcome!")
    def via(self, notifiable):
        return ["vonage"]
Options6/12/25, 3:02 AM Masonite Documentation