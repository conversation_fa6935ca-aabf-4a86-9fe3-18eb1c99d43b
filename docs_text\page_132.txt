=== Page 133 ===

133An example policy for the Post model may look like this:
If an unknown policy is used then a PolicyDoesNotExist exception will be raised.
You can then use the Gate facade methods to authorize actions defined in your policies.
With the previously defined PostPolicy we could make the following calls:from masonite.facades import Gate
from app.models.Post import Post
from app.models.User import User
from app.policies.PostPolicy import PostPolicy
from app.policies.UserPolicy import UserPolicy
class MyAppProvider(Provider):
    #..
    def register(self):
        Gate.register_policies(
            [(Post, PostPolicy), (User, UserPolicy)],
        )
from masonite.authorization import Policy
class PostPolicy(Policy):
    def create(self, user):
        return user.email == "<EMAIL>"
    def view(self, user, instance):
        return True
    def update(self, user, instance):
        return user.id == instance.user_id
Authorizing Actions6/12/25, 3:02 AM Masonite Documentation