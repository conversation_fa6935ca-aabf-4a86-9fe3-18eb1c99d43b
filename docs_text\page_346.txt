=== Page 347 ===

347Masonite Debugbar
Masonite Debugbar is a really helpful way to see the stats of your application while you
are developing. You can use this information to help debug errors you are getting or even
optimize your models and queries for speed and memory issues.
Masonite Debugbar also supports AJAX calls which you will be able to see directly in a
dropdown on your toolbar.
Setting up Masonite Debugbar is simple.
First, install the package:
Put the provider at the end of your provider list:
Then publish the package:
$ pip install masonite-debugbar
from debugbar.providers import DebugProvider
PROVIDERS = [
    #.. 
    DebugProvider
]
$ python craft package:publish debugbarInstall6/12/25, 3:02 AM Masonite Documentation