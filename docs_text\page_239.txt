=== Page 240 ===

240and paste the PATH we just copied. Once we do that our cron should look like:
Exit out of nano. Now we just need to setup the actual cron job:
Now we just need to setup the cron task itself. This could be as easy as copying it and
pasting it into the nano editor again. You may need to change a few things though.
The first * * * * * part is a must and bascially means "run this every minute by
default". The next part is the location of your application which is dependant on where
you installed your Masonite application.
The next part is dependant on your setup. If you have a virtual environment then you need
to activate it by appending && source venv/bin/activate to the cron task. If you are
not running in a virtual environment then you can leave that part out.
Lastly we need to run the schedule command so we can append && craft 
schedule:run
Great! Now we have a cron job that will run the craft command every minute. Masonite
will decide which classes need to be executed.$ env EDITOR=nano crontab -eqcszae3zd4rfdsxs
PATH=/Users/<USER>/Programming/masonitetesting/venv/bin:/Library/Fram
eworks/Python.framework/Versions/3.7/bin:/usr/local/bin:/usr/bin:/bin:/
usr/sbin:/sbin:/Library/Frameworks/Python.framework/Versions/3.7/bin
Setting The Cron Task6/12/25, 3:02 AM Masonite Documentation