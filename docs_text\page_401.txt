=== Page 402 ===

402Added a new DatabaseTestcase so we can properly setup and teardown our database.
This works for sqlite databases by default to prevent your actual database from being
destroyed.
Before in templates we had to specify a path to go back to but most of the time we
wanted to go back to the current path.
Instead of:
We can now do:
In order to learn how to use this you can visit the documentation here.
Learn more in the Requests documentation here.
We built a new validation library from scratch and completely ripped out the old validation
code. Any current validation code will need to be updated to the new way.
The new way is MUCH better. You can read about it in the new validation section here.<form ..>
    {{ back(request().path) }}
</form>
<form ..>
    {{ back() }}
</form>
The back view helper now defaults to the
current path
Added a completely new validation library6/12/25, 3:02 AM Masonite Documentation