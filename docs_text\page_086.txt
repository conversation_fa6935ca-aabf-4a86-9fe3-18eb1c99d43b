=== Page 87 ===

87This is useful for redirecting back to the previous page. If you supply this helper then the
request.back() method will go to this endpoint. It's typically good to use this to go back to
a page after a form is submitted with errors:
Now when a form is submitted and you want to send the user back then in your controller
you just have to do:
You can access the session here:
Learn more about session in the Session documentation.
This allows you to easily fetch configuration values in your templates:
Gets a cookie:<form action="/some/url" method="POST">
    {{ back(request().path) }}
</form>
def show(self, response: Response):
    # Some failed validation
    return response.back()
<p> Error: {{ session().get('error') }} </p>
<h2> App Name: {{ config('application.name') }}</h2>Session
Config
Cookie6/12/25, 3:02 AM Masonite Documentation