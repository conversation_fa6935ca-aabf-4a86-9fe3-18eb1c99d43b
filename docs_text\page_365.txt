=== Page 366 ===

366Masonite 1.5
Masonite 1.5 is focused on a few bug fixes and changes to several core classes in order
to expand on the support of third party package integration.
Masonite 1.5 is releasing also with a new official package for adding RESTful API's to
your project. This package used API Resources which are classes that can add a plethora
of capabilities to your API endpoints. You can read more about it at the Masonite Entry
documentation.
Masonite 1.5 also adds additional support HTTP methods like PUT, PATCH and 
DELETE. More information about these new routes can be found under the Routing
documentation
Nearly all dependencies have been moved to the core Masonite package. The only thing
inside the project that is installed using craft new is the WSGI server (which is waitress
by default) and Masonite itself. This will improve the ability to change and update
dependencies.
Introduction
Masonite Entry
HTTP Methods
Moving Dependencies
Changing Form Request Methods6/12/25, 3:02 AM Masonite Documentation