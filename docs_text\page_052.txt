=== Page 53 ===

53Sometimes you want to optionally match routes and route parameters. For example you
may want to match /dashboard/user and /dashboard/user/settings to the same
controller method. In this event you can use optional parameters which are simply
replacing the @ symbol with a ?:
If you need to match any route that doesn't belong to the other route definitions you could
use the fallback method. For example, when you want to manage front routes from a 
react-router.
You can specify the subdomain you want this route to be matched to. If you only want
this route to be matched on a "docs" subdomain (docs.example.com):
Route compilers are a way to match on a certain route parameter by a specific type. For
example, if you only watch to match where the @user_id is an integer. You can do this
by appending a : character and compiler name to the parameter:
Available route compilers are:Route.get('/dashboard/?option', 'WelcomeController@show')
Route.fallback('WellcomeController@fallback')
Route.get('/dashboard/@user_id', 
'WelcomeController@show').domain('docs')
Route.get('/dashboard/@user_id:string', 'WelcomeController@show')Define a fallback route
Domain
Route Compilers6/12/25, 3:02 AM Masonite Documentation