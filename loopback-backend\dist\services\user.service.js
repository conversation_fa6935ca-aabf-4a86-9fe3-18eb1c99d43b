"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MyUserService = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const bcryptjs_1 = require("bcryptjs");
const repositories_1 = require("../repositories");
let MyUserService = class MyUserService {
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async verifyCredentials(credentials) {
        const invalidCredentialsError = 'Invalid email or password.';
        const foundUser = await this.userRepository.findOne({
            where: { email: credentials.email },
        });
        if (!foundUser) {
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        // Check if user is active
        if (!foundUser.isActive) {
            throw new rest_1.HttpErrors.Unauthorized('Account is deactivated.');
        }
        // Check if account is locked
        if (foundUser.lockUntil && foundUser.lockUntil > new Date()) {
            const lockTimeRemaining = Math.ceil((foundUser.lockUntil.getTime() - Date.now()) / (1000 * 60));
            throw new rest_1.HttpErrors.Unauthorized(`Account is temporarily locked due to multiple failed login attempts. ` +
                `Please try again in ${lockTimeRemaining} minutes. ` +
                `To unlock immediately: use "Forgot Password" to reset your password, ` +
                `login with OTP/SMS verification, or contact support for assistance.`);
        }
        if (!foundUser.password) {
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        const passwordMatched = await (0, bcryptjs_1.compare)(credentials.password, foundUser.password);
        if (!passwordMatched) {
            // Increment login attempts
            await this.handleFailedLogin(foundUser);
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        // Reset login attempts on successful login
        await this.handleSuccessfulLogin(foundUser);
        return foundUser;
    }
    convertToUserProfile(user) {
        return {
            [security_1.securityId]: user.id.toString(),
            name: `${user.firstName} ${user.lastName}`,
            id: user.id,
            email: user.email,
            roles: user.roles,
        };
    }
    async handleFailedLogin(user) {
        const maxAttempts = 5;
        const lockTime = 30 * 60 * 1000; // 30 minutes
        const attempts = (user.loginAttempts || 0) + 1;
        const updateData = {
            loginAttempts: attempts,
            updatedAt: new Date(),
        };
        if (attempts >= maxAttempts) {
            updateData.lockUntil = new Date(Date.now() + lockTime);
        }
        await this.userRepository.updateById(user.id, updateData);
    }
    async handleSuccessfulLogin(user) {
        await this.userRepository.updateById(user.id, {
            loginAttempts: 0,
            lockUntil: undefined,
            lastLoginAt: new Date(),
            updatedAt: new Date(),
        });
    }
};
exports.MyUserService = MyUserService;
exports.MyUserService = MyUserService = tslib_1.__decorate([
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository])
], MyUserService);
//# sourceMappingURL=user.service.js.map