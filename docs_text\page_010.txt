=== Page 11 ===

11Notice here we "type hinted" the View class. This is what Masonite calls "Auto resolving
dependency injection". If this doesn't make sense to you right now don't worry. The more
you read on the more you will understand.
You'll notice now that we are returning the blog view but it does not exist yet.
All views are in the templates directory. We can create a new file called 
templates/blog.html.
We can put some text in this file like:
Let's run the migration for the first time:
and then run the serverfrom masonite.view import View
# ...
def show(self, view: View):
    return view.render('blog')
templates/blog.html
This is a blog
terminal
$ python craft migrate
terminal
$ python craft serveCreating Our View6/12/25, 3:02 AM Masonite Documentation