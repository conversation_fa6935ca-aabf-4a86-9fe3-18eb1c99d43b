{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\n// Components\nimport { DashboardComponent } from './dashboard.component';\nconst routes = [{\n  path: '',\n  component: DashboardComponent\n}];\nlet DashboardModule = class DashboardModule {};\nDashboardModule = __decorate([NgModule({\n  declarations: [DashboardComponent],\n  imports: [CommonModule, RouterModule.forChild(routes),\n  // Angular Material\n  MatCardModule, MatButtonModule, MatIconModule, MatGridListModule]\n})], DashboardModule);\nexport { DashboardModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}