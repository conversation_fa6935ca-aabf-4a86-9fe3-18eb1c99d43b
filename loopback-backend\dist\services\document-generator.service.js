"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentGeneratorService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const repositories_1 = require("../repositories");
const child_process_1 = require("child_process");
const path = tslib_1.__importStar(require("path"));
const fs = tslib_1.__importStar(require("fs/promises"));
const crypto = tslib_1.__importStar(require("crypto"));
let DocumentGeneratorService = class DocumentGeneratorService {
    constructor(generatedDocumentRepository, crawledContentRepository) {
        this.generatedDocumentRepository = generatedDocumentRepository;
        this.crawledContentRepository = crawledContentRepository;
        this.activeGenerators = new Map();
        // Path to the Python document generator script
        this.generatorScriptPath = path.join(__dirname, '../../scripts/document_generator.py');
        this.outputDirectory = path.join(__dirname, '../../storage/generated_documents');
        this.ensureOutputDirectory();
    }
    /**
     * Ensure output directory exists
     */
    async ensureOutputDirectory() {
        try {
            await fs.access(this.outputDirectory);
        }
        catch {
            await fs.mkdir(this.outputDirectory, { recursive: true });
        }
    }
    /**
     * Generate document from crawled content
     */
    async generateDocument(crawlJobId, userId, options) {
        // Create document record
        const filename = this.generateFilename(options.format, options.organizationType);
        const accessToken = this.generateAccessToken();
        const document = await this.generatedDocumentRepository.create({
            crawlJobId,
            userId,
            filename,
            format: options.format,
            status: 'pending',
            organizationType: options.organizationType,
            selectedContentIds: options.selectedContentIds,
            generationOptions: options,
            destinationFolder: options.destinationFolder,
            accessToken,
            expiresAt: this.calculateExpiryDate(),
        });
        // Start generation process
        await this.startGenerationProcess(document.id, options);
        return document;
    }
    /**
     * Start document generation process
     */
    async startGenerationProcess(documentId, options) {
        try {
            // Update status to generating
            await this.generatedDocumentRepository.updateById(documentId, {
                status: 'generating',
                startedAt: new Date(),
                updatedAt: new Date(),
            });
            // Get selected content
            const selectedContent = await this.getSelectedContent(options.selectedContentIds);
            // Prepare generation data
            const generationData = {
                documentId,
                options,
                content: selectedContent,
                outputDirectory: this.outputDirectory,
            };
            // Start Python generator process
            await this.spawnGeneratorProcess(documentId, generationData);
        }
        catch (error) {
            await this.handleGenerationError(documentId, error);
        }
    }
    /**
     * Get selected content for document generation
     */
    async getSelectedContent(contentIds) {
        const contents = await this.crawledContentRepository.find({
            where: {
                id: { inq: contentIds },
                status: 'completed',
            },
            order: ['depth ASC', 'createdAt ASC'],
        });
        return contents;
    }
    /**
     * Spawn Python document generator process
     */
    async spawnGeneratorProcess(documentId, generationData) {
        const args = [
            this.generatorScriptPath,
            '--document-id', documentId,
            '--data', JSON.stringify(generationData),
            '--callback-url', `http://localhost:3002/api/document-generator/callback`,
        ];
        const generator = (0, child_process_1.spawn)('python', args, {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                PYTHONPATH: path.join(__dirname, '../../scripts'),
            },
        });
        this.activeGenerators.set(documentId, generator);
        // Handle generator output
        generator.stdout.on('data', (data) => {
            console.log(`Generator ${documentId} stdout:`, data.toString());
            this.handleGeneratorOutput(documentId, data.toString());
        });
        generator.stderr.on('data', (data) => {
            console.error(`Generator ${documentId} stderr:`, data.toString());
        });
        generator.on('close', (code) => {
            console.log(`Generator ${documentId} exited with code ${code}`);
            this.activeGenerators.delete(documentId);
            this.handleGeneratorExit(documentId, code);
        });
        generator.on('error', (error) => {
            console.error(`Generator ${documentId} error:`, error);
            this.activeGenerators.delete(documentId);
            this.handleGenerationError(documentId, error);
        });
    }
    /**
     * Handle generator output for progress updates
     */
    async handleGeneratorOutput(documentId, output) {
        try {
            const lines = output.split('\n').filter(line => line.trim());
            for (const line of lines) {
                if (line.startsWith('PROGRESS:')) {
                    const progressData = JSON.parse(line.substring(9));
                    await this.updateGenerationProgress(documentId, progressData);
                }
                else if (line.startsWith('COMPLETED:')) {
                    const completionData = JSON.parse(line.substring(10));
                    await this.handleGenerationCompletion(documentId, completionData);
                }
            }
        }
        catch (error) {
            console.error('Error parsing generator output:', error);
        }
    }
    /**
     * Handle generator process exit
     */
    async handleGeneratorExit(documentId, code) {
        if (code !== 0) {
            await this.handleGenerationError(documentId, new Error(`Generator exited with code ${code}`));
        }
    }
    /**
     * Handle generation errors
     */
    async handleGenerationError(documentId, error) {
        await this.generatedDocumentRepository.updateById(documentId, {
            status: 'failed',
            completedAt: new Date(),
            updatedAt: new Date(),
            errorMessage: error.message || 'Unknown error occurred',
        });
    }
    /**
     * Update generation progress
     */
    async updateGenerationProgress(documentId, progressData) {
        await this.generatedDocumentRepository.updateProgress(documentId, progressData.processedPages, progressData.totalPages, progressData.status);
    }
    /**
     * Handle generation completion
     */
    async handleGenerationCompletion(documentId, completionData) {
        const filePath = completionData.filePath;
        const fileSize = completionData.fileSize;
        const downloadUrl = this.generateDownloadUrl(documentId);
        await this.generatedDocumentRepository.updateById(documentId, {
            status: 'completed',
            filePath,
            fileSize,
            downloadUrl,
            completedAt: new Date(),
            updatedAt: new Date(),
            progressPercentage: 100,
        });
    }
    /**
     * Get document generation progress
     */
    async getGenerationProgress(documentId) {
        const document = await this.generatedDocumentRepository.findById(documentId);
        return {
            documentId,
            status: document.status,
            processedPages: document.processedPages,
            totalPages: document.totalPages,
            errorMessage: document.errorMessage,
            filePath: document.filePath,
        };
    }
    /**
     * Cancel document generation
     */
    async cancelGeneration(documentId) {
        const generator = this.activeGenerators.get(documentId);
        if (generator) {
            generator.kill('SIGTERM');
            this.activeGenerators.delete(documentId);
        }
        await this.generatedDocumentRepository.updateById(documentId, {
            status: 'failed',
            completedAt: new Date(),
            updatedAt: new Date(),
            errorMessage: 'Generation cancelled by user',
        });
    }
    /**
     * Generate filename for document
     */
    generateFilename(format, organizationType) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const prefix = organizationType === 'single_file' ? 'website-content' : 'website-content-collection';
        return `${prefix}-${timestamp}.${format}`;
    }
    /**
     * Generate access token for document download
     */
    generateAccessToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    /**
     * Calculate document expiry date (30 days from now)
     */
    calculateExpiryDate() {
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);
        return expiryDate;
    }
    /**
     * Generate download URL for document
     */
    generateDownloadUrl(documentId) {
        return `/api/documents/${documentId}/download`;
    }
    /**
     * Get active generators count
     */
    getActiveGeneratorsCount() {
        return this.activeGenerators.size;
    }
    /**
     * Get all active generator document IDs
     */
    getActiveGeneratorDocumentIds() {
        return Array.from(this.activeGenerators.keys());
    }
};
exports.DocumentGeneratorService = DocumentGeneratorService;
exports.DocumentGeneratorService = DocumentGeneratorService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.SINGLETON }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.GeneratedDocumentRepository)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.CrawledContentRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.GeneratedDocumentRepository,
        repositories_1.CrawledContentRepository])
], DocumentGeneratorService);
//# sourceMappingURL=document-generator.service.js.map