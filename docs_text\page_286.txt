=== Page 287 ===

287Service Container
The Service Container is an extremely powerful feature of Masonite and should be used
to the fullest extent possible. It's important to understand the concepts of the Service
Container. It's a simple concept but is a bit magical if you don't understand what's going
on under the hood.
The Service Container is just a dictionary where classes are loaded into it by key-value
pairs, and then can be retrieved by either the key or value through resolving objects.
That's it.
Think of "resolving objects" as Mason<PERSON> saying "what does your object need? Ok, I have
them in this dictionary, let me get them for you."
The container holds all of the frameworks classes and features so adding features to
Masonite only entails adding classes into the container to be used by the developer later
on. This typically means "registering" these classes into the container (more about this
later on).
This allows Masonite to be extremely modular.
There are a few objects that are resolved by the container by default. These include your
controller methods (which are the most common and you have probably used them so
far) driver and middleware constructors and any other classes that are specified in the
documentation.
There are three methods that are important in interacting with the container: bind, 
make and resolve
In order to bind classes into the container, we will just need to use a simple bind
method on our app container. In a service provider, that will look like:
Getting Started
Bind6/12/25, 3:02 AM Masonite Documentation