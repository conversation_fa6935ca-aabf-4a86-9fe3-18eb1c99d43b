=== Page 271 ===

271For image or video file type validation prefer the direct image and video validation rules.
This is used to make sure a value is greater than a specific value
Used to make sure that value is a valid image.
Valid image types are defined by all MIME types starting with image/. For more details
you can check mimetypes Python package which gives known MIME types with 
mimetypes.types_map.
Additionally you can check image size as with basic file validatorvalidate.file('document', mimes=['pdf', 'txt'], size='4MB')
"""
{
  'age': 25
}
"""
validate.greater_than('age', 18)
"""
{
  'avatar': '/my/picture.png'
}
"""
validate.image('avatar')
validate.image('avatar', size="2MB")Greater_than
Image
In_range6/12/25, 3:02 AM Masonite Documentation