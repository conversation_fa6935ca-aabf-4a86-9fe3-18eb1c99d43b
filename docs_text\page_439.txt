=== Page 440 ===

440To get this in line for 2.1 you will need to use Container Swapping in order to be able to
resolve this. This is actually an awesome feature.
First go to your provider where you binded it to the container:
and add a container swap right below it by swapping it with a class:
now you can use that class to resolve:
Completely removed the masonite.facades module and put the only class (the Auth
class) in the masonite.auth module.
So all instances of:from app.managers import IntegrationManager
def boot(self):
    self.app.bind('IntegrationManager', 
IntegrationManager.driver('something'))
from app.managers import IntegrationManager
def boot(self):
    self.app.bind('IntegrationManager', 
IntegrationManager.driver('somedriver'))
    self.app.swap(IntegrationManager, 
IntegrationManager.driver('somedriver'))
from app.managers import IntegrationManager
def slack_send(self, manager: IntegrationManager):
    return manager.driver('slack').scopes('incoming-
webhook').state(self.request.param('id')).redirect()
from masonite.facades.Auth import AuthRemoved Masonite Facades6/12/25, 3:02 AM Masonite Documentation