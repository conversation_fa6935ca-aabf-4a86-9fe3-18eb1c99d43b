=== Page 105 ===

105Configuration
Configuration files in Masonite are gathered in one folder named config in default
projects.
Each feature can have some options in its own file named after the feature. For example
you will find mail related options in config/mail.py file.
The Configuration class is responsible for loading all configuration files before the
application starts.
It will load all files located at path defined through config.location binding which
default to config/.
Then values are accessed based on the file they belong to and a dotted path can be used
to access nested options.
Given the following config/mail.py file:
•Accessing mail will return a dictionary with all the options.from masonite.environment import env
FROM_EMAIL = env("MAIL_FROM", "<EMAIL>")
DRIVERS = {
    "default": env("MAIL_DRIVER", "terminal"),
    "smtp": {
        "host": env("MAIL_HOST"),
        "port": env("MAIL_PORT", "587"),
        "username": env("MAIL_USERNAME"),
        "password": env("MAIL_PASSWORD"),
        "from": FROM_EMAIL,
    }
}Getting Started6/12/25, 3:02 AM Masonite Documentation