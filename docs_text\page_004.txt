=== Page 5 ===

5For example, you would run python3 -m venv venv instead of python -m venv venv
First install the Masonite package:
Then start a new project:
This will create a new project in the current directory.
If you want to create the project in a new directory (e.g. my_project) you must provide the
directory name with project start my_project.
Then install Masonite dependencies:
If you have created the project in a new directory you must go to this directory before
running project install.
Once installed you can run the development server:
Congratulations! You’ve setup your first Masonite project! Keep going to learn more about
how to use Masonite to build your applications.$ pip install masonite
$ project start .
$ project install
$ python craft serveInstallation6/12/25, 3:02 AM Masonite Documentation