=== Page 170 ===

170Then in this facade you should declare your typed methods. Here is a partial example of
the Mail facades types hints:
Notice that:
•methods code is not present, but replaced with ....
•methods do not receive self as first argument. Because when calling the facade
we don't really instantiate it (even if we get an instance of the object bound to the
container). This allow to have the correct type hint behaviour.from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from ..mail import Mailable
class Mail:
    """Handle sending e-mails from Masonite via different drivers."""
    def mailable(mailable: "Mailable") -> "Mail":
        ...
    def send(driver: str = None):
        ...6/12/25, 3:02 AM Masonite Documentation