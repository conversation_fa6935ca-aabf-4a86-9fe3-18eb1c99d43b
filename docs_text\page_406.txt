=== Page 407 ===

407Masonite 2.3
Masonite 2.3 brings a lot of code and quality of life improvements. There aren't a lot of
major ground breaking changes and depending on how your application is structured,
may not require much effort to upgrade.
Below is a list of changes that will be in Masonite 2.3:
Some larger changes include all packages for Masonite use SEMVER versioning while
Masonite still using ROMVER as it has been since Masonite started.
Masonite will also not require any packages for you through Masonite requirements and
will instead put the requirements in the requirements.txt file in new projects. This will
allow packages to release new Majors outside of the major upgrades of Masonite. So we
can develop new package improvements faster.
The masonite.testing.TestSuite class has been completely removed. This was a
class that was obsolete and has been replace by the masonite.testing.TestCase
class anyway. The TestSuite class was bascially wrappers around some of the
commands that predated the newer testing features.Preface
Package Changes
Remove TestSuite Class
Removed SassProvider6/12/25, 3:02 AM Masonite Documentation