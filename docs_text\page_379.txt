=== Page 380 ===

380Y<PERSON> can now completely remove fetching of any inputs that Masonite handles internally
such as __token and __method when fetching any inputs. This is also great for building
third party libraries:
Read more in Requests documentation.
Because of the changes to internal framework variables, there are several changes to the
CSRF middleware that comes in every application of Masonite.
Be sure to read the changes in the Upgrade Guide 1.6 to 2.0.
Added a new default package to Masonite that allows scheduling recurring tasks:
Read about Masonite Scheduler under the Task Scheduling documentation.
It's important during development that you have the ability to seed your database with
dummy data. This will improve team development with Masonite to get everyones
database setup accordingly.Request.all(internal_variables=False)
New Argument in Request.all
Made several changes to the CSRF Middleware
Added Scheduler to Masonite
Added Database Seeding Support6/12/25, 3:02 AM Masonite Documentation