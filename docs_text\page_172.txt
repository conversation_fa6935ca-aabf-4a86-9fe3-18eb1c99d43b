=== Page 173 ===

173When uploading files from a form you will find the put_file method more useful:from masonite.filesystem import Storage
from masonite.request import Request
def store(self, storage: Storage, request: Request):
  storage.disk('local').put('errors/info.log', 'errors')
Method Description
exists(file_path) Boolean to check if a file exists.
missing(file_path) Boolean to check if a file does not exist.
streamCreates a FileStream object to manage a
file stream.
copy('/file1.jpg', '/file2,jpg')Copies a file from 1 directory to another
directory
move('/file1.jpg', '/file2,jpg') Moves a file from 1 directory to another
prepend('file.log', 'content') Prepends content to a file
append('file.log', 'content') Appends content to a file
put('file.log', 'content') Puts content to a file
get('file.log') Gets content of a file
put_file('directory', resource, 
"name")Puts a file resource to a directory. Must be an
instance of Masonite's UploadedFile. Takes
an optional third name parameter to specify
the file name
from masonite.filesystem import Storage
from masonite.request import Request
def store(self, storage: Storage, request: Request):
  path = storage.disk('local').put_file('avatars', 
request.input('avatar'))Uploading Form Files6/12/25, 3:02 AM Masonite Documentation