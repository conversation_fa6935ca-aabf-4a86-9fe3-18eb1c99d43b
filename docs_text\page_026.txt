=== Page 27 ===

27Notice here we have a @id string. We can use this to grab that section of the URL in our
controller in the next section below. This is like a route URL capture group.
Let's create a single method so we show a single post.
We use the param() method to fetch the id from the URL. Remember this key was set in
the route above when we specified the @id
For a real application we might do something like @slug and then fetch it with 
request().param('slug').
We just need to display 1 post so lets just put together a simple view:app/controllers/PostController.py
from app.models.Post import Post
from masonite.request import Request
from masonite.views import View
...
def single(self, view: View, request: Request):
    post = Post.find(request.param('id'))
    return view.render('single', {'post': post})
templates/single.html
{{ post.title }}
<br>
{{ post.body }}
<hr>Single Method
Single Post View6/12/25, 3:02 AM Masonite Documentation