=== Page 212 ===

212Masonite makes it really easy to do this by creating a specific package provider that will
register your package resources. The default package layout comes with such a provider
inheriting from PackageProvider class:
configure() method is called in usual register() method and is used to register all
resources used in your package.
root(import_path) method should be called first and is used to specify the import
path of your package. If your package needs to be used like this:
Then super_awesome_package is the import path of your package. If your package is
imported like this:
Then masonite.inertia is the import path of your package.
name(string) method should be called in second and is used to specify the name of
your package (not the PyPi package name neither the Python module name) but the
name that will be used to reference your package in the publish command or in the
resources paths (it should be a name without special characters and spaces i.e. a Python
valid name). This will also be the name of your package configuration file.# providers/SuperAwesomeProvider.py
from masonite.packages import PackageProvider
class SuperAwesomeProvider(PackageProvider):
    def configure(self):
        (
            self.root("super_awesome_package")
            .name("super_awesome")
        )
    def boot(self):
        pass
from super_awesome_package.providers import SuperAwesomeProvider
from masonite.inertia.providers import InertiaProvider6/12/25, 3:02 AM Masonite Documentation