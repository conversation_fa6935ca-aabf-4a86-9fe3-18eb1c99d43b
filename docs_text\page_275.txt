=== Page 276 ===

276Used to make sure the value matches another field value
Used to make sure the value is None
Used to make sure a value is a numeric value"""
{
  'user1': {
    'role': 'admin'
  },
  'user2': {
    'role': 'admin'
  }
}
"""
validate.matches('user1.role', 'user2.role')
"""
{
  'age': 25,
  'active': None
}
"""
validate.none('active')Matches
None
Numeric6/12/25, 3:02 AM Masonite Documentation