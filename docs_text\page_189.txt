=== Page 190 ===

190database channel cannot be used with those notifications because no Notifiable entity is
attached to it.
If you want to send notifications e.g. to your users inside your application, you can define
them as notifiables. Then you can still use Notification class to send notification:
Or you can use a handy notify() method:
ORM Models can be defined as Notifiable to allow notifications to be sent to them.
The most common use case is to set User model as Notifiable as we often need to
send notifications to users.notification.route('mail', '<EMAIL>').route('slack', 
'#general').send(Welcome())
from masonite.notification import NotificationManager
from app.notifications.Welcome import Welcome
class WelcomeController(Controller):
    def welcome(self, notification: NotificationManager):
        user = self.request.user()
        notification.send(user, Welcome())
        # send to all users
        users = User.all()
        notification.send(users, Welcome())
user = self.request.user()
user.notify(Welcome())To notifiables
Using Notifiables6/12/25, 3:02 AM Masonite Documentation