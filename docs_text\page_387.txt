=== Page 388 ===

388Y<PERSON> can now optionally use . instead of / in your views:
We moved the CSRF middleware completely into the core framework and allow
developers to extend from it now. This will allow us to fix any security bugs that are apart
of the CSRF feature.
You may see this pattern a lot in the future which is only extending classes from the core
framework so we can hot fix things much better.
Masonite now has a plethora of docstrings on each and every class by default to really
give the developer an understanding about what each default class is actually doing and
what it is dependent on.
Masonite is also much more PEP 8 compliant. We removed all instances of triple single
quotes: ''' for the more preferred and PEP 8 compliant double quotes """ for
docstrings.
We also cleaned a lot of the classes generated by the auth command since those were
pretty ugly.from masonite.request import Request
def show(self, request: Request):
    request.param('user')
    request.param('id')
def show(self, view: View):
    return view.render('dashboard.user.show')Added ability use dot notation for views
Moved the CsrfMiddleware into core and extended it
Completely cleaned the project6/12/25, 3:02 AM Masonite Documentation