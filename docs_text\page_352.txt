=== Page 353 ===

353data here in the html method loop is the collection we built. You can use this to style
your tab and content.
In the collect method return dictionary, the description is used to show a quick
description at the top of the tabs content. The count here is the number badge shown
in the actual tab itself.
Lastly we just need to register the collector to the debugbar for it to show up. You can do
this in your own provider. If you are building this in your own application you can make
your own provider. If you are building a collector as part of a package you can have your
developers install it in their projects.
And then finally register the provider with your provider config and your collector will now
show up in the debug toolbar with the rest of the collectors.class YourProvider(Provider):
    def __init__(self, application):
        self.application = application
    def register(self):
        debugger = self.application.make('debugger')
        debugger.add_collector(YourCollector())
    def boot(self):
        passRegistering the collector6/12/25, 3:02 AM Masonite Documentation