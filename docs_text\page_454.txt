=== Page 455 ===

455this is no longer required and routes will be found automatically. There is no longer a 
self.routes() method.
The JSON method signature has changed and you now should specify the request
method as the first parameter.
A previous call that looked like this:
should become:
Previously you logged a user in by using the user method but now you can using the 
actingAs method before you call the route:
A method like this:    def setup_method(self):
        super().setup_method()
        self.routes([
            Get().route('/testing', 
'SomeController@show').name('testing.route').middleware('auth', 
'owner')
        ])
self.json('/test/json/response/1', {'id': 1}, method="POST")
self.json('POST', '/test/json/response/1', {'id': 1})JSON method
User6/12/25, 3:02 AM Masonite Documentation