=== Page 112 ===

112Here when raising this exception in your code, Masonite will know that it should render it
by calling the get_response() method and here it will render as a string containing the
message raised.
Note that you can also set an HTTP status code by adding - as for HTTP exceptions - the 
get_status() method to the exception.
Existing Exception Handlers are:
•HTTPExceptionHandler
•DumpExceptionHandler
You can build your own Exception Handlers to override the way Masonite handles an
exception that is thrown or to add new behaviours.class CustomException(Exception):
    def __init__(self, message=""):
        super().__init__(message)
        self.message = message
    def get_response(self):
        return self.message
class CustomException(Exception):
    def __init__(self, message=""):
        super().__init__(message)
        self.message = message
    def get_response(self):
        return self.message
    def get_status(self):
        return 401
Existing Exception Handlers6/12/25, 3:02 AM Masonite Documentation