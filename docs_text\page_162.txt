=== Page 163 ===

163Now any POST routes that are to your-domain.com/oauth/github are not protected by
CSRF and no checks will be made against this route. Use this sparingly as CSRF
protection is crucial to application security but you may find that not all routes need it.
You can also use * wildcards for exempting several routes under the same prefix. For
example you may find yourself needing to do this:
This can get a bit repetitive so you may specify a wildcard instead:from masonite.middleware import VerifyCsrfToken as Middleware
class VerifyCsrfToken(Middleware):
    exempt = ['/oauth/github']
from masonite.middleware import VerifyCsrfToken as Middleware
class VerifyCsrfToken(Middleware):
    exempt = [
        '/api/document/reject-reason',
        '/api/document/*/reject',
        '/api/document/*/approve',
        '/api/document/*/process/@user',
    ]
    ...
from masonite.middleware import VerifyCsrfToken as Middleware
class VerifyCsrfToken(Middleware):
    exempt = [
        '/api/document/*',
    ]
    ...Exempting Multiple Routes6/12/25, 3:02 AM Masonite Documentation