=== Page 174 ===

174The put_file method will return the relative path to the file so you can save it to the
database and fetch it later.
By default, a file name will be auto generated for you using a UUID4 string. You can
specify your own name by using a name parameter:
You do not need to specify the extension in the name as the extension will be pulled from
the resource object.
When uploading images to something like an AWS bucket, you may want to display the
images. You may use a combination of the asset helper and setting a path in your
filesystem config. This mainly just provides a nice interface for combining 2 strings
When using Amazon S3, you will need to set your bucket permissions and policies
appropriately.
First, set a path in your filesystem config:storage.disk('local').put_file('avatars', request.input('avatar'), 
name="user1")
DISKS = {
    "default": "local",
    # "..",
    "s3": {
        "driver": "s3",
        # "..",
          "path": "https://bucket.s3.us-east-2.amazonaws.com"
    },
}Asset Helper
Displaying Files6/12/25, 3:02 AM Masonite Documentation