=== Page 233 ===

233Then you should define Redis as default driver and configure it with your Redis server
parameters:
Finally ensure that the Redis server is running and you're ready to start using sessions.
To save session data you can simply "set" data into the session:
Flash data is data specific to the next request. This is useful for data such as error
messages or alerts:DRIVERS = {
    "default": "redis",
    "redis": {
        "host": "127.0.0.1",
        "port": 6379,
        "password": "",
        "options": {"db": 1},  # redis module driver specific options
        "timeout": 60 * 60,
        "namespace": "masonite4",
    },
}
from masonite.sessions import Session
def store(self, session: Session):
  data = session.set('key', 'value')
from masonite.sessions import Session
def store(self, session: Session):
  data = session.flash('key', 'value')Saving Session Data
Flashing Data6/12/25, 3:02 AM Masonite Documentation