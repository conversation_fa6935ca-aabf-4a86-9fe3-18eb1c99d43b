=== Page 38 ===

38Code comments should be left to a MINIMUM. If your code is complex to the point that it
requires a comment then you should consider refactoring your code instead of adding a
comment. If you're code MUST be complex enough that future developers may not
understand it, add a # comment above it
For normal code this will look something like:
Please read this process carefully to prevent pull requests from being rejected.
1.You should open an issue before making any pull requests. Not all features will be
added to the framework and some may be better off as a third party package or not
be added at all. It wouldn't be good if you worked on a feature for several days and
the pull request gets rejected for reasons that could have been discussed in an issue
for several minutes.
2.Ensure any changes are well commented and any configuration files that are added
have docstring comments on the variables it's setting. See the comments section
above.
3.Update the MasoniteFramework/docs repo (and the README.md inside 
MasoniteFramework/masonite repo if applicable) with details of changes to the UI.
This includes new environment variables, new file locations, container parameters,
new feature explanations etc.
4.Name your branches in the form of <feature|fix>/<issue-number>. For example
if you are doing a bug fix and the issue number is 576 then name your branch 
fix/576. This will help us locate the branches on our computers at later dates. If it
is a new feature name it feature/576.# This code performs a complex task that may not be understood later on
# You can add a second line like this
complex_code = 'value'
perform_some_complex_task()Code Comments
Pull Request Process6/12/25, 3:02 AM Masonite Documentation