=== Page 378 ===

378An incredible new feature is autoloading support. You can now list directories in the new 
AUTOLOAD constant in your config/application.py file and it will automatically load
all classes into the container. This is great for loading command and models into the
container when the server starts up.
You can also use this class as a standalone class in your own service providers.
Read more in Autoloading documentation.
Updated all libraries to the latest version with the exception of the Pendulum library which
latest version is a breaking change and therefore was left out. The breaking change
would not be worth it to add the complexity of upgrading so you may upgrade on a per
project basis.
Previously you had to import classes like:
Now you can simply specify:
Because of this change we no longer need the same duplicated class names in the
PROVIDERS list either.
Read more about changing duplicated class names under the Duplicate Class Names
documentation.
from masonite.drivers.UploadDriver import UploadDriver
from masonite.drivers import UploadDriver
Updated Libraries
Removed Importing Duplicate Class Names6/12/25, 3:02 AM Masonite Documentation