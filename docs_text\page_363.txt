=== Page 364 ===

364Templates may have a lot of logic that are only updated every few minutes or even every
few months. With template caching you can now cache your templates anywhere from
every few seconds to every few years. This is an extremely powerful caching technique
that will allow your servers to run less intensively and easily increase the performance of
your application.
If a page gets hit 100 times every second then you can cache for 5, 10 or 15 seconds at a
time to lessen the load on your server.
This feature only activates if you have the CacheProvider loaded in your PROVIDERS
list. If you try to use these features without that provider then you will be hit with a 
RequiredContainerBindingNotFound exception letting you know you are missing a
required binding from a service provider. This provider comes out of the box in Masonite
1.4.
We have also updated the code to closely conform to PEP 8 standards.
Because of the caching features, we have added a bootstrap/cache folder where all
caching will be put but you can change this in the new config/cache.py file.
Masonite 1.4 brings the idea of contracts which are very similar to interfaces in other
languages. Contracts ensure that a driver or manager inherits has the same functionality
across all classes of the same type.PEP 8 Standards
Added a New Folder and Configuration File
Added Contracts
Added CSRF Protection6/12/25, 3:02 AM Masonite Documentation