=== Page 433 ===

433Some variable internals have changed to prepend a double underscore to them to better
symbolize they are being handled internally. Because of this we need to change any
instances of csrf_token to __token in the CSRF Middleware file.
You can check for what the class should look like from the MasoniteFramework/masonite
repository
Masonite 2 comes with a new autoloader. This can load all classes in any directory you
specify right into the Service Container when the server first starts. This is incredibly
useful for loading your models, commands or tasks right into the container.
Simply add a new AUTOLOAD constant in your config/application.py file. This is the
entire section of the autoload configuration.def show(self):
    return request().redirect('/dashboard/@id', {'id': league.id})
CSRF Middleware
Autoloading
config/application.py6/12/25, 3:02 AM Masonite Documentation