=== Page 85 ===

85Y<PERSON> can get the request class:
You can get the location of static assets:
You can create a path to an asset by using the asset helper:
this will render a URL like this:
See your filesystems.py configuration for how how to set the paths up.
You can create a CSRF token hidden field to be used with forms:
You can get only the token that generates. This is useful for JS frontends where you need
to pass a CSRF token to the backend for an AJAX call<p> Path: {{ request().path }} </p>
...
<img src="{{ asset('s3', 'profile.jpg') }}" alt="profile">
...
<img src="https://s3.us-east-2.amazonaws.com/bucket/profile.jpg" 
alt="profile">
<form action="/some/url" method="POST">
    {{ csrf_field }}
    <input ..>
</form>Asset
CSRF Field
CSRF Token6/12/25, 3:02 AM Masonite Documentation