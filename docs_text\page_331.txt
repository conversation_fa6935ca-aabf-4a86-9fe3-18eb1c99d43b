=== Page 332 ===

332Database Tests
By default, your tests are are not ran in isolation from a database point of view. It means
that your local database will be modified any time you run your tests and won't be
rollbacked at the end of the tests. While this behaviour might be fine in most case you
can learn below how to configure your tests cases to reset the database after each test.
If you want to have a clean database for each test you must subclass the TestCase
class with DatabaseTransactions class. Then all your tests will run inside a transaction
so any data you create will only exist within the lifecycle of the test. Once the test
completes, your database is rolled back to its previous state. This is a perfect way to
prevent test data from clogging up your database.
Note that you can define the connection that will be used during testing. This will allow
you to select a different database that will be used for testing. Here is a standard
exemple of database configuration file that you can use.from masonite.tests import TestCase, DatabaseTransactions
class TestSomething(TestCase, DatabaseTransactions):
  connection = "testing"
  def test_can_create_user(self):
      User.create({"name": "john", "email": "john6", "password": 
"secret"})Resetting The Database After Each Test6/12/25, 3:02 AM Masonite Documentation