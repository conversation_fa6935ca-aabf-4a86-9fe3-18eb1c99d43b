=== Page 100 ===

100Environments
Environment variables in Masonite are defined in a .env file and should contain all
environment variables needed for your project.
You can have multiple environment files that are loaded when the server first starts. It is
often helpful to have different variable values depending on the environment where the
application is running (locally, during tests or on a production server).
Also it might be useful to have more global environment variables that can be shared
across your team for 3rd party services like Stripe or Mailgun and then have more
developer specific values like database connections or different storage drivers for
development.
We'll walk through how to configure your environments in this documentation.
Environment variables should be set on a project per project basis inside your .env file.
Never commit any of your .env files into source control ! It would be a security risk in the
event someone gained access to your repository since sensitive credentials and data would
get exposed.
That is why .env and .env.* are in the project .gitignore file by default, so you
should not worry about accidentally committing those files to source control.
In a fresh Masonite installation, a .env.example file located at project root directory will
define minimum and common configuration values for a Masonite application. During the
installation process, this file will be copied to .env file.
Security
Getting Started6/12/25, 3:02 AM Masonite Documentation