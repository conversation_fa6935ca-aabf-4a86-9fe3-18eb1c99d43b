{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/payment.service\";\nimport * as i3 from \"../../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/divider\";\nfunction PaymentTestComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PaymentTestComponent_button_19_Template_button_click_0_listener() {\n      const testAmount_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectTestAmount(testAmount_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const testAmount_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"color\", ((tmp_2_0 = ctx_r2.paymentForm.get(\"currency\")) == null ? null : tmp_2_0.value) === testAmount_r2.currency ? \"primary\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", testAmount_r2.label, \" \");\n  }\n}\nfunction PaymentTestComponent_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", currency_r4, \" \");\n  }\n}\nfunction PaymentTestComponent_mat_hint_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getEquivalentAmount());\n  }\n}\nfunction PaymentTestComponent_mat_spinner_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 29);\n  }\n}\nfunction PaymentTestComponent_mat_icon_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"lock\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentTestComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Pay Securely with Razorpay\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentTestComponent_mat_card_57_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"div\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"div\", 41)(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const payment_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatCurrency(payment_r5.amount, payment_r5.currency), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(payment_r5.description || \"Test Payment\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, payment_r5.createdAt, \"medium\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", payment_r5.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + payment_r5.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusIcon(payment_r5.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 10, payment_r5.status), \" \");\n  }\n}\nfunction PaymentTestComponent_mat_card_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 30)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Payment History \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7, \" Your recent test transactions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 31);\n    i0.ɵɵtemplate(10, PaymentTestComponent_mat_card_57_div_10_Template, 18, 12, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.payments);\n  }\n}\nexport let PaymentTestComponent = /*#__PURE__*/(() => {\n  class PaymentTestComponent {\n    constructor(formBuilder, paymentService, authService, snackBar) {\n      this.formBuilder = formBuilder;\n      this.paymentService = paymentService;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.payments = [];\n      this.supportedCurrencies = environment.razorpay.supportedCurrencies;\n      this.testAmounts = [{\n        amount: 100,\n        currency: 'INR',\n        label: '₹100 - Basic Test'\n      }, {\n        amount: 500,\n        currency: 'INR',\n        label: '₹500 - Standard Test'\n      }, {\n        amount: 1000,\n        currency: 'INR',\n        label: '₹1,000 - Premium Test'\n      }, {\n        amount: 5,\n        currency: 'USD',\n        label: '$5 - Basic Test'\n      }, {\n        amount: 25,\n        currency: 'USD',\n        label: '$25 - Standard Test'\n      }, {\n        amount: 50,\n        currency: 'USD',\n        label: '$50 - Premium Test'\n      }];\n      this.paymentForm = this.formBuilder.group({\n        amount: ['', [Validators.required, Validators.min(1), Validators.max(1000000)]],\n        currency: [environment.razorpay.currency, [Validators.required]],\n        description: ['Payment test transaction']\n      });\n    }\n    ngOnInit() {\n      this.loadPaymentHistory();\n    }\n    onSubmit() {\n      if (this.paymentForm.invalid) {\n        this.markFormGroupTouched(this.paymentForm);\n        return;\n      }\n      const paymentRequest = this.paymentForm.value;\n      const validation = this.paymentService.validateAmount(paymentRequest.amount, paymentRequest.currency);\n      if (!validation.valid) {\n        this.snackBar.open(validation.error, 'Close', {\n          duration: 5000\n        });\n        return;\n      }\n      this.loading = true;\n      this.paymentService.processPayment(paymentRequest).subscribe({\n        next: response => {\n          if (response.success) {\n            this.snackBar.open('Payment completed successfully!', 'Close', {\n              duration: 5000\n            });\n            this.loadPaymentHistory();\n            this.paymentForm.patchValue({\n              amount: '',\n              description: 'Payment test transaction'\n            });\n          } else {\n            this.snackBar.open(response.message || 'Payment failed', 'Close', {\n              duration: 5000\n            });\n          }\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Payment processing failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    selectTestAmount(testAmount) {\n      this.paymentForm.patchValue({\n        amount: testAmount.amount,\n        currency: testAmount.currency,\n        description: `Test payment - ${testAmount.label}`\n      });\n    }\n    loadPaymentHistory() {\n      this.paymentService.getMyPayments().subscribe({\n        next: response => {\n          this.payments = response.payments;\n        },\n        error: error => {\n          console.error('Failed to load payment history:', error);\n        }\n      });\n    }\n    formatCurrency(amount, currency) {\n      return this.paymentService.formatCurrency(amount, currency);\n    }\n    getStatusIcon(status) {\n      return this.paymentService.getStatusIcon(status);\n    }\n    getFieldError(fieldName) {\n      const field = this.paymentForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['min']) return `Minimum amount is ${field.errors['min'].min}`;\n        if (field.errors['max']) return `Maximum amount is ${field.errors['max'].max}`;\n      }\n      return '';\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    convertAmount(amount, fromCurrency, toCurrency) {\n      return this.paymentService.convertCurrency(amount, fromCurrency, toCurrency);\n    }\n    getEquivalentAmount() {\n      const amount = this.paymentForm.get('amount')?.value;\n      const currency = this.paymentForm.get('currency')?.value;\n      if (!amount || !currency) return '';\n      const otherCurrency = currency === 'INR' ? 'USD' : 'INR';\n      const convertedAmount = this.convertAmount(amount, currency, otherCurrency);\n      return `≈ ${this.formatCurrency(convertedAmount, otherCurrency)}`;\n    }\n    static #_ = this.ɵfac = function PaymentTestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaymentTestComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PaymentService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentTestComponent,\n      selectors: [[\"app-payment-test\"]],\n      decls: 84,\n      vars: 11,\n      consts: [[1, \"payment-container\"], [1, \"container\"], [1, \"subtitle\"], [1, \"payment-card\"], [1, \"test-amounts\"], [1, \"amount-buttons\"], [\"mat-stroked-button\", \"\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-3\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"currency-field\"], [\"formControlName\", \"currency\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"amount-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"amount\", \"min\", \"1\", \"step\", \"0.01\"], [4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"maxlength\", \"100\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"payment-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"security-notice\"], [\"class\", \"payment-history\", 4, \"ngIf\"], [1, \"testing-guide\"], [1, \"guide-section\"], [1, \"test-cards\"], [1, \"test-card\"], [\"mat-stroked-button\", \"\", 3, \"click\", \"color\"], [3, \"value\"], [\"diameter\", \"20\"], [1, \"payment-history\"], [1, \"payment-list\"], [\"class\", \"payment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"payment-item\"], [1, \"payment-info\"], [1, \"payment-amount\"], [1, \"payment-details\"], [1, \"payment-description\"], [1, \"payment-date\"], [1, \"payment-id\"], [1, \"payment-actions\"], [1, \"payment-status\", 3, \"ngClass\"]],\n      template: function PaymentTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Payment Testing Interface\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \"Test Razorpay integration with secure payment processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-card\", 3)(7, \"mat-card-header\")(8, \"mat-card-title\")(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"payment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Make a Test Payment \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-card-subtitle\");\n          i0.ɵɵtext(13, \" Test payments using Razorpay's secure checkout \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"div\", 4)(16, \"h3\");\n          i0.ɵɵtext(17, \"Quick Test Amounts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 5);\n          i0.ɵɵtemplate(19, PaymentTestComponent_button_19_Template, 2, 2, \"button\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"mat-divider\", 7);\n          i0.ɵɵelementStart(21, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function PaymentTestComponent_Template_form_ngSubmit_21_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"mat-form-field\", 10)(24, \"mat-label\");\n          i0.ɵɵtext(25, \"Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-select\", 11);\n          i0.ɵɵtemplate(27, PaymentTestComponent_mat_option_27_Template, 2, 2, \"mat-option\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-icon\", 13);\n          i0.ɵɵtext(29, \"attach_money\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 14)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 15);\n          i0.ɵɵelementStart(34, \"span\", 13);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"mat-error\");\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, PaymentTestComponent_mat_hint_38_Template, 2, 1, \"mat-hint\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 17)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Description (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 18);\n          i0.ɵɵelementStart(43, \"mat-icon\", 13);\n          i0.ɵɵtext(44, \"description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"button\", 19);\n          i0.ɵɵtemplate(46, PaymentTestComponent_mat_spinner_46_Template, 1, 0, \"mat-spinner\", 20)(47, PaymentTestComponent_mat_icon_47_Template, 2, 0, \"mat-icon\", 16)(48, PaymentTestComponent_span_48_Template, 2, 0, \"span\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 21)(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\")(53, \"strong\");\n          i0.ɵɵtext(54, \"Secure Payment Processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56, \"All payments are processed securely through Razorpay's PCI DSS compliant platform.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(57, PaymentTestComponent_mat_card_57_Template, 11, 1, \"mat-card\", 22);\n          i0.ɵɵelementStart(58, \"mat-card\", 23)(59, \"mat-card-header\")(60, \"mat-card-title\")(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Testing Guide \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 24)(66, \"h4\");\n          i0.ɵɵtext(67, \"Test Card Numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 25)(69, \"div\", 26)(70, \"strong\");\n          i0.ɵɵtext(71, \"Visa:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(72, \" ************** 1111 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 26)(74, \"strong\");\n          i0.ɵɵtext(75, \"Mastercard:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(76, \" ************** 4444 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"p\")(78, \"strong\");\n          i0.ɵɵtext(79, \"CVV:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" Any 3-4 digit number | \");\n          i0.ɵɵelementStart(81, \"strong\");\n          i0.ɵɵtext(82, \"Expiry:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(83, \" Any future date\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_3_0;\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngForOf\", ctx.testAmounts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.paymentForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.supportedCurrencies);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(((tmp_3_0 = ctx.paymentForm.get(\"currency\")) == null ? null : tmp_3_0.value) === \"INR\" ? \"\\u20B9\" : \"$\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"amount\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getEquivalentAmount());\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.payments.length > 0);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatHint, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i10.MatIcon, i11.MatSelect, i11.MatOption, i12.MatProgressSpinner, i13.MatDivider, i5.TitleCasePipe, i5.DatePipe],\n      styles: [\"html[_ngcontent-%COMP%]{--mat-sys-on-surface: initial}.mat-app-background[_ngcontent-%COMP%]{background-color:var(--mat-app-background-color, var(--mat-sys-background, transparent));color:var(--mat-app-text-color, var(--mat-sys-on-background, inherit))}.mat-elevation-z0[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z0[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-0, 0px 0px 0px 0px --mat-sys-shadow, 0px 0px 0px 0px --mat-sys-shadow, 0px 0px 0px 0px --mat-sys-shadow)}.mat-elevation-z1[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z1[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-1, 0px 2px 1px -1px --mat-sys-shadow, 0px 1px 1px 0px --mat-sys-shadow, 0px 1px 3px 0px --mat-sys-shadow)}.mat-elevation-z2[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z2[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-2, 0px 3px 1px -2px --mat-sys-shadow, 0px 2px 2px 0px --mat-sys-shadow, 0px 1px 5px 0px --mat-sys-shadow)}.mat-elevation-z3[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z3[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-3, 0px 3px 3px -2px --mat-sys-shadow, 0px 3px 4px 0px --mat-sys-shadow, 0px 1px 8px 0px --mat-sys-shadow)}.mat-elevation-z4[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z4[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-4, 0px 2px 4px -1px --mat-sys-shadow, 0px 4px 5px 0px --mat-sys-shadow, 0px 1px 10px 0px --mat-sys-shadow)}.mat-elevation-z5[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z5[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-5, 0px 3px 5px -1px --mat-sys-shadow, 0px 5px 8px 0px --mat-sys-shadow, 0px 1px 14px 0px --mat-sys-shadow)}.mat-elevation-z6[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z6[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-6, 0px 3px 5px -1px --mat-sys-shadow, 0px 6px 10px 0px --mat-sys-shadow, 0px 1px 18px 0px --mat-sys-shadow)}.mat-elevation-z7[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z7[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-7, 0px 4px 5px -2px --mat-sys-shadow, 0px 7px 10px 1px --mat-sys-shadow, 0px 2px 16px 1px --mat-sys-shadow)}.mat-elevation-z8[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z8[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-8, 0px 5px 5px -3px --mat-sys-shadow, 0px 8px 10px 1px --mat-sys-shadow, 0px 3px 14px 2px --mat-sys-shadow)}.mat-elevation-z9[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z9[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-9, 0px 5px 6px -3px --mat-sys-shadow, 0px 9px 12px 1px --mat-sys-shadow, 0px 3px 16px 2px --mat-sys-shadow)}.mat-elevation-z10[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z10[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-10, 0px 6px 6px -3px --mat-sys-shadow, 0px 10px 14px 1px --mat-sys-shadow, 0px 4px 18px 3px --mat-sys-shadow)}.mat-elevation-z11[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z11[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-11, 0px 6px 7px -4px --mat-sys-shadow, 0px 11px 15px 1px --mat-sys-shadow, 0px 4px 20px 3px --mat-sys-shadow)}.mat-elevation-z12[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z12[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-12, 0px 7px 8px -4px --mat-sys-shadow, 0px 12px 17px 2px --mat-sys-shadow, 0px 5px 22px 4px --mat-sys-shadow)}.mat-elevation-z13[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z13[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-13, 0px 7px 8px -4px --mat-sys-shadow, 0px 13px 19px 2px --mat-sys-shadow, 0px 5px 24px 4px --mat-sys-shadow)}.mat-elevation-z14[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z14[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-14, 0px 7px 9px -4px --mat-sys-shadow, 0px 14px 21px 2px --mat-sys-shadow, 0px 5px 26px 4px --mat-sys-shadow)}.mat-elevation-z15[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z15[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-15, 0px 8px 9px -5px --mat-sys-shadow, 0px 15px 22px 2px --mat-sys-shadow, 0px 6px 28px 5px --mat-sys-shadow)}.mat-elevation-z16[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z16[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-16, 0px 8px 10px -5px --mat-sys-shadow, 0px 16px 24px 2px --mat-sys-shadow, 0px 6px 30px 5px --mat-sys-shadow)}.mat-elevation-z17[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z17[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-17, 0px 8px 11px -5px --mat-sys-shadow, 0px 17px 26px 2px --mat-sys-shadow, 0px 6px 32px 5px --mat-sys-shadow)}.mat-elevation-z18[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z18[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-18, 0px 9px 11px -5px --mat-sys-shadow, 0px 18px 28px 2px --mat-sys-shadow, 0px 7px 34px 6px --mat-sys-shadow)}.mat-elevation-z19[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z19[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-19, 0px 9px 12px -6px --mat-sys-shadow, 0px 19px 29px 2px --mat-sys-shadow, 0px 7px 36px 6px --mat-sys-shadow)}.mat-elevation-z20[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z20[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-20, 0px 10px 13px -6px --mat-sys-shadow, 0px 20px 31px 3px --mat-sys-shadow, 0px 8px 38px 7px --mat-sys-shadow)}.mat-elevation-z21[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z21[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-21, 0px 10px 13px -6px --mat-sys-shadow, 0px 21px 33px 3px --mat-sys-shadow, 0px 8px 40px 7px --mat-sys-shadow)}.mat-elevation-z22[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z22[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-22, 0px 10px 14px -6px --mat-sys-shadow, 0px 22px 35px 3px --mat-sys-shadow, 0px 8px 42px 7px --mat-sys-shadow)}.mat-elevation-z23[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z23[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-23, 0px 11px 14px -7px --mat-sys-shadow, 0px 23px 36px 3px --mat-sys-shadow, 0px 9px 44px 8px --mat-sys-shadow)}.mat-elevation-z24[_ngcontent-%COMP%], .mat-mdc-elevation-specific.mat-elevation-z24[_ngcontent-%COMP%]{box-shadow:var(--mat-app-elevation-shadow-level-24, 0px 11px 15px -7px --mat-sys-shadow, 0px 24px 38px 3px --mat-sys-shadow, 0px 9px 46px 8px --mat-sys-shadow)}html[_ngcontent-%COMP%]{--mat-ripple-color: rgba(0, 0, 0, .1)}html[_ngcontent-%COMP%]{--mat-option-selected-state-label-text-color: #3f51b5;--mat-option-label-text-color: rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color: rgba(0, 0, 0, .04)}.mat-accent[_ngcontent-%COMP%]{--mat-option-selected-state-label-text-color: #ff4081;--mat-option-label-text-color: rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color: rgba(0, 0, 0, .04)}.mat-warn[_ngcontent-%COMP%]{--mat-option-selected-state-label-text-color: #f44336;--mat-option-label-text-color: rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color: rgba(0, 0, 0, .04)}html[_ngcontent-%COMP%]{--mat-optgroup-label-text-color: rgba(0, 0, 0, .87)}html[_ngcontent-%COMP%]{--mat-pseudo-checkbox-full-selected-icon-color: #ff4081;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, .54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: #b0b0b0;--mat-pseudo-checkbox-full-disabled-selected-icon-color: #b0b0b0;--mat-pseudo-checkbox-minimal-selected-checkmark-color: #ff4081;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: #b0b0b0}.mat-primary[_ngcontent-%COMP%]{--mat-pseudo-checkbox-full-selected-icon-color: #3f51b5;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, .54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: #b0b0b0;--mat-pseudo-checkbox-full-disabled-selected-icon-color: #b0b0b0;--mat-pseudo-checkbox-minimal-selected-checkmark-color: #3f51b5;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: #b0b0b0}.mat-accent[_ngcontent-%COMP%]{--mat-pseudo-checkbox-full-selected-icon-color: #ff4081;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, .54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: #b0b0b0;--mat-pseudo-checkbox-full-disabled-selected-icon-color: #b0b0b0;--mat-pseudo-checkbox-minimal-selected-checkmark-color: #ff4081;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: #b0b0b0}.mat-warn[_ngcontent-%COMP%]{--mat-pseudo-checkbox-full-selected-icon-color: #f44336;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, .54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: #b0b0b0;--mat-pseudo-checkbox-full-disabled-selected-icon-color: #b0b0b0;--mat-pseudo-checkbox-minimal-selected-checkmark-color: #f44336;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: #b0b0b0}html[_ngcontent-%COMP%]{--mat-app-background-color: #fafafa;--mat-app-text-color: rgba(0, 0, 0, .87);--mat-app-elevation-shadow-level-0: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-1: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-2: 0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-3: 0px 3px 3px -2px rgba(0, 0, 0, .2), 0px 3px 4px 0px rgba(0, 0, 0, .14), 0px 1px 8px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-4: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-5: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 5px 8px 0px rgba(0, 0, 0, .14), 0px 1px 14px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-6: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-7: 0px 4px 5px -2px rgba(0, 0, 0, .2), 0px 7px 10px 1px rgba(0, 0, 0, .14), 0px 2px 16px 1px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-8: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-9: 0px 5px 6px -3px rgba(0, 0, 0, .2), 0px 9px 12px 1px rgba(0, 0, 0, .14), 0px 3px 16px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-10: 0px 6px 6px -3px rgba(0, 0, 0, .2), 0px 10px 14px 1px rgba(0, 0, 0, .14), 0px 4px 18px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-11: 0px 6px 7px -4px rgba(0, 0, 0, .2), 0px 11px 15px 1px rgba(0, 0, 0, .14), 0px 4px 20px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-12: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-13: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 13px 19px 2px rgba(0, 0, 0, .14), 0px 5px 24px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-14: 0px 7px 9px -4px rgba(0, 0, 0, .2), 0px 14px 21px 2px rgba(0, 0, 0, .14), 0px 5px 26px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-15: 0px 8px 9px -5px rgba(0, 0, 0, .2), 0px 15px 22px 2px rgba(0, 0, 0, .14), 0px 6px 28px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-16: 0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-17: 0px 8px 11px -5px rgba(0, 0, 0, .2), 0px 17px 26px 2px rgba(0, 0, 0, .14), 0px 6px 32px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-18: 0px 9px 11px -5px rgba(0, 0, 0, .2), 0px 18px 28px 2px rgba(0, 0, 0, .14), 0px 7px 34px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-19: 0px 9px 12px -6px rgba(0, 0, 0, .2), 0px 19px 29px 2px rgba(0, 0, 0, .14), 0px 7px 36px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-20: 0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 20px 31px 3px rgba(0, 0, 0, .14), 0px 8px 38px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-21: 0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 21px 33px 3px rgba(0, 0, 0, .14), 0px 8px 40px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-22: 0px 10px 14px -6px rgba(0, 0, 0, .2), 0px 22px 35px 3px rgba(0, 0, 0, .14), 0px 8px 42px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-23: 0px 11px 14px -7px rgba(0, 0, 0, .2), 0px 23px 36px 3px rgba(0, 0, 0, .14), 0px 9px 44px 8px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-24: 0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-option-label-text-font: Roboto, sans-serif;--mat-option-label-text-line-height: 24px;--mat-option-label-text-size: 16px;--mat-option-label-text-tracking: .03125em;--mat-option-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-optgroup-label-text-font: Roboto, sans-serif;--mat-optgroup-label-text-line-height: 24px;--mat-optgroup-label-text-size: 16px;--mat-optgroup-label-text-tracking: .03125em;--mat-optgroup-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-card-elevated-container-shape: 4px;--mat-card-outlined-container-shape: 4px;--mat-card-filled-container-shape: 4px;--mat-card-outlined-outline-width: 1px}html[_ngcontent-%COMP%]{--mat-card-elevated-container-color: white;--mat-card-elevated-container-elevation: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-card-outlined-container-color: white;--mat-card-outlined-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-card-outlined-outline-color: rgba(0, 0, 0, .12);--mat-card-subtitle-text-color: rgba(0, 0, 0, .54);--mat-card-filled-container-color: white;--mat-card-filled-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-card-title-text-font: Roboto, sans-serif;--mat-card-title-text-line-height: 32px;--mat-card-title-text-size: 20px;--mat-card-title-text-tracking: .0125em;--mat-card-title-text-weight: 500;--mat-card-subtitle-text-font: Roboto, sans-serif;--mat-card-subtitle-text-line-height: 22px;--mat-card-subtitle-text-size: 14px;--mat-card-subtitle-text-tracking: .0071428571em;--mat-card-subtitle-text-weight: 500}html[_ngcontent-%COMP%]{--mat-progress-bar-active-indicator-height: 4px;--mat-progress-bar-track-height: 4px;--mat-progress-bar-track-shape: 0}.mat-mdc-progress-bar[_ngcontent-%COMP%]{--mat-progress-bar-active-indicator-color: #3f51b5;--mat-progress-bar-track-color: rgba(63, 81, 181, .25)}.mat-mdc-progress-bar.mat-accent[_ngcontent-%COMP%]{--mat-progress-bar-active-indicator-color: #ff4081;--mat-progress-bar-track-color: rgba(255, 64, 129, .25)}.mat-mdc-progress-bar.mat-warn[_ngcontent-%COMP%]{--mat-progress-bar-active-indicator-color: #f44336;--mat-progress-bar-track-color: rgba(244, 67, 54, .25)}html[_ngcontent-%COMP%]{--mat-tooltip-container-shape: 4px;--mat-tooltip-supporting-text-line-height: 16px}html[_ngcontent-%COMP%]{--mat-tooltip-container-color: #616161;--mat-tooltip-supporting-text-color: #fff}html[_ngcontent-%COMP%]{--mat-tooltip-supporting-text-font: Roboto, sans-serif;--mat-tooltip-supporting-text-size: 12px;--mat-tooltip-supporting-text-weight: 400;--mat-tooltip-supporting-text-tracking: .0333333333em}html[_ngcontent-%COMP%]{--mat-form-field-filled-active-indicator-height: 1px;--mat-form-field-filled-focus-active-indicator-height: 2px;--mat-form-field-filled-container-shape: 4px;--mat-form-field-outlined-outline-width: 1px;--mat-form-field-outlined-focus-outline-width: 2px;--mat-form-field-outlined-container-shape: 4px}html[_ngcontent-%COMP%]{--mat-form-field-focus-select-arrow-color: rgba(63, 81, 181, .87);--mat-form-field-filled-caret-color: #3f51b5;--mat-form-field-filled-focus-active-indicator-color: #3f51b5;--mat-form-field-filled-focus-label-text-color: rgba(63, 81, 181, .87);--mat-form-field-outlined-caret-color: #3f51b5;--mat-form-field-outlined-focus-outline-color: #3f51b5;--mat-form-field-outlined-focus-label-text-color: rgba(63, 81, 181, .87);--mat-form-field-disabled-input-text-placeholder-color: rgba(0, 0, 0, .38);--mat-form-field-state-layer-color: rgba(0, 0, 0, .87);--mat-form-field-error-text-color: #f44336;--mat-form-field-select-option-text-color: inherit;--mat-form-field-select-disabled-option-text-color: GrayText;--mat-form-field-leading-icon-color: unset;--mat-form-field-disabled-leading-icon-color: unset;--mat-form-field-trailing-icon-color: unset;--mat-form-field-disabled-trailing-icon-color: unset;--mat-form-field-error-focus-trailing-icon-color: unset;--mat-form-field-error-hover-trailing-icon-color: unset;--mat-form-field-error-trailing-icon-color: unset;--mat-form-field-enabled-select-arrow-color: rgba(0, 0, 0, .54);--mat-form-field-disabled-select-arrow-color: rgba(0, 0, 0, .38);--mat-form-field-hover-state-layer-opacity: .04;--mat-form-field-focus-state-layer-opacity: .08;--mat-form-field-filled-container-color: rgb(244.8, 244.8, 244.8);--mat-form-field-filled-disabled-container-color: rgb(249.9, 249.9, 249.9);--mat-form-field-filled-label-text-color: rgba(0, 0, 0, .6);--mat-form-field-filled-hover-label-text-color: rgba(0, 0, 0, .6);--mat-form-field-filled-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-form-field-filled-input-text-color: rgba(0, 0, 0, .87);--mat-form-field-filled-disabled-input-text-color: rgba(0, 0, 0, .38);--mat-form-field-filled-input-text-placeholder-color: rgba(0, 0, 0, .6);--mat-form-field-filled-error-hover-label-text-color: #f44336;--mat-form-field-filled-error-focus-label-text-color: #f44336;--mat-form-field-filled-error-label-text-color: #f44336;--mat-form-field-filled-error-caret-color: #f44336;--mat-form-field-filled-active-indicator-color: rgba(0, 0, 0, .42);--mat-form-field-filled-disabled-active-indicator-color: rgba(0, 0, 0, .06);--mat-form-field-filled-hover-active-indicator-color: rgba(0, 0, 0, .87);--mat-form-field-filled-error-active-indicator-color: #f44336;--mat-form-field-filled-error-focus-active-indicator-color: #f44336;--mat-form-field-filled-error-hover-active-indicator-color: #f44336;--mat-form-field-outlined-label-text-color: rgba(0, 0, 0, .6);--mat-form-field-outlined-hover-label-text-color: rgba(0, 0, 0, .6);--mat-form-field-outlined-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-form-field-outlined-input-text-color: rgba(0, 0, 0, .87);--mat-form-field-outlined-disabled-input-text-color: rgba(0, 0, 0, .38);--mat-form-field-outlined-input-text-placeholder-color: rgba(0, 0, 0, .6);--mat-form-field-outlined-error-caret-color: #f44336;--mat-form-field-outlined-error-focus-label-text-color: #f44336;--mat-form-field-outlined-error-label-text-color: #f44336;--mat-form-field-outlined-error-hover-label-text-color: #f44336;--mat-form-field-outlined-outline-color: rgba(0, 0, 0, .38);--mat-form-field-outlined-disabled-outline-color: rgba(0, 0, 0, .06);--mat-form-field-outlined-hover-outline-color: rgba(0, 0, 0, .87);--mat-form-field-outlined-error-focus-outline-color: #f44336;--mat-form-field-outlined-error-hover-outline-color: #f44336;--mat-form-field-outlined-error-outline-color: #f44336}.mat-mdc-form-field.mat-accent[_ngcontent-%COMP%]{--mat-form-field-focus-select-arrow-color: rgba(255, 64, 129, .87);--mat-form-field-filled-caret-color: #ff4081;--mat-form-field-filled-focus-active-indicator-color: #ff4081;--mat-form-field-filled-focus-label-text-color: rgba(255, 64, 129, .87);--mat-form-field-outlined-caret-color: #ff4081;--mat-form-field-outlined-focus-outline-color: #ff4081;--mat-form-field-outlined-focus-label-text-color: rgba(255, 64, 129, .87)}.mat-mdc-form-field.mat-warn[_ngcontent-%COMP%]{--mat-form-field-focus-select-arrow-color: rgba(244, 67, 54, .87);--mat-form-field-filled-caret-color: #f44336;--mat-form-field-filled-focus-active-indicator-color: #f44336;--mat-form-field-filled-focus-label-text-color: rgba(244, 67, 54, .87);--mat-form-field-outlined-caret-color: #f44336;--mat-form-field-outlined-focus-outline-color: #f44336;--mat-form-field-outlined-focus-label-text-color: rgba(244, 67, 54, .87)}html[_ngcontent-%COMP%]{--mat-form-field-container-height: 56px;--mat-form-field-filled-label-display: block;--mat-form-field-container-vertical-padding: 16px;--mat-form-field-filled-with-label-container-padding-top: 24px;--mat-form-field-filled-with-label-container-padding-bottom: 8px}html[_ngcontent-%COMP%]{--mat-form-field-container-text-font: Roboto, sans-serif;--mat-form-field-container-text-line-height: 24px;--mat-form-field-container-text-size: 16px;--mat-form-field-container-text-tracking: .03125em;--mat-form-field-container-text-weight: 400;--mat-form-field-outlined-label-text-populated-size: 16px;--mat-form-field-subscript-text-font: Roboto, sans-serif;--mat-form-field-subscript-text-line-height: 20px;--mat-form-field-subscript-text-size: 12px;--mat-form-field-subscript-text-tracking: .0333333333em;--mat-form-field-subscript-text-weight: 400;--mat-form-field-filled-label-text-font: Roboto, sans-serif;--mat-form-field-filled-label-text-size: 16px;--mat-form-field-filled-label-text-tracking: .03125em;--mat-form-field-filled-label-text-weight: 400;--mat-form-field-outlined-label-text-font: Roboto, sans-serif;--mat-form-field-outlined-label-text-size: 16px;--mat-form-field-outlined-label-text-tracking: .03125em;--mat-form-field-outlined-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-select-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color: rgba(0, 0, 0, .38);--mat-select-placeholder-text-color: rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color: rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color: rgba(0, 0, 0, .38);--mat-select-focused-arrow-color: rgba(63, 81, 181, .87);--mat-select-invalid-arrow-color: rgba(244, 67, 54, .87)}html[_ngcontent-%COMP%]   .mat-mdc-form-field.mat-accent[_ngcontent-%COMP%]{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color: rgba(0, 0, 0, .38);--mat-select-placeholder-text-color: rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color: rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color: rgba(0, 0, 0, .38);--mat-select-focused-arrow-color: rgba(255, 64, 129, .87);--mat-select-invalid-arrow-color: rgba(244, 67, 54, .87)}html[_ngcontent-%COMP%]   .mat-mdc-form-field.mat-warn[_ngcontent-%COMP%]{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color: rgba(0, 0, 0, .38);--mat-select-placeholder-text-color: rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color: rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color: rgba(0, 0, 0, .38);--mat-select-focused-arrow-color: rgba(244, 67, 54, .87);--mat-select-invalid-arrow-color: rgba(244, 67, 54, .87)}html[_ngcontent-%COMP%]{--mat-select-arrow-transform: translateY(-8px)}html[_ngcontent-%COMP%]{--mat-select-trigger-text-font: Roboto, sans-serif;--mat-select-trigger-text-line-height: 24px;--mat-select-trigger-text-size: 16px;--mat-select-trigger-text-tracking: .03125em;--mat-select-trigger-text-weight: 400}html[_ngcontent-%COMP%]{--mat-autocomplete-container-shape: 4px;--mat-autocomplete-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-autocomplete-background-color: white}html[_ngcontent-%COMP%]{--mat-dialog-container-shape: 4px;--mat-dialog-container-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12);--mat-dialog-container-max-width: 80vw;--mat-dialog-container-small-max-width: 80vw;--mat-dialog-container-min-width: 0;--mat-dialog-actions-alignment: start;--mat-dialog-actions-padding: 8px;--mat-dialog-content-padding: 20px 24px;--mat-dialog-with-actions-content-padding: 20px 24px;--mat-dialog-headline-padding: 0 24px 9px}html[_ngcontent-%COMP%]{--mat-dialog-container-color: white;--mat-dialog-subhead-color: rgba(0, 0, 0, .87);--mat-dialog-supporting-text-color: rgba(0, 0, 0, .6)}html[_ngcontent-%COMP%]{--mat-dialog-subhead-font: Roboto, sans-serif;--mat-dialog-subhead-line-height: 32px;--mat-dialog-subhead-size: 20px;--mat-dialog-subhead-weight: 500;--mat-dialog-subhead-tracking: .0125em;--mat-dialog-supporting-text-font: Roboto, sans-serif;--mat-dialog-supporting-text-line-height: 24px;--mat-dialog-supporting-text-size: 16px;--mat-dialog-supporting-text-weight: 400;--mat-dialog-supporting-text-tracking: .03125em}.mat-mdc-standard-chip[_ngcontent-%COMP%]{--mat-chip-container-shape-radius: 16px;--mat-chip-disabled-container-opacity: .4;--mat-chip-disabled-outline-color: transparent;--mat-chip-flat-selected-outline-width: 0;--mat-chip-focus-outline-color: transparent;--mat-chip-hover-state-layer-opacity: .04;--mat-chip-outline-color: transparent;--mat-chip-outline-width: 0;--mat-chip-selected-hover-state-layer-opacity: .04;--mat-chip-selected-trailing-action-state-layer-color: transparent;--mat-chip-trailing-action-focus-opacity: 1;--mat-chip-trailing-action-focus-state-layer-opacity: 0;--mat-chip-trailing-action-hover-state-layer-opacity: 0;--mat-chip-trailing-action-opacity: .54;--mat-chip-trailing-action-state-layer-color: transparent;--mat-chip-with-avatar-avatar-shape-radius: 14px;--mat-chip-with-avatar-avatar-size: 28px;--mat-chip-with-avatar-disabled-avatar-opacity: 1;--mat-chip-with-icon-disabled-icon-opacity: 1;--mat-chip-with-icon-icon-size: 18px;--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity: 1}.mat-mdc-standard-chip[_ngcontent-%COMP%]{--mat-chip-disabled-label-text-color: #212121;--mat-chip-elevated-container-color: rgb(224.4, 224.4, 224.4);--mat-chip-elevated-disabled-container-color: rgb(224.4, 224.4, 224.4);--mat-chip-elevated-selected-container-color: rgb(224.4, 224.4, 224.4);--mat-chip-flat-disabled-selected-container-color: rgb(224.4, 224.4, 224.4);--mat-chip-focus-state-layer-color: black;--mat-chip-focus-state-layer-opacity: .12;--mat-chip-hover-state-layer-color: black;--mat-chip-label-text-color: #212121;--mat-chip-selected-disabled-trailing-icon-color: #212121;--mat-chip-selected-focus-state-layer-color: black;--mat-chip-selected-focus-state-layer-opacity: .12;--mat-chip-selected-hover-state-layer-color: black;--mat-chip-selected-label-text-color: #212121;--mat-chip-selected-trailing-icon-color: #212121;--mat-chip-with-icon-disabled-icon-color: #212121;--mat-chip-with-icon-icon-color: #212121;--mat-chip-with-icon-selected-icon-color: #212121;--mat-chip-with-trailing-icon-disabled-trailing-icon-color: #212121;--mat-chip-with-trailing-icon-trailing-icon-color: #212121}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-primary[_ngcontent-%COMP%], .mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-primary[_ngcontent-%COMP%]{--mat-chip-disabled-label-text-color: white;--mat-chip-elevated-container-color: #3f51b5;--mat-chip-elevated-disabled-container-color: #3f51b5;--mat-chip-elevated-selected-container-color: #3f51b5;--mat-chip-flat-disabled-selected-container-color: #3f51b5;--mat-chip-focus-state-layer-color: black;--mat-chip-focus-state-layer-opacity: .12;--mat-chip-hover-state-layer-color: black;--mat-chip-label-text-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-focus-state-layer-color: black;--mat-chip-selected-focus-state-layer-opacity: .12;--mat-chip-selected-hover-state-layer-color: black;--mat-chip-selected-label-text-color: white;--mat-chip-selected-trailing-icon-color: white;--mat-chip-with-icon-disabled-icon-color: white;--mat-chip-with-icon-icon-color: white;--mat-chip-with-icon-selected-icon-color: white;--mat-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mat-chip-with-trailing-icon-trailing-icon-color: white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-accent[_ngcontent-%COMP%], .mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-accent[_ngcontent-%COMP%]{--mat-chip-disabled-label-text-color: white;--mat-chip-elevated-container-color: #ff4081;--mat-chip-elevated-disabled-container-color: #ff4081;--mat-chip-elevated-selected-container-color: #ff4081;--mat-chip-flat-disabled-selected-container-color: #ff4081;--mat-chip-focus-state-layer-color: black;--mat-chip-focus-state-layer-opacity: .12;--mat-chip-hover-state-layer-color: black;--mat-chip-label-text-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-focus-state-layer-color: black;--mat-chip-selected-focus-state-layer-opacity: .12;--mat-chip-selected-hover-state-layer-color: black;--mat-chip-selected-label-text-color: white;--mat-chip-selected-trailing-icon-color: white;--mat-chip-with-icon-disabled-icon-color: white;--mat-chip-with-icon-icon-color: white;--mat-chip-with-icon-selected-icon-color: white;--mat-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mat-chip-with-trailing-icon-trailing-icon-color: white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-warn[_ngcontent-%COMP%], .mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-warn[_ngcontent-%COMP%]{--mat-chip-disabled-label-text-color: white;--mat-chip-elevated-container-color: #f44336;--mat-chip-elevated-disabled-container-color: #f44336;--mat-chip-elevated-selected-container-color: #f44336;--mat-chip-flat-disabled-selected-container-color: #f44336;--mat-chip-focus-state-layer-color: black;--mat-chip-focus-state-layer-opacity: .12;--mat-chip-hover-state-layer-color: black;--mat-chip-label-text-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-focus-state-layer-color: black;--mat-chip-selected-focus-state-layer-opacity: .12;--mat-chip-selected-hover-state-layer-color: black;--mat-chip-selected-label-text-color: white;--mat-chip-selected-trailing-icon-color: white;--mat-chip-with-icon-disabled-icon-color: white;--mat-chip-with-icon-icon-color: white;--mat-chip-with-icon-selected-icon-color: white;--mat-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mat-chip-with-trailing-icon-trailing-icon-color: white}.mat-mdc-chip.mat-mdc-standard-chip[_ngcontent-%COMP%]{--mat-chip-container-height: 32px}.mat-mdc-standard-chip[_ngcontent-%COMP%]{--mat-chip-label-text-font: Roboto, sans-serif;--mat-chip-label-text-line-height: 20px;--mat-chip-label-text-size: 14px;--mat-chip-label-text-tracking: .0178571429em;--mat-chip-label-text-weight: 400}html[_ngcontent-%COMP%], html[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%]{--mat-slide-toggle-disabled-selected-handle-opacity: .38;--mat-slide-toggle-disabled-selected-icon-opacity: .38;--mat-slide-toggle-disabled-track-opacity: .12;--mat-slide-toggle-disabled-unselected-handle-opacity: .38;--mat-slide-toggle-disabled-unselected-icon-opacity: .38;--mat-slide-toggle-disabled-unselected-track-outline-color: transparent;--mat-slide-toggle-disabled-unselected-track-outline-width: 1px;--mat-slide-toggle-handle-height: 20px;--mat-slide-toggle-handle-shape: 10px;--mat-slide-toggle-handle-width: 20px;--mat-slide-toggle-hidden-track-opacity: 1;--mat-slide-toggle-hidden-track-transition: transform 75ms 0ms cubic-bezier(.4, 0, .6, 1);--mat-slide-toggle-pressed-handle-size: 20px;--mat-slide-toggle-selected-focus-state-layer-opacity: .12;--mat-slide-toggle-selected-handle-horizontal-margin: 0;--mat-slide-toggle-selected-handle-size: 20px;--mat-slide-toggle-selected-hover-state-layer-opacity: .04;--mat-slide-toggle-selected-icon-size: 18px;--mat-slide-toggle-selected-pressed-handle-horizontal-margin: 0;--mat-slide-toggle-selected-pressed-state-layer-opacity: .1;--mat-slide-toggle-selected-track-outline-color: transparent;--mat-slide-toggle-selected-track-outline-width: 1px;--mat-slide-toggle-selected-with-icon-handle-horizontal-margin: 0;--mat-slide-toggle-track-height: 14px;--mat-slide-toggle-track-outline-color: transparent;--mat-slide-toggle-track-outline-width: 1px;--mat-slide-toggle-track-shape: 7px;--mat-slide-toggle-track-width: 36px;--mat-slide-toggle-unselected-focus-state-layer-opacity: .12;--mat-slide-toggle-unselected-handle-horizontal-margin: 0;--mat-slide-toggle-unselected-handle-size: 20px;--mat-slide-toggle-unselected-hover-state-layer-opacity: .04;--mat-slide-toggle-unselected-icon-size: 18px;--mat-slide-toggle-unselected-pressed-handle-horizontal-margin: 0;--mat-slide-toggle-unselected-pressed-state-layer-opacity: .1;--mat-slide-toggle-unselected-with-icon-handle-horizontal-margin: 0;--mat-slide-toggle-visible-track-opacity: 1;--mat-slide-toggle-visible-track-transition: transform 75ms 0ms cubic-bezier(0, 0, .2, 1);--mat-slide-toggle-with-icon-handle-size: 20px}html[_ngcontent-%COMP%]{--mat-slide-toggle-selected-focus-state-layer-color: #3949ab;--mat-slide-toggle-selected-handle-color: #3949ab;--mat-slide-toggle-selected-hover-state-layer-color: #3949ab;--mat-slide-toggle-selected-pressed-state-layer-color: #3949ab;--mat-slide-toggle-selected-focus-handle-color: #1a237e;--mat-slide-toggle-selected-hover-handle-color: #1a237e;--mat-slide-toggle-selected-pressed-handle-color: #1a237e;--mat-slide-toggle-selected-focus-track-color: #7986cb;--mat-slide-toggle-selected-hover-track-color: #7986cb;--mat-slide-toggle-selected-pressed-track-color: #7986cb;--mat-slide-toggle-selected-track-color: #7986cb;--mat-slide-toggle-disabled-handle-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-slide-toggle-disabled-selected-handle-color: #424242;--mat-slide-toggle-disabled-selected-icon-color: #fff;--mat-slide-toggle-disabled-selected-track-color: #424242;--mat-slide-toggle-disabled-unselected-handle-color: #424242;--mat-slide-toggle-disabled-unselected-icon-color: #fff;--mat-slide-toggle-disabled-unselected-track-color: #424242;--mat-slide-toggle-handle-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-slide-toggle-handle-surface-color: #fff;--mat-slide-toggle-label-text-color: rgba(0, 0, 0, .87);--mat-slide-toggle-selected-icon-color: #fff;--mat-slide-toggle-unselected-hover-handle-color: #212121;--mat-slide-toggle-unselected-focus-handle-color: #212121;--mat-slide-toggle-unselected-focus-state-layer-color: #424242;--mat-slide-toggle-unselected-focus-track-color: #e0e0e0;--mat-slide-toggle-unselected-icon-color: #fff;--mat-slide-toggle-unselected-handle-color: #616161;--mat-slide-toggle-unselected-hover-state-layer-color: #424242;--mat-slide-toggle-unselected-hover-track-color: #e0e0e0;--mat-slide-toggle-unselected-pressed-handle-color: #212121;--mat-slide-toggle-unselected-pressed-track-color: #e0e0e0;--mat-slide-toggle-unselected-pressed-state-layer-color: #424242;--mat-slide-toggle-unselected-track-color: #e0e0e0}html[_ngcontent-%COMP%]{--mdc-slide-toggle-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-slide-toggle-disabled-label-text-color: rgba(0, 0, 0, .38)}html[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%]{--mat-slide-toggle-label-text-color: rgba(0, 0, 0, .87)}html[_ngcontent-%COMP%]   .mat-mdc-slide-toggle.mat-accent[_ngcontent-%COMP%]{--mat-slide-toggle-selected-focus-state-layer-color: #d81b60;--mat-slide-toggle-selected-handle-color: #d81b60;--mat-slide-toggle-selected-hover-state-layer-color: #d81b60;--mat-slide-toggle-selected-pressed-state-layer-color: #d81b60;--mat-slide-toggle-selected-focus-handle-color: #880e4f;--mat-slide-toggle-selected-hover-handle-color: #880e4f;--mat-slide-toggle-selected-pressed-handle-color: #880e4f;--mat-slide-toggle-selected-focus-track-color: #f06292;--mat-slide-toggle-selected-hover-track-color: #f06292;--mat-slide-toggle-selected-pressed-track-color: #f06292;--mat-slide-toggle-selected-track-color: #f06292}html[_ngcontent-%COMP%]   .mat-mdc-slide-toggle.mat-warn[_ngcontent-%COMP%]{--mat-slide-toggle-selected-focus-state-layer-color: #e53935;--mat-slide-toggle-selected-handle-color: #e53935;--mat-slide-toggle-selected-hover-state-layer-color: #e53935;--mat-slide-toggle-selected-pressed-state-layer-color: #e53935;--mat-slide-toggle-selected-focus-handle-color: #b71c1c;--mat-slide-toggle-selected-hover-handle-color: #b71c1c;--mat-slide-toggle-selected-pressed-handle-color: #b71c1c;--mat-slide-toggle-selected-focus-track-color: #e57373;--mat-slide-toggle-selected-hover-track-color: #e57373;--mat-slide-toggle-selected-pressed-track-color: #e57373;--mat-slide-toggle-selected-track-color: #e57373}html[_ngcontent-%COMP%], html[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%]{--mat-slide-toggle-state-layer-size: 40px}html[_ngcontent-%COMP%], html[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%]{--mat-slide-toggle-label-text-font: Roboto, sans-serif;--mat-slide-toggle-label-text-line-height: 20px;--mat-slide-toggle-label-text-size: 14px;--mat-slide-toggle-label-text-tracking: .0178571429em;--mat-slide-toggle-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-radio-disabled-selected-icon-opacity: .38;--mat-radio-disabled-unselected-icon-opacity: .38;--mat-radio-state-layer-size: 40px}.mat-mdc-radio-button.mat-primary[_ngcontent-%COMP%]{--mat-radio-checked-ripple-color: #3f51b5;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-disabled-selected-icon-color: black;--mat-radio-disabled-unselected-icon-color: black;--mat-radio-label-text-color: rgba(0, 0, 0, .87);--mat-radio-ripple-color: black;--mat-radio-selected-focus-icon-color: #3f51b5;--mat-radio-selected-hover-icon-color: #3f51b5;--mat-radio-selected-icon-color: #3f51b5;--mat-radio-selected-pressed-icon-color: #3f51b5;--mat-radio-unselected-focus-icon-color: #212121;--mat-radio-unselected-hover-icon-color: #212121;--mat-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54)}.mat-mdc-radio-button.mat-accent[_ngcontent-%COMP%]{--mat-radio-checked-ripple-color: #ff4081;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-disabled-selected-icon-color: black;--mat-radio-disabled-unselected-icon-color: black;--mat-radio-label-text-color: rgba(0, 0, 0, .87);--mat-radio-ripple-color: black;--mat-radio-selected-focus-icon-color: #ff4081;--mat-radio-selected-hover-icon-color: #ff4081;--mat-radio-selected-icon-color: #ff4081;--mat-radio-selected-pressed-icon-color: #ff4081;--mat-radio-unselected-focus-icon-color: #212121;--mat-radio-unselected-hover-icon-color: #212121;--mat-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54)}.mat-mdc-radio-button.mat-warn[_ngcontent-%COMP%]{--mat-radio-checked-ripple-color: #f44336;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-disabled-selected-icon-color: black;--mat-radio-disabled-unselected-icon-color: black;--mat-radio-label-text-color: rgba(0, 0, 0, .87);--mat-radio-ripple-color: black;--mat-radio-selected-focus-icon-color: #f44336;--mat-radio-selected-hover-icon-color: #f44336;--mat-radio-selected-icon-color: #f44336;--mat-radio-selected-pressed-icon-color: #f44336;--mat-radio-unselected-focus-icon-color: #212121;--mat-radio-unselected-hover-icon-color: #212121;--mat-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54)}html[_ngcontent-%COMP%]{--mat-radio-state-layer-size: 40px;--mat-radio-touch-target-display: block}html[_ngcontent-%COMP%]{--mat-radio-label-text-font: Roboto, sans-serif;--mat-radio-label-text-line-height: 20px;--mat-radio-label-text-size: 14px;--mat-radio-label-text-tracking: .0178571429em;--mat-radio-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-slider-active-track-height: 6px;--mat-slider-active-track-shape: 9999px;--mat-slider-handle-elevation: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-slider-handle-height: 20px;--mat-slider-handle-shape: 50%;--mat-slider-handle-width: 20px;--mat-slider-inactive-track-height: 4px;--mat-slider-inactive-track-shape: 9999px;--mat-slider-value-indicator-border-radius: 4px;--mat-slider-value-indicator-caret-display: block;--mat-slider-value-indicator-container-transform: translateX(-50%);--mat-slider-value-indicator-height: 32px;--mat-slider-value-indicator-padding: 0 12px;--mat-slider-value-indicator-text-transform: none;--mat-slider-value-indicator-width: auto;--mat-slider-with-overlap-handle-outline-width: 1px;--mat-slider-with-tick-marks-active-container-opacity: .6;--mat-slider-with-tick-marks-container-shape: 50%;--mat-slider-with-tick-marks-container-size: 2px;--mat-slider-with-tick-marks-inactive-container-opacity: .6}html[_ngcontent-%COMP%]{--mat-slider-active-track-color: #3f51b5;--mat-slider-focus-handle-color: #3f51b5;--mat-slider-focus-state-layer-color: rgba(63, 81, 181, .2);--mat-slider-handle-color: #3f51b5;--mat-slider-hover-handle-color: #3f51b5;--mat-slider-hover-state-layer-color: rgba(63, 81, 181, .05);--mat-slider-inactive-track-color: #3f51b5;--mat-slider-ripple-color: #3f51b5;--mat-slider-with-tick-marks-active-container-color: white;--mat-slider-with-tick-marks-inactive-container-color: #3f51b5;--mat-slider-disabled-active-track-color: #000;--mat-slider-disabled-handle-color: #000;--mat-slider-disabled-inactive-track-color: #000;--mat-slider-label-container-color: #000;--mat-slider-label-label-text-color: #fff;--mat-slider-value-indicator-opacity: .6;--mat-slider-with-overlap-handle-outline-color: #fff;--mat-slider-with-tick-marks-disabled-container-color: #000}html[_ngcontent-%COMP%]   .mat-accent[_ngcontent-%COMP%]{--mat-slider-active-track-color: #ff4081;--mat-slider-focus-handle-color: #ff4081;--mat-slider-focus-state-layer-color: rgba(255, 64, 129, .2);--mat-slider-handle-color: #ff4081;--mat-slider-hover-handle-color: #ff4081;--mat-slider-hover-state-layer-color: rgba(255, 64, 129, .05);--mat-slider-inactive-track-color: #ff4081;--mat-slider-ripple-color: #ff4081;--mat-slider-with-tick-marks-active-container-color: white;--mat-slider-with-tick-marks-inactive-container-color: #ff4081}html[_ngcontent-%COMP%]   .mat-warn[_ngcontent-%COMP%]{--mat-slider-active-track-color: #f44336;--mat-slider-focus-handle-color: #f44336;--mat-slider-focus-state-layer-color: rgba(244, 67, 54, .2);--mat-slider-handle-color: #f44336;--mat-slider-hover-handle-color: #f44336;--mat-slider-hover-state-layer-color: rgba(244, 67, 54, .05);--mat-slider-inactive-track-color: #f44336;--mat-slider-ripple-color: #f44336;--mat-slider-with-tick-marks-active-container-color: white;--mat-slider-with-tick-marks-inactive-container-color: #f44336}html[_ngcontent-%COMP%]{--mat-slider-label-label-text-font: Roboto, sans-serif;--mat-slider-label-label-text-size: 14px;--mat-slider-label-label-text-line-height: 22px;--mat-slider-label-label-text-tracking: .0071428571em;--mat-slider-label-label-text-weight: 500}html[_ngcontent-%COMP%]{--mat-menu-container-shape: 4px;--mat-menu-divider-bottom-spacing: 0;--mat-menu-divider-top-spacing: 0;--mat-menu-item-spacing: 16px;--mat-menu-item-icon-size: 24px;--mat-menu-item-leading-spacing: 16px;--mat-menu-item-trailing-spacing: 16px;--mat-menu-item-with-icon-leading-spacing: 16px;--mat-menu-item-with-icon-trailing-spacing: 16px;--mat-menu-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-menu-item-label-text-color: rgba(0, 0, 0, .87);--mat-menu-item-icon-color: rgba(0, 0, 0, .87);--mat-menu-item-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-menu-item-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-menu-container-color: white;--mat-menu-divider-color: rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-menu-item-label-text-font: Roboto, sans-serif;--mat-menu-item-label-text-size: 16px;--mat-menu-item-label-text-tracking: .03125em;--mat-menu-item-label-text-line-height: 24px;--mat-menu-item-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-list-active-indicator-color: transparent;--mat-list-active-indicator-shape: 4px;--mat-list-list-item-container-shape: 0;--mat-list-list-item-leading-avatar-shape: 50%;--mat-list-list-item-container-color: transparent;--mat-list-list-item-selected-container-color: transparent;--mat-list-list-item-leading-avatar-color: transparent;--mat-list-list-item-leading-icon-size: 24px;--mat-list-list-item-leading-avatar-size: 40px;--mat-list-list-item-trailing-icon-size: 24px;--mat-list-list-item-disabled-state-layer-color: transparent;--mat-list-list-item-disabled-state-layer-opacity: 0;--mat-list-list-item-disabled-label-text-opacity: .38;--mat-list-list-item-disabled-leading-icon-opacity: .38;--mat-list-list-item-disabled-trailing-icon-opacity: .38}html[_ngcontent-%COMP%]{--mat-list-list-item-label-text-color: rgba(0, 0, 0, .87);--mat-list-list-item-supporting-text-color: rgba(0, 0, 0, .54);--mat-list-list-item-leading-icon-color: rgba(0, 0, 0, .38);--mat-list-list-item-trailing-supporting-text-color: rgba(0, 0, 0, .38);--mat-list-list-item-trailing-icon-color: rgba(0, 0, 0, .38);--mat-list-list-item-selected-trailing-icon-color: rgba(0, 0, 0, .38);--mat-list-list-item-disabled-label-text-color: black;--mat-list-list-item-disabled-leading-icon-color: black;--mat-list-list-item-disabled-trailing-icon-color: black;--mat-list-list-item-hover-label-text-color: rgba(0, 0, 0, .87);--mat-list-list-item-hover-leading-icon-color: rgba(0, 0, 0, .38);--mat-list-list-item-hover-state-layer-color: black;--mat-list-list-item-hover-state-layer-opacity: .04;--mat-list-list-item-hover-trailing-icon-color: rgba(0, 0, 0, .38);--mat-list-list-item-focus-label-text-color: rgba(0, 0, 0, .87);--mat-list-list-item-focus-state-layer-color: black;--mat-list-list-item-focus-state-layer-opacity: .12}.mdc-list-item__start[_ngcontent-%COMP%], .mdc-list-item__end[_ngcontent-%COMP%]{--mat-radio-checked-ripple-color: #3f51b5;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-disabled-selected-icon-color: black;--mat-radio-disabled-unselected-icon-color: black;--mat-radio-label-text-color: rgba(0, 0, 0, .87);--mat-radio-ripple-color: black;--mat-radio-selected-focus-icon-color: #3f51b5;--mat-radio-selected-hover-icon-color: #3f51b5;--mat-radio-selected-icon-color: #3f51b5;--mat-radio-selected-pressed-icon-color: #3f51b5;--mat-radio-unselected-focus-icon-color: #212121;--mat-radio-unselected-hover-icon-color: #212121;--mat-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54)}.mat-accent[_ngcontent-%COMP%]   .mdc-list-item__start[_ngcontent-%COMP%], .mat-accent[_ngcontent-%COMP%]   .mdc-list-item__end[_ngcontent-%COMP%]{--mat-radio-checked-ripple-color: #ff4081;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-disabled-selected-icon-color: black;--mat-radio-disabled-unselected-icon-color: black;--mat-radio-label-text-color: rgba(0, 0, 0, .87);--mat-radio-ripple-color: black;--mat-radio-selected-focus-icon-color: #ff4081;--mat-radio-selected-hover-icon-color: #ff4081;--mat-radio-selected-icon-color: #ff4081;--mat-radio-selected-pressed-icon-color: #ff4081;--mat-radio-unselected-focus-icon-color: #212121;--mat-radio-unselected-hover-icon-color: #212121;--mat-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54)}.mat-warn[_ngcontent-%COMP%]   .mdc-list-item__start[_ngcontent-%COMP%], .mat-warn[_ngcontent-%COMP%]   .mdc-list-item__end[_ngcontent-%COMP%]{--mat-radio-checked-ripple-color: #f44336;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-disabled-selected-icon-color: black;--mat-radio-disabled-unselected-icon-color: black;--mat-radio-label-text-color: rgba(0, 0, 0, .87);--mat-radio-ripple-color: black;--mat-radio-selected-focus-icon-color: #f44336;--mat-radio-selected-hover-icon-color: #f44336;--mat-radio-selected-icon-color: #f44336;--mat-radio-selected-pressed-icon-color: #f44336;--mat-radio-unselected-focus-icon-color: #212121;--mat-radio-unselected-hover-icon-color: #212121;--mat-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54)}.mat-mdc-list-option[_ngcontent-%COMP%]{--mat-checkbox-disabled-label-color: rgba(0, 0, 0, .38);--mat-checkbox-label-text-color: rgba(0, 0, 0, .87);--mat-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #3f51b5;--mat-checkbox-selected-hover-icon-color: #3f51b5;--mat-checkbox-selected-icon-color: #3f51b5;--mat-checkbox-selected-pressed-icon-color: #3f51b5;--mat-checkbox-unselected-focus-icon-color: #212121;--mat-checkbox-unselected-hover-icon-color: #212121;--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color: #3f51b5;--mat-checkbox-selected-hover-state-layer-color: #3f51b5;--mat-checkbox-selected-pressed-state-layer-color: #3f51b5;--mat-checkbox-unselected-focus-state-layer-color: black;--mat-checkbox-unselected-hover-state-layer-color: black;--mat-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-list-option.mat-accent[_ngcontent-%COMP%]{--mat-checkbox-disabled-label-color: rgba(0, 0, 0, .38);--mat-checkbox-label-text-color: rgba(0, 0, 0, .87);--mat-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #ff4081;--mat-checkbox-selected-hover-icon-color: #ff4081;--mat-checkbox-selected-icon-color: #ff4081;--mat-checkbox-selected-pressed-icon-color: #ff4081;--mat-checkbox-unselected-focus-icon-color: #212121;--mat-checkbox-unselected-hover-icon-color: #212121;--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color: #ff4081;--mat-checkbox-selected-hover-state-layer-color: #ff4081;--mat-checkbox-selected-pressed-state-layer-color: #ff4081;--mat-checkbox-unselected-focus-state-layer-color: black;--mat-checkbox-unselected-hover-state-layer-color: black;--mat-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-list-option.mat-warn[_ngcontent-%COMP%]{--mat-checkbox-disabled-label-color: rgba(0, 0, 0, .38);--mat-checkbox-label-text-color: rgba(0, 0, 0, .87);--mat-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #f44336;--mat-checkbox-selected-hover-icon-color: #f44336;--mat-checkbox-selected-icon-color: #f44336;--mat-checkbox-selected-pressed-icon-color: #f44336;--mat-checkbox-unselected-focus-icon-color: #212121;--mat-checkbox-unselected-hover-icon-color: #212121;--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color: #f44336;--mat-checkbox-selected-hover-state-layer-color: #f44336;--mat-checkbox-selected-pressed-state-layer-color: #f44336;--mat-checkbox-unselected-focus-state-layer-color: black;--mat-checkbox-unselected-hover-state-layer-color: black;--mat-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-list-base.mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--selected[_ngcontent-%COMP%]   .mdc-list-item__primary-text[_ngcontent-%COMP%], .mat-mdc-list-base.mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--selected[_ngcontent-%COMP%]   .mdc-list-item__start[_ngcontent-%COMP%], .mat-mdc-list-base.mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--activated[_ngcontent-%COMP%]   .mdc-list-item__primary-text[_ngcontent-%COMP%], .mat-mdc-list-base.mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--activated[_ngcontent-%COMP%]   .mdc-list-item__start[_ngcontent-%COMP%]{color:#3f51b5}.mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--disabled[_ngcontent-%COMP%]   .mdc-list-item__start[_ngcontent-%COMP%], .mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--disabled[_ngcontent-%COMP%]   .mdc-list-item__content[_ngcontent-%COMP%], .mat-mdc-list-base[_ngcontent-%COMP%]   .mdc-list-item--disabled[_ngcontent-%COMP%]   .mdc-list-item__end[_ngcontent-%COMP%]{opacity:1}html[_ngcontent-%COMP%]{--mat-list-list-item-leading-icon-start-space: 16px;--mat-list-list-item-leading-icon-end-space: 32px;--mat-list-list-item-one-line-container-height: 48px;--mat-list-list-item-two-line-container-height: 64px;--mat-list-list-item-three-line-container-height: 88px}.mdc-list-item__start[_ngcontent-%COMP%], .mdc-list-item__end[_ngcontent-%COMP%]{--mat-radio-state-layer-size: 40px;--mat-radio-touch-target-display: block}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-one-line[_ngcontent-%COMP%], .mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-one-line[_ngcontent-%COMP%], .mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-one-line[_ngcontent-%COMP%]{height:56px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines[_ngcontent-%COMP%], .mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines[_ngcontent-%COMP%], .mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines[_ngcontent-%COMP%]{height:72px}html[_ngcontent-%COMP%]{--mat-list-list-item-label-text-font: Roboto, sans-serif;--mat-list-list-item-label-text-line-height: 24px;--mat-list-list-item-label-text-size: 16px;--mat-list-list-item-label-text-tracking: .03125em;--mat-list-list-item-label-text-weight: 400;--mat-list-list-item-supporting-text-font: Roboto, sans-serif;--mat-list-list-item-supporting-text-line-height: 20px;--mat-list-list-item-supporting-text-size: 14px;--mat-list-list-item-supporting-text-tracking: .0178571429em;--mat-list-list-item-supporting-text-weight: 400;--mat-list-list-item-trailing-supporting-text-font: Roboto, sans-serif;--mat-list-list-item-trailing-supporting-text-line-height: 20px;--mat-list-list-item-trailing-supporting-text-size: 12px;--mat-list-list-item-trailing-supporting-text-tracking: .0333333333em;--mat-list-list-item-trailing-supporting-text-weight: 400}.mdc-list-group__subheader[_ngcontent-%COMP%]{font:400 16px/28px Roboto,sans-serif;letter-spacing:.009375em}html[_ngcontent-%COMP%]{--mat-paginator-container-text-color: rgba(0, 0, 0, .87);--mat-paginator-container-background-color: white;--mat-paginator-enabled-icon-color: rgba(0, 0, 0, .54);--mat-paginator-disabled-icon-color: rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-paginator-container-size: 56px;--mat-paginator-form-field-container-height: 40px;--mat-paginator-form-field-container-vertical-padding: 8px;--mat-paginator-touch-target-display: block}html[_ngcontent-%COMP%]{--mat-paginator-container-text-font: Roboto, sans-serif;--mat-paginator-container-text-line-height: 20px;--mat-paginator-container-text-size: 12px;--mat-paginator-container-text-tracking: .0333333333em;--mat-paginator-container-text-weight: 400;--mat-paginator-select-trigger-text-size: 12px}html[_ngcontent-%COMP%]{--mat-tab-container-height: 48px;--mat-tab-divider-color: transparent;--mat-tab-divider-height: 0;--mat-tab-active-indicator-height: 2px;--mat-tab-active-indicator-shape: 0}.mat-mdc-tab-group[_ngcontent-%COMP%], .mat-mdc-tab-nav-bar[_ngcontent-%COMP%]{--mat-tab-disabled-ripple-color: rgba(0, 0, 0, .38);--mat-tab-pagination-icon-color: black;--mat-tab-inactive-label-text-color: rgba(0, 0, 0, .6);--mat-tab-active-label-text-color: #3f51b5;--mat-tab-active-ripple-color: #3f51b5;--mat-tab-inactive-ripple-color: #3f51b5;--mat-tab-inactive-focus-label-text-color: rgba(0, 0, 0, .6);--mat-tab-inactive-hover-label-text-color: rgba(0, 0, 0, .6);--mat-tab-active-focus-label-text-color: #3f51b5;--mat-tab-active-hover-label-text-color: #3f51b5;--mat-tab-active-focus-indicator-color: #3f51b5;--mat-tab-active-hover-indicator-color: #3f51b5;--mat-tab-active-indicator-color: #3f51b5}.mat-mdc-tab-group.mat-accent[_ngcontent-%COMP%], .mat-mdc-tab-nav-bar.mat-accent[_ngcontent-%COMP%]{--mat-tab-disabled-ripple-color: rgba(0, 0, 0, .38);--mat-tab-pagination-icon-color: black;--mat-tab-inactive-label-text-color: rgba(0, 0, 0, .6);--mat-tab-active-label-text-color: #ff4081;--mat-tab-active-ripple-color: #ff4081;--mat-tab-inactive-ripple-color: #ff4081;--mat-tab-inactive-focus-label-text-color: rgba(0, 0, 0, .6);--mat-tab-inactive-hover-label-text-color: rgba(0, 0, 0, .6);--mat-tab-active-focus-label-text-color: #ff4081;--mat-tab-active-hover-label-text-color: #ff4081;--mat-tab-active-focus-indicator-color: #ff4081;--mat-tab-active-hover-indicator-color: #ff4081;--mat-tab-active-indicator-color: #ff4081}.mat-mdc-tab-group.mat-warn[_ngcontent-%COMP%], .mat-mdc-tab-nav-bar.mat-warn[_ngcontent-%COMP%]{--mat-tab-disabled-ripple-color: rgba(0, 0, 0, .38);--mat-tab-pagination-icon-color: black;--mat-tab-inactive-label-text-color: rgba(0, 0, 0, .6);--mat-tab-active-label-text-color: #f44336;--mat-tab-active-ripple-color: #f44336;--mat-tab-inactive-ripple-color: #f44336;--mat-tab-inactive-focus-label-text-color: rgba(0, 0, 0, .6);--mat-tab-inactive-hover-label-text-color: rgba(0, 0, 0, .6);--mat-tab-active-focus-label-text-color: #f44336;--mat-tab-active-hover-label-text-color: #f44336;--mat-tab-active-focus-indicator-color: #f44336;--mat-tab-active-hover-indicator-color: #f44336;--mat-tab-active-indicator-color: #f44336}.mat-mdc-tab-header[_ngcontent-%COMP%]{--mat-tab-container-height: 48px}.mat-mdc-tab-header[_ngcontent-%COMP%]{--mat-tab-label-text-font: Roboto, sans-serif;--mat-tab-label-text-size: 14px;--mat-tab-label-text-tracking: .0892857143em;--mat-tab-label-text-line-height: 36px;--mat-tab-label-text-weight: 500}html[_ngcontent-%COMP%]{--mat-checkbox-disabled-selected-checkmark-color: #fff;--mat-checkbox-selected-focus-state-layer-opacity: .16;--mat-checkbox-selected-hover-state-layer-opacity: .04;--mat-checkbox-selected-pressed-state-layer-opacity: .16;--mat-checkbox-unselected-focus-state-layer-opacity: .16;--mat-checkbox-unselected-hover-state-layer-opacity: .04;--mat-checkbox-unselected-pressed-state-layer-opacity: .16}html[_ngcontent-%COMP%]{--mat-checkbox-disabled-label-color: rgba(0, 0, 0, .38);--mat-checkbox-label-text-color: rgba(0, 0, 0, .87);--mat-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #ff4081;--mat-checkbox-selected-hover-icon-color: #ff4081;--mat-checkbox-selected-icon-color: #ff4081;--mat-checkbox-selected-pressed-icon-color: #ff4081;--mat-checkbox-unselected-focus-icon-color: #212121;--mat-checkbox-unselected-hover-icon-color: #212121;--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color: #ff4081;--mat-checkbox-selected-hover-state-layer-color: #ff4081;--mat-checkbox-selected-pressed-state-layer-color: #ff4081;--mat-checkbox-unselected-focus-state-layer-color: black;--mat-checkbox-unselected-hover-state-layer-color: black;--mat-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-checkbox.mat-primary[_ngcontent-%COMP%]{--mat-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #3f51b5;--mat-checkbox-selected-hover-icon-color: #3f51b5;--mat-checkbox-selected-icon-color: #3f51b5;--mat-checkbox-selected-pressed-icon-color: #3f51b5;--mat-checkbox-unselected-focus-icon-color: #212121;--mat-checkbox-unselected-hover-icon-color: #212121;--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color: #3f51b5;--mat-checkbox-selected-hover-state-layer-color: #3f51b5;--mat-checkbox-selected-pressed-state-layer-color: #3f51b5;--mat-checkbox-unselected-focus-state-layer-color: black;--mat-checkbox-unselected-hover-state-layer-color: black;--mat-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-checkbox.mat-warn[_ngcontent-%COMP%]{--mat-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #f44336;--mat-checkbox-selected-hover-icon-color: #f44336;--mat-checkbox-selected-icon-color: #f44336;--mat-checkbox-selected-pressed-icon-color: #f44336;--mat-checkbox-unselected-focus-icon-color: #212121;--mat-checkbox-unselected-hover-icon-color: #212121;--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color: #f44336;--mat-checkbox-selected-hover-state-layer-color: #f44336;--mat-checkbox-selected-pressed-state-layer-color: #f44336;--mat-checkbox-unselected-focus-state-layer-color: black;--mat-checkbox-unselected-hover-state-layer-color: black;--mat-checkbox-unselected-pressed-state-layer-color: black}html[_ngcontent-%COMP%]{--mat-checkbox-touch-target-display: block;--mat-checkbox-state-layer-size: 40px}html[_ngcontent-%COMP%]{--mat-checkbox-label-text-font: Roboto, sans-serif;--mat-checkbox-label-text-line-height: 20px;--mat-checkbox-label-text-size: 14px;--mat-checkbox-label-text-tracking: .0178571429em;--mat-checkbox-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-button-filled-container-shape: 4px;--mat-button-filled-horizontal-padding: 16px;--mat-button-filled-icon-offset: -4px;--mat-button-filled-icon-spacing: 8px;--mat-button-outlined-container-shape: 4px;--mat-button-outlined-horizontal-padding: 15px;--mat-button-outlined-icon-offset: -4px;--mat-button-outlined-icon-spacing: 8px;--mat-button-outlined-keep-touch-target: false;--mat-button-outlined-outline-width: 1px;--mat-button-protected-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-button-protected-container-shape: 4px;--mat-button-protected-disabled-container-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-button-protected-focus-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-button-protected-horizontal-padding: 16px;--mat-button-protected-hover-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-button-protected-icon-offset: -4px;--mat-button-protected-icon-spacing: 8px;--mat-button-protected-pressed-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-button-text-container-shape: 4px;--mat-button-text-horizontal-padding: 8px;--mat-button-text-icon-offset: 0;--mat-button-text-icon-spacing: 8px;--mat-button-text-with-icon-horizontal-padding: 8px;--mat-button-tonal-container-shape: 4px;--mat-button-tonal-horizontal-padding: 16px;--mat-button-tonal-icon-offset: -4px;--mat-button-tonal-icon-spacing: 8px}html[_ngcontent-%COMP%]{--mat-button-filled-container-color: white;--mat-button-filled-disabled-container-color: rgba(0, 0, 0, .12);--mat-button-filled-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-button-filled-disabled-state-layer-color: black;--mat-button-filled-focus-state-layer-opacity: .12;--mat-button-filled-hover-state-layer-opacity: .04;--mat-button-filled-label-text-color: black;--mat-button-filled-pressed-state-layer-opacity: .12;--mat-button-filled-ripple-color: rgba(0, 0, 0, .1);--mat-button-filled-state-layer-color: black;--mat-button-outlined-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-button-outlined-disabled-outline-color: rgba(0, 0, 0, .12);--mat-button-outlined-disabled-state-layer-color: black;--mat-button-outlined-focus-state-layer-opacity: .12;--mat-button-outlined-hover-state-layer-opacity: .04;--mat-button-outlined-label-text-color: black;--mat-button-outlined-outline-color: rgba(0, 0, 0, .12);--mat-button-outlined-pressed-state-layer-opacity: .12;--mat-button-outlined-ripple-color: rgba(0, 0, 0, .1);--mat-button-outlined-state-layer-color: black;--mat-button-protected-container-color: white;--mat-button-protected-disabled-container-color: rgba(0, 0, 0, .12);--mat-button-protected-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-button-protected-disabled-state-layer-color: black;--mat-button-protected-focus-state-layer-opacity: .12;--mat-button-protected-hover-state-layer-opacity: .04;--mat-button-protected-label-text-color: black;--mat-button-protected-pressed-state-layer-opacity: .12;--mat-button-protected-ripple-color: rgba(0, 0, 0, .1);--mat-button-protected-state-layer-color: black;--mat-button-text-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-button-text-disabled-state-layer-color: black;--mat-button-text-focus-state-layer-opacity: .12;--mat-button-text-hover-state-layer-opacity: .04;--mat-button-text-label-text-color: black;--mat-button-text-pressed-state-layer-opacity: .12;--mat-button-text-ripple-color: rgba(0, 0, 0, .1);--mat-button-text-state-layer-color: black;--mat-button-tonal-container-color: white;--mat-button-tonal-disabled-container-color: rgba(0, 0, 0, .12);--mat-button-tonal-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-button-tonal-disabled-state-layer-color: black;--mat-button-tonal-focus-state-layer-opacity: .12;--mat-button-tonal-hover-state-layer-opacity: .04;--mat-button-tonal-label-text-color: black;--mat-button-tonal-pressed-state-layer-opacity: .12;--mat-button-tonal-ripple-color: rgba(0, 0, 0, .1);--mat-button-tonal-state-layer-color: black}.mat-mdc-button.mat-primary[_ngcontent-%COMP%], .mat-mdc-unelevated-button.mat-primary[_ngcontent-%COMP%], .mat-mdc-raised-button.mat-primary[_ngcontent-%COMP%], .mat-mdc-outlined-button.mat-primary[_ngcontent-%COMP%], .mat-tonal-button.mat-primary[_ngcontent-%COMP%]{--mat-button-filled-container-color: #3f51b5;--mat-button-filled-label-text-color: white;--mat-button-filled-ripple-color: rgba(255, 255, 255, .1);--mat-button-filled-state-layer-color: white;--mat-button-outlined-label-text-color: #3f51b5;--mat-button-outlined-outline-color: rgba(0, 0, 0, .12);--mat-button-outlined-ripple-color: rgba(63, 81, 181, .1);--mat-button-outlined-state-layer-color: #3f51b5;--mat-button-protected-container-color: #3f51b5;--mat-button-protected-label-text-color: white;--mat-button-protected-ripple-color: rgba(255, 255, 255, .1);--mat-button-protected-state-layer-color: white;--mat-button-text-label-text-color: #3f51b5;--mat-button-text-ripple-color: rgba(63, 81, 181, .1);--mat-button-text-state-layer-color: #3f51b5;--mat-button-tonal-container-color: #3f51b5;--mat-button-tonal-label-text-color: white;--mat-button-tonal-ripple-color: rgba(255, 255, 255, .1);--mat-button-tonal-state-layer-color: white}.mat-mdc-button.mat-accent[_ngcontent-%COMP%], .mat-mdc-unelevated-button.mat-accent[_ngcontent-%COMP%], .mat-mdc-raised-button.mat-accent[_ngcontent-%COMP%], .mat-mdc-outlined-button.mat-accent[_ngcontent-%COMP%], .mat-tonal-button.mat-accent[_ngcontent-%COMP%]{--mat-button-filled-container-color: #ff4081;--mat-button-filled-label-text-color: white;--mat-button-filled-ripple-color: rgba(255, 255, 255, .1);--mat-button-filled-state-layer-color: white;--mat-button-outlined-label-text-color: #ff4081;--mat-button-outlined-outline-color: rgba(0, 0, 0, .12);--mat-button-outlined-ripple-color: rgba(255, 64, 129, .1);--mat-button-outlined-state-layer-color: #ff4081;--mat-button-protected-container-color: #ff4081;--mat-button-protected-label-text-color: white;--mat-button-protected-ripple-color: rgba(255, 255, 255, .1);--mat-button-protected-state-layer-color: white;--mat-button-text-label-text-color: #ff4081;--mat-button-text-ripple-color: rgba(255, 64, 129, .1);--mat-button-text-state-layer-color: #ff4081;--mat-button-tonal-container-color: #ff4081;--mat-button-tonal-label-text-color: white;--mat-button-tonal-ripple-color: rgba(255, 255, 255, .1);--mat-button-tonal-state-layer-color: white}.mat-mdc-button.mat-warn[_ngcontent-%COMP%], .mat-mdc-unelevated-button.mat-warn[_ngcontent-%COMP%], .mat-mdc-raised-button.mat-warn[_ngcontent-%COMP%], .mat-mdc-outlined-button.mat-warn[_ngcontent-%COMP%], .mat-tonal-button.mat-warn[_ngcontent-%COMP%]{--mat-button-filled-container-color: #f44336;--mat-button-filled-label-text-color: white;--mat-button-filled-ripple-color: rgba(255, 255, 255, .1);--mat-button-filled-state-layer-color: white;--mat-button-outlined-label-text-color: #f44336;--mat-button-outlined-outline-color: rgba(0, 0, 0, .12);--mat-button-outlined-ripple-color: rgba(244, 67, 54, .1);--mat-button-outlined-state-layer-color: #f44336;--mat-button-protected-container-color: #f44336;--mat-button-protected-label-text-color: white;--mat-button-protected-ripple-color: rgba(255, 255, 255, .1);--mat-button-protected-state-layer-color: white;--mat-button-text-label-text-color: #f44336;--mat-button-text-ripple-color: rgba(244, 67, 54, .1);--mat-button-text-state-layer-color: #f44336;--mat-button-tonal-container-color: #f44336;--mat-button-tonal-label-text-color: white;--mat-button-tonal-ripple-color: rgba(255, 255, 255, .1);--mat-button-tonal-state-layer-color: white}html[_ngcontent-%COMP%]{--mat-button-filled-container-height: 36px;--mat-button-filled-touch-target-display: block;--mat-button-outlined-container-height: 36px;--mat-button-outlined-touch-target-display: block;--mat-button-protected-container-height: 36px;--mat-button-protected-touch-target-display: block;--mat-button-text-container-height: 36px;--mat-button-text-touch-target-display: block;--mat-button-tonal-container-height: 36px;--mat-button-tonal-touch-target-display: block}html[_ngcontent-%COMP%]{--mat-button-filled-label-text-font: Roboto, sans-serif;--mat-button-filled-label-text-size: 14px;--mat-button-filled-label-text-tracking: .0892857143em;--mat-button-filled-label-text-transform: none;--mat-button-filled-label-text-weight: 500;--mat-button-outlined-label-text-font: Roboto, sans-serif;--mat-button-outlined-label-text-size: 14px;--mat-button-outlined-label-text-tracking: .0892857143em;--mat-button-outlined-label-text-transform: none;--mat-button-outlined-label-text-weight: 500;--mat-button-protected-label-text-font: Roboto, sans-serif;--mat-button-protected-label-text-size: 14px;--mat-button-protected-label-text-tracking: .0892857143em;--mat-button-protected-label-text-transform: none;--mat-button-protected-label-text-weight: 500;--mat-button-text-label-text-font: Roboto, sans-serif;--mat-button-text-label-text-size: 14px;--mat-button-text-label-text-tracking: .0892857143em;--mat-button-text-label-text-transform: none;--mat-button-text-label-text-weight: 500;--mat-button-tonal-label-text-font: Roboto, sans-serif;--mat-button-tonal-label-text-size: 14px;--mat-button-tonal-label-text-tracking: .0892857143em;--mat-button-tonal-label-text-transform: none;--mat-button-tonal-label-text-weight: 500}html[_ngcontent-%COMP%]{--mat-icon-button-icon-size: 24px;--mat-icon-button-container-shape: 50%}html[_ngcontent-%COMP%]{--mat-icon-button-disabled-icon-color: rgba(0, 0, 0, .38);--mat-icon-button-disabled-state-layer-color: black;--mat-icon-button-focus-state-layer-opacity: .12;--mat-icon-button-hover-state-layer-opacity: .04;--mat-icon-button-icon-color: inherit;--mat-icon-button-pressed-state-layer-opacity: .12;--mat-icon-button-ripple-color: rgba(0, 0, 0, .1);--mat-icon-button-state-layer-color: black}html[_ngcontent-%COMP%]   .mat-mdc-icon-button.mat-primary[_ngcontent-%COMP%]{--mat-icon-button-icon-color: #3f51b5;--mat-icon-button-state-layer-color: #3f51b5;--mat-icon-button-ripple-color: rgba(63, 81, 181, .1)}html[_ngcontent-%COMP%]   .mat-mdc-icon-button.mat-accent[_ngcontent-%COMP%]{--mat-icon-button-icon-color: #ff4081;--mat-icon-button-state-layer-color: #ff4081;--mat-icon-button-ripple-color: rgba(255, 64, 129, .1)}html[_ngcontent-%COMP%]   .mat-mdc-icon-button.mat-warn[_ngcontent-%COMP%]{--mat-icon-button-icon-color: #f44336;--mat-icon-button-state-layer-color: #f44336;--mat-icon-button-ripple-color: rgba(244, 67, 54, .1)}html[_ngcontent-%COMP%]{--mat-icon-button-touch-target-display: block}.mat-mdc-icon-button.mat-mdc-button-base[_ngcontent-%COMP%]{--mdc-icon-button-state-layer-size: 48px;--mat-icon-button-state-layer-size: 48px;width:var(--mat-icon-button-state-layer-size);height:var(--mat-icon-button-state-layer-size);padding:12px}html[_ngcontent-%COMP%]{--mat-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-fab-container-shape: 50%;--mat-fab-extended-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-fab-extended-container-height: 48px;--mat-fab-extended-container-shape: 24px;--mat-fab-extended-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-extended-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-extended-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-fab-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-fab-small-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-fab-small-container-shape: 50%;--mat-fab-small-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-small-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-small-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-fab-container-color: white;--mat-fab-disabled-state-container-color: rgba(0, 0, 0, .12);--mat-fab-disabled-state-foreground-color: rgba(0, 0, 0, .38);--mat-fab-disabled-state-layer-color: black;--mat-fab-focus-state-layer-opacity: .12;--mat-fab-foreground-color: black;--mat-fab-hover-state-layer-opacity: .04;--mat-fab-pressed-state-layer-opacity: .12;--mat-fab-ripple-color: rgba(0, 0, 0, .1);--mat-fab-small-container-color: white;--mat-fab-small-disabled-state-container-color: rgba(0, 0, 0, .12);--mat-fab-small-disabled-state-foreground-color: rgba(0, 0, 0, .38);--mat-fab-small-disabled-state-layer-color: black;--mat-fab-small-focus-state-layer-opacity: .12;--mat-fab-small-foreground-color: black;--mat-fab-small-hover-state-layer-opacity: .04;--mat-fab-small-pressed-state-layer-opacity: .12;--mat-fab-small-ripple-color: rgba(0, 0, 0, .1);--mat-fab-small-state-layer-color: black;--mat-fab-state-layer-color: black}html[_ngcontent-%COMP%]   .mat-mdc-fab.mat-primary[_ngcontent-%COMP%], html[_ngcontent-%COMP%]   .mat-mdc-mini-fab.mat-primary[_ngcontent-%COMP%]{--mat-fab-container-color: #3f51b5;--mat-fab-foreground-color: white;--mat-fab-ripple-color: rgba(255, 255, 255, .1);--mat-fab-small-container-color: #3f51b5;--mat-fab-small-foreground-color: white;--mat-fab-small-ripple-color: rgba(255, 255, 255, .1);--mat-fab-small-state-layer-color: white;--mat-fab-state-layer-color: white}html[_ngcontent-%COMP%]   .mat-mdc-fab.mat-accent[_ngcontent-%COMP%], html[_ngcontent-%COMP%]   .mat-mdc-mini-fab.mat-accent[_ngcontent-%COMP%]{--mat-fab-container-color: #ff4081;--mat-fab-foreground-color: white;--mat-fab-ripple-color: rgba(255, 255, 255, .1);--mat-fab-small-container-color: #ff4081;--mat-fab-small-foreground-color: white;--mat-fab-small-ripple-color: rgba(255, 255, 255, .1);--mat-fab-small-state-layer-color: white;--mat-fab-state-layer-color: white}html[_ngcontent-%COMP%]   .mat-mdc-fab.mat-warn[_ngcontent-%COMP%], html[_ngcontent-%COMP%]   .mat-mdc-mini-fab.mat-warn[_ngcontent-%COMP%]{--mat-fab-container-color: #f44336;--mat-fab-foreground-color: white;--mat-fab-ripple-color: rgba(255, 255, 255, .1);--mat-fab-small-container-color: #f44336;--mat-fab-small-foreground-color: white;--mat-fab-small-ripple-color: rgba(255, 255, 255, .1);--mat-fab-small-state-layer-color: white;--mat-fab-state-layer-color: white}html[_ngcontent-%COMP%]{--mat-fab-small-touch-target-display: block;--mat-fab-touch-target-display: block}html[_ngcontent-%COMP%]{--mat-fab-extended-label-text-font: Roboto, sans-serif;--mat-fab-extended-label-text-size: 14px;--mat-fab-extended-label-text-tracking: .0892857143em;--mat-fab-extended-label-text-weight: 500}html[_ngcontent-%COMP%]{--mat-snack-bar-container-shape: 4px}html[_ngcontent-%COMP%]{--mat-snack-bar-container-color: #333333;--mat-snack-bar-supporting-text-color: rgba(255, 255, 255, .87);--mat-snack-bar-button-color: #c5cae9}html[_ngcontent-%COMP%]{--mat-snack-bar-supporting-text-font: Roboto, sans-serif;--mat-snack-bar-supporting-text-line-height: 20px;--mat-snack-bar-supporting-text-size: 14px;--mat-snack-bar-supporting-text-weight: 400}html[_ngcontent-%COMP%]{--mat-table-row-item-outline-width: 1px}html[_ngcontent-%COMP%]{--mat-table-background-color: white;--mat-table-header-headline-color: rgba(0, 0, 0, .87);--mat-table-row-item-label-text-color: rgba(0, 0, 0, .87);--mat-table-row-item-outline-color: rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-table-header-container-height: 56px;--mat-table-footer-container-height: 52px;--mat-table-row-item-container-height: 52px}html[_ngcontent-%COMP%]{--mat-table-header-headline-font: Roboto, sans-serif;--mat-table-header-headline-line-height: 22px;--mat-table-header-headline-size: 14px;--mat-table-header-headline-weight: 500;--mat-table-header-headline-tracking: .0071428571em;--mat-table-row-item-label-text-font: Roboto, sans-serif;--mat-table-row-item-label-text-line-height: 20px;--mat-table-row-item-label-text-size: 14px;--mat-table-row-item-label-text-weight: 400;--mat-table-row-item-label-text-tracking: .0178571429em;--mat-table-footer-supporting-text-font: Roboto, sans-serif;--mat-table-footer-supporting-text-line-height: 20px;--mat-table-footer-supporting-text-size: 14px;--mat-table-footer-supporting-text-weight: 400;--mat-table-footer-supporting-text-tracking: .0178571429em}html[_ngcontent-%COMP%]{--mat-progress-spinner-active-indicator-width: 4px;--mat-progress-spinner-size: 48px}html[_ngcontent-%COMP%]{--mat-progress-spinner-active-indicator-color: #3f51b5}html[_ngcontent-%COMP%]   .mat-accent[_ngcontent-%COMP%]{--mat-progress-spinner-active-indicator-color: #ff4081}html[_ngcontent-%COMP%]   .mat-warn[_ngcontent-%COMP%]{--mat-progress-spinner-active-indicator-color: #f44336}html[_ngcontent-%COMP%]{--mat-badge-container-shape: 50%;--mat-badge-container-size: unset;--mat-badge-small-size-container-size: unset;--mat-badge-large-size-container-size: unset;--mat-badge-legacy-container-size: 22px;--mat-badge-legacy-small-size-container-size: 16px;--mat-badge-legacy-large-size-container-size: 28px;--mat-badge-container-offset: -11px 0;--mat-badge-small-size-container-offset: -8px 0;--mat-badge-large-size-container-offset: -14px 0;--mat-badge-container-overlap-offset: -11px;--mat-badge-small-size-container-overlap-offset: -8px;--mat-badge-large-size-container-overlap-offset: -14px;--mat-badge-container-padding: 0;--mat-badge-small-size-container-padding: 0;--mat-badge-large-size-container-padding: 0}html[_ngcontent-%COMP%]{--mat-badge-background-color: #3f51b5;--mat-badge-text-color: white;--mat-badge-disabled-state-background-color: #b9b9b9;--mat-badge-disabled-state-text-color: rgba(0, 0, 0, .38)}.mat-badge-accent[_ngcontent-%COMP%]{--mat-badge-background-color: #ff4081;--mat-badge-text-color: white}.mat-badge-warn[_ngcontent-%COMP%]{--mat-badge-background-color: #f44336;--mat-badge-text-color: white}html[_ngcontent-%COMP%]{--mat-badge-text-font: Roboto, sans-serif;--mat-badge-line-height: 22px;--mat-badge-text-size: 12px;--mat-badge-text-weight: 600;--mat-badge-small-size-text-size: 9px;--mat-badge-small-size-line-height: 16px;--mat-badge-large-size-text-size: 24px;--mat-badge-large-size-line-height: 28px}html[_ngcontent-%COMP%]{--mat-bottom-sheet-container-shape: 4px}html[_ngcontent-%COMP%]{--mat-bottom-sheet-container-text-color: rgba(0, 0, 0, .87);--mat-bottom-sheet-container-background-color: white}html[_ngcontent-%COMP%]{--mat-bottom-sheet-container-text-font: Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height: 20px;--mat-bottom-sheet-container-text-size: 14px;--mat-bottom-sheet-container-text-tracking: .0178571429em;--mat-bottom-sheet-container-text-weight: 400}html[_ngcontent-%COMP%]{--mat-button-toggle-focus-state-layer-opacity: .12;--mat-button-toggle-hover-state-layer-opacity: .04;--mat-button-toggle-legacy-focus-state-layer-opacity: 1;--mat-button-toggle-legacy-height: 36px;--mat-button-toggle-legacy-shape: 2px;--mat-button-toggle-shape: 4px}html[_ngcontent-%COMP%]{--mat-button-toggle-background-color: white;--mat-button-toggle-disabled-selected-state-background-color: #bdbdbd;--mat-button-toggle-disabled-selected-state-text-color: rgba(0, 0, 0, .87);--mat-button-toggle-disabled-state-background-color: white;--mat-button-toggle-disabled-state-text-color: rgba(0, 0, 0, .26);--mat-button-toggle-divider-color: rgb(224.4, 224.4, 224.4);--mat-button-toggle-legacy-disabled-selected-state-background-color: #bdbdbd;--mat-button-toggle-legacy-disabled-state-background-color: #eeeeee;--mat-button-toggle-legacy-disabled-state-text-color: rgba(0, 0, 0, .26);--mat-button-toggle-legacy-selected-state-background-color: #e0e0e0;--mat-button-toggle-legacy-selected-state-text-color: rgba(0, 0, 0, .54);--mat-button-toggle-legacy-state-layer-color: rgba(0, 0, 0, .12);--mat-button-toggle-legacy-text-color: rgba(0, 0, 0, .38);--mat-button-toggle-selected-state-background-color: #e0e0e0;--mat-button-toggle-selected-state-text-color: rgba(0, 0, 0, .87);--mat-button-toggle-state-layer-color: black;--mat-button-toggle-text-color: rgba(0, 0, 0, .87)}html[_ngcontent-%COMP%]{--mat-button-toggle-height: 48px}html[_ngcontent-%COMP%]{--mat-button-toggle-label-text-font: Roboto, sans-serif;--mat-button-toggle-label-text-line-height: 24px;--mat-button-toggle-label-text-size: 16px;--mat-button-toggle-label-text-tracking: .03125em;--mat-button-toggle-label-text-weight: 400;--mat-button-toggle-legacy-label-text-font: Roboto, sans-serif;--mat-button-toggle-legacy-label-text-line-height: 24px;--mat-button-toggle-legacy-label-text-size: 16px;--mat-button-toggle-legacy-label-text-tracking: .03125em;--mat-button-toggle-legacy-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-datepicker-calendar-container-shape: 4px;--mat-datepicker-calendar-container-touch-shape: 4px;--mat-datepicker-calendar-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-datepicker-calendar-container-touch-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #3f51b5;--mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(63, 81, 181, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: rgba(63, 81, 181, .3);--mat-datepicker-calendar-date-hover-state-background-color: rgba(63, 81, 181, .3);--mat-datepicker-toggle-active-state-icon-color: #3f51b5;--mat-datepicker-calendar-date-in-range-state-background-color: rgba(63, 81, 181, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: rgb(69.5241935484, 163.4758064516, 93.9516129032);--mat-datepicker-toggle-icon-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-body-label-text-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-period-button-text-color: black;--mat-datepicker-calendar-period-button-icon-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-navigation-button-icon-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-header-divider-color: rgba(0, 0, 0, .12);--mat-datepicker-calendar-header-text-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-date-today-outline-color: rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-today-disabled-state-outline-color: rgba(0, 0, 0, .18);--mat-datepicker-calendar-date-text-color: rgba(0, 0, 0, .87);--mat-datepicker-calendar-date-outline-color: transparent;--mat-datepicker-calendar-date-disabled-state-text-color: rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-preview-state-outline-color: rgba(0, 0, 0, .24);--mat-datepicker-range-input-separator-color: rgba(0, 0, 0, .87);--mat-datepicker-range-input-disabled-state-separator-color: rgba(0, 0, 0, .38);--mat-datepicker-range-input-disabled-state-text-color: rgba(0, 0, 0, .38);--mat-datepicker-calendar-container-background-color: white;--mat-datepicker-calendar-container-text-color: rgba(0, 0, 0, .87)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]{--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #ff4081;--mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(255, 64, 129, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: rgba(255, 64, 129, .3);--mat-datepicker-calendar-date-hover-state-background-color: rgba(255, 64, 129, .3);--mat-datepicker-calendar-date-in-range-state-background-color: rgba(255, 64, 129, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: rgb(69.5241935484, 163.4758064516, 93.9516129032)}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]{--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #f44336;--mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(244, 67, 54, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: rgba(244, 67, 54, .3);--mat-datepicker-calendar-date-hover-state-background-color: rgba(244, 67, 54, .3);--mat-datepicker-calendar-date-in-range-state-background-color: rgba(244, 67, 54, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: rgb(69.5241935484, 163.4758064516, 93.9516129032)}.mat-datepicker-toggle-active.mat-accent[_ngcontent-%COMP%]{--mat-datepicker-toggle-active-state-icon-color: #ff4081}.mat-datepicker-toggle-active.mat-warn[_ngcontent-%COMP%]{--mat-datepicker-toggle-active-state-icon-color: #f44336}.mat-calendar-controls[_ngcontent-%COMP%]{--mat-icon-button-touch-target-display: none}.mat-calendar-controls[_ngcontent-%COMP%]   .mat-mdc-icon-button.mat-mdc-button-base[_ngcontent-%COMP%]{--mdc-icon-button-state-layer-size: 40px;--mat-icon-button-state-layer-size: 40px;width:var(--mat-icon-button-state-layer-size);height:var(--mat-icon-button-state-layer-size);padding:8px}html[_ngcontent-%COMP%]{--mat-datepicker-calendar-text-font: Roboto, sans-serif;--mat-datepicker-calendar-text-size: 13px;--mat-datepicker-calendar-body-label-text-size: 14px;--mat-datepicker-calendar-body-label-text-weight: 500;--mat-datepicker-calendar-period-button-text-size: 14px;--mat-datepicker-calendar-period-button-text-weight: 500;--mat-datepicker-calendar-header-text-size: 11px;--mat-datepicker-calendar-header-text-weight: 400}html[_ngcontent-%COMP%]{--mat-divider-width: 1px}html[_ngcontent-%COMP%]{--mat-divider-color: rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-expansion-container-shape: 4px;--mat-expansion-legacy-header-indicator-display: inline-block;--mat-expansion-header-indicator-display: none}html[_ngcontent-%COMP%]{--mat-expansion-container-background-color: white;--mat-expansion-container-text-color: rgba(0, 0, 0, .87);--mat-expansion-actions-divider-color: rgba(0, 0, 0, .12);--mat-expansion-header-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-expansion-header-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-expansion-header-disabled-state-text-color: rgba(0, 0, 0, .26);--mat-expansion-header-text-color: rgba(0, 0, 0, .87);--mat-expansion-header-description-color: rgba(0, 0, 0, .54);--mat-expansion-header-indicator-color: rgba(0, 0, 0, .54)}html[_ngcontent-%COMP%]{--mat-expansion-header-collapsed-state-height: 48px;--mat-expansion-header-expanded-state-height: 64px}html[_ngcontent-%COMP%]{--mat-expansion-header-text-font: Roboto, sans-serif;--mat-expansion-header-text-size: 14px;--mat-expansion-header-text-weight: 500;--mat-expansion-header-text-line-height: inherit;--mat-expansion-header-text-tracking: inherit;--mat-expansion-container-text-font: Roboto, sans-serif;--mat-expansion-container-text-line-height: 20px;--mat-expansion-container-text-size: 14px;--mat-expansion-container-text-tracking: .0178571429em;--mat-expansion-container-text-weight: 400}html[_ngcontent-%COMP%]{--mat-grid-list-tile-header-primary-text-size: 14px;--mat-grid-list-tile-header-secondary-text-size: 12px;--mat-grid-list-tile-footer-primary-text-size: 14px;--mat-grid-list-tile-footer-secondary-text-size: 12px}html[_ngcontent-%COMP%]{--mat-icon-color: inherit}.mat-icon.mat-primary[_ngcontent-%COMP%]{--mat-icon-color: #3f51b5}.mat-icon.mat-accent[_ngcontent-%COMP%]{--mat-icon-color: #ff4081}.mat-icon.mat-warn[_ngcontent-%COMP%]{--mat-icon-color: #f44336}html[_ngcontent-%COMP%]{--mat-sidenav-container-shape: 0;--mat-sidenav-container-elevation-shadow: 0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-sidenav-container-width: auto}html[_ngcontent-%COMP%]{--mat-sidenav-container-divider-color: rgba(0, 0, 0, .12);--mat-sidenav-container-background-color: white;--mat-sidenav-container-text-color: rgba(0, 0, 0, .87);--mat-sidenav-content-background-color: #fafafa;--mat-sidenav-content-text-color: rgba(0, 0, 0, .87);--mat-sidenav-scrim-color: rgba(0, 0, 0, .6)}html[_ngcontent-%COMP%]{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #3f51b5;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #3f51b5;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #3f51b5;--mat-stepper-header-edit-state-icon-foreground-color: white;--mat-stepper-container-color: white;--mat-stepper-line-color: rgba(0, 0, 0, .12);--mat-stepper-header-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-stepper-header-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-stepper-header-label-text-color: rgba(0, 0, 0, .54);--mat-stepper-header-optional-label-text-color: rgba(0, 0, 0, .54);--mat-stepper-header-selected-state-label-text-color: rgba(0, 0, 0, .87);--mat-stepper-header-error-state-label-text-color: #f44336;--mat-stepper-header-icon-background-color: rgba(0, 0, 0, .54);--mat-stepper-header-error-state-icon-foreground-color: #f44336;--mat-stepper-header-error-state-icon-background-color: transparent}html[_ngcontent-%COMP%]   .mat-step-header.mat-accent[_ngcontent-%COMP%]{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #ff4081;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #ff4081;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #ff4081;--mat-stepper-header-edit-state-icon-foreground-color: white}html[_ngcontent-%COMP%]   .mat-step-header.mat-warn[_ngcontent-%COMP%]{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #f44336;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #f44336;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #f44336;--mat-stepper-header-edit-state-icon-foreground-color: white}html[_ngcontent-%COMP%]{--mat-stepper-header-height: 72px}html[_ngcontent-%COMP%]{--mat-stepper-container-text-font: Roboto, sans-serif;--mat-stepper-header-label-text-font: Roboto, sans-serif;--mat-stepper-header-label-text-size: 14px;--mat-stepper-header-label-text-weight: 400;--mat-stepper-header-error-state-label-text-size: 16px;--mat-stepper-header-selected-state-label-text-size: 16px;--mat-stepper-header-selected-state-label-text-weight: 400}html[_ngcontent-%COMP%]{--mat-sort-arrow-color: rgb(117.3, 117.3, 117.3)}html[_ngcontent-%COMP%]{--mat-toolbar-container-background-color: whitesmoke;--mat-toolbar-container-text-color: rgba(0, 0, 0, .87)}.mat-toolbar.mat-primary[_ngcontent-%COMP%]{--mat-toolbar-container-background-color: #3f51b5;--mat-toolbar-container-text-color: white}.mat-toolbar.mat-accent[_ngcontent-%COMP%]{--mat-toolbar-container-background-color: #ff4081;--mat-toolbar-container-text-color: white}.mat-toolbar.mat-warn[_ngcontent-%COMP%]{--mat-toolbar-container-background-color: #f44336;--mat-toolbar-container-text-color: white}html[_ngcontent-%COMP%]{--mat-toolbar-standard-height: 64px;--mat-toolbar-mobile-height: 56px}html[_ngcontent-%COMP%]{--mat-toolbar-title-text-font: Roboto, sans-serif;--mat-toolbar-title-text-line-height: 32px;--mat-toolbar-title-text-size: 20px;--mat-toolbar-title-text-tracking: .0125em;--mat-toolbar-title-text-weight: 500}html[_ngcontent-%COMP%]{--mat-tree-container-background-color: white;--mat-tree-node-text-color: rgba(0, 0, 0, .87)}html[_ngcontent-%COMP%]{--mat-tree-node-min-height: 48px}html[_ngcontent-%COMP%]{--mat-tree-node-text-font: Roboto, sans-serif;--mat-tree-node-text-size: 14px;--mat-tree-node-text-weight: 400}html[_ngcontent-%COMP%]{--mat-timepicker-container-shape: 4px;--mat-timepicker-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html[_ngcontent-%COMP%]{--mat-timepicker-container-background-color: white}.mat-h1[_ngcontent-%COMP%], .mat-headline-5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-headline-5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font:400 24px/32px Roboto,sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2[_ngcontent-%COMP%], .mat-headline-6[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-headline-6[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font:500 20px/32px Roboto,sans-serif;letter-spacing:.0125em;margin:0 0 16px}.mat-h3[_ngcontent-%COMP%], .mat-subtitle-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h3[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-subtitle-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font:400 16px/28px Roboto,sans-serif;letter-spacing:.009375em;margin:0 0 16px}.mat-h4[_ngcontent-%COMP%], .mat-body-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h4[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-body-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font:400 16px/24px Roboto,sans-serif;letter-spacing:.03125em;margin:0 0 16px}.mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font:400 11.62px/20px Roboto,sans-serif;margin:0 0 12px}.mat-h6[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h6[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font:400 9.38px/20px Roboto,sans-serif;margin:0 0 12px}.mat-body-strong[_ngcontent-%COMP%], .mat-subtitle-2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-body-strong[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-subtitle-2[_ngcontent-%COMP%]{font:500 14px/22px Roboto,sans-serif;letter-spacing:.0071428571em}.mat-body[_ngcontent-%COMP%], .mat-body-2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-body[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-body-2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]{font:400 14px/20px Roboto,sans-serif;letter-spacing:.0178571429em}.mat-body[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mat-body-2[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-body[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-body-2[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px}.mat-small[_ngcontent-%COMP%], .mat-caption[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-small[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-caption[_ngcontent-%COMP%]{font:400 12px/20px Roboto,sans-serif;letter-spacing:.0333333333em}.mat-headline-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-headline-1[_ngcontent-%COMP%]{font:300 96px/96px Roboto,sans-serif;letter-spacing:-.015625em;margin:0 0 56px}.mat-headline-2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-headline-2[_ngcontent-%COMP%]{font:300 60px/60px Roboto,sans-serif;letter-spacing:-.0083333333em;margin:0 0 64px}.mat-headline-3[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-headline-3[_ngcontent-%COMP%]{font:400 48px/50px Roboto,sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-headline-4[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-headline-4[_ngcontent-%COMP%]{font:400 34px/40px Roboto,sans-serif;letter-spacing:.0073529412em;margin:0 0 64px}*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{height:100%;font-family:Roboto,sans-serif}body[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);min-height:100vh}[_ngcontent-%COMP%]:root{--primary-color: #3f51b5;--accent-color: #ff4081;--warn-color: #f44336;--success-color: #4caf50;--info-color: #2196f3;--warning-color: #ff9800}.text-center[_ngcontent-%COMP%]{text-align:center}.text-left[_ngcontent-%COMP%]{text-align:left}.text-right[_ngcontent-%COMP%]{text-align:right}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.mt-4[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-5[_ngcontent-%COMP%]{margin-top:3rem}.mb-1[_ngcontent-%COMP%]{margin-bottom:.25rem}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1.5rem}.mb-5[_ngcontent-%COMP%]{margin-bottom:3rem}.p-1[_ngcontent-%COMP%]{padding:.25rem}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-3[_ngcontent-%COMP%]{padding:1rem}.p-4[_ngcontent-%COMP%]{padding:1.5rem}.p-5[_ngcontent-%COMP%]{padding:3rem}.w-100[_ngcontent-%COMP%]{width:100%}.h-100[_ngcontent-%COMP%]{height:100%}.auth-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;padding:20px}.auth-card[_ngcontent-%COMP%]{max-width:400px;width:100%;background:#fff;border-radius:12px;box-shadow:0 8px 32px #0000001a;overflow:hidden}.auth-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-color),var(--accent-color));color:#fff;padding:2rem;text-align:center}.auth-content[_ngcontent-%COMP%]{padding:2rem}.form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.submit-button[_ngcontent-%COMP%]{width:100%;height:48px;margin-top:1rem}.divider[_ngcontent-%COMP%]{text-align:center;margin:1.5rem 0;position:relative}.divider[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:0;right:0;height:1px;background:#e0e0e0}.divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#fff;padding:0 1rem;color:#666;font-size:.875rem}.error-message[_ngcontent-%COMP%]{color:var(--warn-color);font-size:.875rem;margin-top:.5rem}.success-message[_ngcontent-%COMP%]{color:var(--success-color);font-size:.875rem;margin-top:.5rem}.loading-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#00000080;display:flex;align-items:center;justify-content:center;z-index:9999}.payment-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:2rem;box-shadow:0 4px 16px #0000001a;margin-bottom:1rem}.currency-selector[_ngcontent-%COMP%]{margin-bottom:1rem}.amount-input[_ngcontent-%COMP%]{font-size:1.5rem;text-align:center}.payment-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--success-color),#66bb6a);color:#fff;width:100%;height:56px;font-size:1.1rem;font-weight:500}.payment-history[_ngcontent-%COMP%]{margin-top:2rem}.payment-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;border-bottom:1px solid #e0e0e0}.payment-status[_ngcontent-%COMP%]{padding:.25rem .75rem;border-radius:16px;font-size:.75rem;font-weight:500;text-transform:uppercase}.status-paid[_ngcontent-%COMP%]{background:#e8f5e8;color:var(--success-color)}.status-pending[_ngcontent-%COMP%]{background:#fff3e0;color:var(--warning-color)}.status-failed[_ngcontent-%COMP%]{background:#ffebee;color:var(--warn-color)}@media (max-width: 768px){.auth-container[_ngcontent-%COMP%]{padding:10px}.auth-card[_ngcontent-%COMP%]{margin:0}.auth-header[_ngcontent-%COMP%], .auth-content[_ngcontent-%COMP%]{padding:1.5rem}}.fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translate(-100%)}to{transform:translate(0)}}.security-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;background:#e8f5e8;color:var(--success-color);padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:500}.security-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;margin-right:.25rem}.my-3[_ngcontent-%COMP%]{margin:1.5rem 0}.form-row[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem}.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%]{flex:0 0 120px}.form-row[_ngcontent-%COMP%]   .amount-field[_ngcontent-%COMP%]{flex:1}.test-amounts[_ngcontent-%COMP%]{margin-bottom:1.5rem}.test-amounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:1rem;color:#333}.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:.5rem}.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:.75rem;font-size:.875rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#333;margin-bottom:.75rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0;color:#666}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:.5rem;margin-bottom:1rem}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]{background:#f8f9fa;padding:.75rem;border-radius:6px;font-family:monospace;font-size:.875rem}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%]{flex:1}.amount-buttons[_ngcontent-%COMP%], .test-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n    });\n  }\n  return PaymentTestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}