=== Page 124 ===

124Authentication
Masonite makes authentication really simply.
Masonite comes with a command to scaffold out a basic authentication system. You
may use this as a great starting point for adding authentication to your application. This
command will create controllers, views, and mailables for you.
If you would like to implement your own authentication from scratch you can skip to the
sections below.
First run the command to add the news files:
Then add the authentication routes to your routes file:
You may then go to the /login or /register route to implement your authentication.
The configuration for Masonite's authentication is quite simple:python craft auth
from masonite.authentication import Auth
ROUTES = [
  # routes
]
ROUTES += Auth.routes()Authentication Scaffold Command
Configuration6/12/25, 3:02 AM Masonite Documentation