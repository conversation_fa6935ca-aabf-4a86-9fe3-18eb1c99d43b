"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlJob = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const user_model_1 = require("./user.model");
const crawled_content_model_1 = require("./crawled-content.model");
const generated_document_model_1 = require("./generated-document.model");
let CrawlJob = class CrawlJob extends repository_1.Entity {
    constructor(data) {
        super(data);
    }
};
exports.CrawlJob = CrawlJob;
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        id: true,
        generated: true,
    }),
    tslib_1.__metadata("design:type", String)
], CrawlJob.prototype, "id", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        jsonSchema: {
            format: 'uri',
            minLength: 10,
            maxLength: 2000,
            errorMessage: 'URL should be a valid URL',
        },
    }),
    tslib_1.__metadata("design:type", String)
], CrawlJob.prototype, "url", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'pending',
        jsonSchema: {
            enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'paused'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], CrawlJob.prototype, "status", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 2,
        jsonSchema: {
            minimum: 1,
            maximum: 10,
            errorMessage: 'Crawl depth should be between 1 and 10',
        },
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "maxDepth", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 100,
        jsonSchema: {
            minimum: 1,
            maximum: 10000,
            errorMessage: 'Max pages should be between 1 and 10000',
        },
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "maxPages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'array',
        itemType: 'string',
        default: ['text/html'],
        jsonSchema: {
            items: {
                type: 'string',
                enum: ['text/html', 'application/pdf', 'text/plain', 'application/json', 'text/css', 'application/javascript'],
            },
        },
    }),
    tslib_1.__metadata("design:type", Array)
], CrawlJob.prototype, "allowedContentTypes", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'array',
        itemType: 'string',
        default: [],
        jsonSchema: {
            items: {
                type: 'string',
                pattern: '^[a-zA-Z0-9*._-]+$',
            },
        },
    }),
    tslib_1.__metadata("design:type", Array)
], CrawlJob.prototype, "excludePatterns", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'array',
        itemType: 'string',
        default: [],
        jsonSchema: {
            items: {
                type: 'string',
                pattern: '^[a-zA-Z0-9*._-]+$',
            },
        },
    }),
    tslib_1.__metadata("design:type", Array)
], CrawlJob.prototype, "includePatterns", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'boolean',
        default: true,
    }),
    tslib_1.__metadata("design:type", Boolean)
], CrawlJob.prototype, "followExternalLinks", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'boolean',
        default: false,
    }),
    tslib_1.__metadata("design:type", Boolean)
], CrawlJob.prototype, "respectRobotsTxt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 1000,
        jsonSchema: {
            minimum: 100,
            maximum: 10000,
            errorMessage: 'Delay should be between 100ms and 10000ms',
        },
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "delayBetweenRequests", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
        default: {},
    }),
    tslib_1.__metadata("design:type", Object)
], CrawlJob.prototype, "crawlOptions", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "totalPages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "processedPages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "failedPages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
        jsonSchema: {
            minimum: 0,
            maximum: 100,
        },
    }),
    tslib_1.__metadata("design:type", Number)
], CrawlJob.prototype, "progressPercentage", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
    }),
    tslib_1.__metadata("design:type", String)
], CrawlJob.prototype, "errorMessage", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
    }),
    tslib_1.__metadata("design:type", Object)
], CrawlJob.prototype, "crawlStatistics", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'started_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawlJob.prototype, "startedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'completed_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawlJob.prototype, "completedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'created_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawlJob.prototype, "createdAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'updated_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawlJob.prototype, "updatedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.belongsTo)(() => user_model_1.User, {}, {
        postgresql: {
            columnName: 'user_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawlJob.prototype, "userId", void 0);
tslib_1.__decorate([
    (0, repository_1.hasMany)(() => crawled_content_model_1.CrawledContent, { keyTo: 'crawlJobId' }),
    tslib_1.__metadata("design:type", Array)
], CrawlJob.prototype, "crawledContents", void 0);
tslib_1.__decorate([
    (0, repository_1.hasMany)(() => generated_document_model_1.GeneratedDocument, { keyTo: 'crawlJobId' }),
    tslib_1.__metadata("design:type", Array)
], CrawlJob.prototype, "generatedDocuments", void 0);
exports.CrawlJob = CrawlJob = tslib_1.__decorate([
    (0, repository_1.model)({
        settings: {
            strict: true,
            indexes: {
                userIdIndex: {
                    keys: {
                        userId: 1,
                    },
                },
                statusIndex: {
                    keys: {
                        status: 1,
                    },
                },
                createdAtIndex: {
                    keys: {
                        createdAt: -1,
                    },
                },
            },
            postgresql: {
                schema: 'public',
                table: 'crawl_job'
            }
        },
    }),
    tslib_1.__metadata("design:paramtypes", [Object])
], CrawlJob);
//# sourceMappingURL=crawl-job.model.js.map