=== Page 343 ===

343•assertSentTo(notifiable, notification_class, callable_assert=None, count=None)
•assertLast(callable_assert)
•assertNotSentTo(notifiable, notification_class)
On Notifications instances:
•assertSentVia(*drivers)
•assertEqual(value, reference)
•assertNotEqual(value, reference)
•assertIn(value, container)
Available helpers are:
•resetCount()
•last()
For mocking any piece of code in Python you can use the standard unittest.mock
module. You can find more information in unittest documentation.
Here is basic example
For mocking external HTTP requests you can use the responses module. You can find
more information in responses documentation.
from unittest.mock import patch
with patch("some.module") as SomeClass:
    SomeClass.return_value.my_method.return_value = 0
    self.assertEqual(SomeClass().my_method(), 0)
Basic Python mocks6/12/25, 3:02 AM Masonite Documentation