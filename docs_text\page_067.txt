=== Page 68 ===

68You can redirect the user to any number of URL's.
First you can redirect to a simple URL:
You can redirect back to where the user came from:
If using the back method as part of a form request then you will need to use the back
view helper as well:
You can also redirect to a route by its name:
This will find a named route users.home like:from masonite.response import Response
def show(self, response: Response):
    return response.redirect('/home')
from masonite.response import Response
def show(self, response: Response):
    return response.back()
<form>
  {{ csrf_field }}
  {{ back() }}
  <input type="text">
  ...
</form>
from masonite.response import Response
def show(self, response: Response):
    return response.redirect(name='users.home')Redirecting6/12/25, 3:02 AM Masonite Documentation