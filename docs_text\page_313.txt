=== Page 314 ===

314Set a given pendulum instance to be returned when a now (or today, tomorrow
yesterday) instance is created. It's really useful during tests to check timestamps logic.
This allow to control which datetime will be returned to be able to always have an
expected behaviour in the tests.
When using those helpers you should not forget to reset the default pendulum behaviour
with restoreTime() helper to avoid breaking other tests. It can be done directly in the test
or in a tearDown() method.
Set the mocked time as tomorrow. (It's a shortcut to avoid doing 
self.fakeTime(pendulum.tomorrow())).
Set the mocked time as yesterday.
Set the mocked time as an offset of a given unit of time in the future. Unit can be
specified among pendulum units: seconds, minutes, hours, days (default), weeks,given_date = pendulum.datetime(2021, 2, 5)
self.fakeTime(given_date)
self.assertEqual(pendulum.now(), given_date)
tomorrow = pendulum.tomorrow()
self.fakeTimeTomorrow()
self.assertEqual(pendulum.now(), tomorrow)fakeTime
fakeTimeTomorrow
fakeTimeYesterday
fakeTimeInFuture6/12/25, 3:02 AM Masonite Documentation