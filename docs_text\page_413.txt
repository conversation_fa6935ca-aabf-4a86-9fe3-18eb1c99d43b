=== Page 414 ===

414Now you can simply return a tuple if you want to change the status code that gets
returned.
For example before we had to do something like this:
Now you can simply return a tuple:
Previously we converted the response to a string when the request was finished but this
prevented use cases where we wanted to return bytes (like returning an image or PDF).
Now the conversion is happens (or doesn't happen) internally before the WSGI server
needs to render a response. This results in a slight change in your application.
The CLI tool no longer needs to be installed as the first step. Now the first step would be
to install masonite which will give you access to craft. From there you can create a new
project.def show(self, response: Response):
    return response.json({'error': 'unauthenticated'}, status=401)
def show(self):
    return {'error': 'unauthenticated'}, 401Returning tuples inside controllers changes
status codes
Changed how the WSGI server returns
responses
Masonite CLI is now inside Masonite core.6/12/25, 3:02 AM Masonite Documentation