{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RateLimitService } from '../../services/rate-limit.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/rate-limit.service\";\nimport * as i2 from \"@angular/common\";\nfunction RateLimitNotificationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n    i0.ɵɵtext(7, \"Rate Limit Exceeded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 8);\n    i0.ɵɵtext(9, \"Too many requests - please wait\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 9);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵlistener(\"click\", function RateLimitNotificationComponent_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.dismissNotification());\n    });\n    i0.ɵɵelement(12, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"span\", 14);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 15);\n    i0.ɵɵtext(19, \"MIN\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 16);\n    i0.ɵɵtext(21, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 13)(23, \"span\", 14);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 15);\n    i0.ɵɵtext(26, \"SEC\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 17);\n    i0.ɵɵelement(28, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 19);\n    i0.ɵɵtext(30, \" Please wait \");\n    i0.ɵɵelementStart(31, \"strong\");\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" before making another request \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 20)(36, \"div\", 21);\n    i0.ɵɵelement(37, \"i\", 22);\n    i0.ɵɵelementStart(38, \"div\", 23)(39, \"p\")(40, \"strong\");\n    i0.ɵɵtext(41, \"Why am I seeing this?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43, \"Rate limiting protects our service from overload and ensures fair usage for all users.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(44, \"div\", 24)(45, \"button\", 25);\n    i0.ɵɵpipe(46, \"async\");\n    i0.ɵɵlistener(\"click\", function RateLimitNotificationComponent_div_0_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshPage());\n    });\n    i0.ɵɵelement(47, \"i\", 26);\n    i0.ɵɵtext(48, \" Try Again \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ((tmp_1_0 = i0.ɵɵpipeBind1(11, 7, ctx_r1.rateLimit$)) == null ? null : tmp_1_0.remainingTime) > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getMinutes());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getSeconds());\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatTime(((tmp_5_0 = i0.ɵɵpipeBind1(33, 9, ctx_r1.rateLimit$)) == null ? null : tmp_5_0.remainingTime) || 0));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"disabled\", ((tmp_6_0 = i0.ɵɵpipeBind1(46, 11, ctx_r1.rateLimit$)) == null ? null : tmp_6_0.remainingTime) > 0);\n  }\n}\nexport class RateLimitNotificationComponent {\n  constructor(rateLimitService) {\n    this.rateLimitService = rateLimitService;\n    this.rateLimit$ = this.rateLimitService.rateLimit$;\n  }\n  ngOnInit() {\n    console.log('🚀 RateLimitNotificationComponent initialized');\n    // Debug: Subscribe to rate limit changes\n    this.rateLimit$.subscribe(status => {\n      console.log('📊 Rate limit status changed:', status);\n    });\n  }\n  ngOnDestroy() {\n    // Cleanup if needed\n  }\n  /**\n   * Format time for display\n   */\n  formatTime(seconds) {\n    return RateLimitService.formatTimeRemaining(seconds);\n  }\n  /**\n   * Get minutes from remaining time\n   */\n  getMinutes() {\n    const status = this.rateLimitService.getCurrentStatus();\n    const minutes = Math.floor(status.remainingTime / 60);\n    return minutes.toString().padStart(2, '0');\n  }\n  /**\n   * Get seconds from remaining time\n   */\n  getSeconds() {\n    const status = this.rateLimitService.getCurrentStatus();\n    const seconds = status.remainingTime % 60;\n    return seconds.toString().padStart(2, '0');\n  }\n  /**\n   * Get progress percentage for progress bar\n   */\n  getProgressPercentage() {\n    const status = this.rateLimitService.getCurrentStatus();\n    if (status.retryAfter === 0) return 0;\n    const elapsed = status.retryAfter - status.remainingTime;\n    return elapsed / status.retryAfter * 100;\n  }\n  /**\n   * Dismiss notification (only when countdown is finished)\n   */\n  dismissNotification() {\n    const status = this.rateLimitService.getCurrentStatus();\n    if (status.remainingTime <= 0) {\n      this.rateLimitService.clearRateLimit();\n    }\n  }\n  /**\n   * Refresh the page when rate limit is cleared\n   */\n  refreshPage() {\n    const status = this.rateLimitService.getCurrentStatus();\n    if (status.remainingTime <= 0) {\n      this.rateLimitService.clearRateLimit();\n      window.location.reload();\n    }\n  }\n  static #_ = this.ɵfac = function RateLimitNotificationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RateLimitNotificationComponent)(i0.ɵɵdirectiveInject(i1.RateLimitService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RateLimitNotificationComponent,\n    selectors: [[\"app-rate-limit-notification\"]],\n    decls: 2,\n    vars: 3,\n    consts: [[\"class\", \"rate-limit-popup-overlay\", 4, \"ngIf\"], [1, \"rate-limit-popup-overlay\"], [1, \"rate-limit-popup\"], [1, \"popup-header\"], [1, \"header-icon\"], [1, \"fas\", \"fa-clock\"], [1, \"header-content\"], [1, \"popup-title\"], [1, \"popup-subtitle\"], [\"type\", \"button\", 1, \"close-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\"], [1, \"countdown-section\"], [1, \"countdown-display\"], [1, \"time-box\"], [1, \"time-number\"], [1, \"time-label\"], [1, \"time-separator\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-text\"], [1, \"message-section\"], [1, \"message-content\"], [1, \"fas\", \"fa-shield-alt\", \"message-icon\"], [1, \"message-text\"], [1, \"action-buttons\"], [1, \"btn-refresh\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n    template: function RateLimitNotificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, RateLimitNotificationComponent_div_0_Template, 49, 13, \"div\", 0);\n        i0.ɵɵpipe(1, \"async\");\n      }\n      if (rf & 2) {\n        let tmp_0_0;\n        i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = i0.ɵɵpipeBind1(1, 1, ctx.rateLimit$)) == null ? null : tmp_0_0.isRateLimited);\n      }\n    },\n    dependencies: [CommonModule, i2.NgIf, i2.AsyncPipe],\n    styles: [\"\\n\\n    .rate-limit-popup-overlay[_ngcontent-%COMP%] {\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.6);\\n      -webkit-backdrop-filter: blur(8px);\\n              backdrop-filter: blur(8px);\\n      z-index: 10000;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      padding: 20px;\\n      animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n    }\\n\\n    \\n\\n    .rate-limit-popup[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);\\n      border-radius: 16px;\\n      box-shadow: \\n        0 20px 60px rgba(0, 0, 0, 0.3),\\n        0 0 0 1px rgba(255, 255, 255, 0.1);\\n      max-width: 480px;\\n      width: 100%;\\n      overflow: hidden;\\n      animation: _ngcontent-%COMP%_popIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);\\n      border: 2px solid #ffc107;\\n    }\\n\\n    \\n\\n    .popup-header[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\\n      color: white;\\n      padding: 24px;\\n      display: flex;\\n      align-items: center;\\n      position: relative;\\n    }\\n\\n    .header-icon[_ngcontent-%COMP%] {\\n      background: rgba(255, 255, 255, 0.2);\\n      border-radius: 50%;\\n      width: 56px;\\n      height: 56px;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      margin-right: 16px;\\n      font-size: 24px;\\n      animation: _ngcontent-%COMP%_pulse 2s infinite;\\n    }\\n\\n    .header-content[_ngcontent-%COMP%] {\\n      flex: 1;\\n    }\\n\\n    .popup-title[_ngcontent-%COMP%] {\\n      font-size: 20px;\\n      font-weight: 700;\\n      margin: 0 0 4px 0;\\n      letter-spacing: -0.5px;\\n    }\\n\\n    .popup-subtitle[_ngcontent-%COMP%] {\\n      font-size: 14px;\\n      margin: 0;\\n      opacity: 0.9;\\n      font-weight: 400;\\n    }\\n\\n    .close-btn[_ngcontent-%COMP%] {\\n      background: rgba(255, 255, 255, 0.2);\\n      border: none;\\n      border-radius: 50%;\\n      width: 36px;\\n      height: 36px;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      color: white;\\n      cursor: pointer;\\n      transition: all 0.2s ease;\\n      font-size: 14px;\\n    }\\n\\n    .close-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n      background: rgba(255, 255, 255, 0.3);\\n      transform: scale(1.1);\\n    }\\n\\n    .close-btn[_ngcontent-%COMP%]:disabled {\\n      opacity: 0.4;\\n      cursor: not-allowed;\\n    }\\n\\n    \\n\\n    .countdown-section[_ngcontent-%COMP%] {\\n      padding: 32px 24px;\\n      text-align: center;\\n      background: #fff;\\n    }\\n\\n    .countdown-display[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      margin-bottom: 24px;\\n      gap: 8px;\\n    }\\n\\n    .time-box[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n      color: white;\\n      padding: 16px 12px;\\n      border-radius: 12px;\\n      min-width: 80px;\\n      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\\n      text-align: center;\\n    }\\n\\n    .time-number[_ngcontent-%COMP%] {\\n      display: block;\\n      font-size: 28px;\\n      font-weight: 700;\\n      line-height: 1;\\n      margin-bottom: 4px;\\n      font-family: 'Segoe UI', system-ui, sans-serif;\\n    }\\n\\n    .time-label[_ngcontent-%COMP%] {\\n      display: block;\\n      font-size: 10px;\\n      font-weight: 600;\\n      letter-spacing: 1px;\\n      opacity: 0.9;\\n    }\\n\\n    .time-separator[_ngcontent-%COMP%] {\\n      font-size: 32px;\\n      font-weight: 300;\\n      color: #667eea;\\n      animation: _ngcontent-%COMP%_blink 1s infinite;\\n    }\\n\\n    \\n\\n    .progress-container[_ngcontent-%COMP%] {\\n      background: #e9ecef;\\n      height: 8px;\\n      border-radius: 4px;\\n      overflow: hidden;\\n      margin: 16px 0;\\n      position: relative;\\n    }\\n\\n    .progress-bar[_ngcontent-%COMP%] {\\n      height: 100%;\\n      background: linear-gradient(90deg, #ff6b6b 0%, #ffa500 100%);\\n      border-radius: 4px;\\n      transition: width 1s ease;\\n      position: relative;\\n      overflow: hidden;\\n    }\\n\\n    .progress-bar[_ngcontent-%COMP%]::after {\\n      content: '';\\n      position: absolute;\\n      top: 0;\\n      left: -100%;\\n      width: 100%;\\n      height: 100%;\\n      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);\\n      animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n    }\\n\\n    .progress-text[_ngcontent-%COMP%] {\\n      color: #6c757d;\\n      font-size: 14px;\\n      margin: 8px 0 0 0;\\n    }\\n\\n    \\n\\n    .message-section[_ngcontent-%COMP%] {\\n      background: #f8f9fa;\\n      padding: 20px 24px;\\n      border-top: 1px solid #dee2e6;\\n    }\\n\\n    .message-content[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: flex-start;\\n      gap: 12px;\\n    }\\n\\n    .message-icon[_ngcontent-%COMP%] {\\n      color: #17a2b8;\\n      font-size: 18px;\\n      margin-top: 2px;\\n    }\\n\\n    .message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 0 0 8px 0;\\n      font-size: 13px;\\n      line-height: 1.4;\\n      color: #495057;\\n    }\\n\\n    .message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n      margin-bottom: 0;\\n    }\\n\\n    \\n\\n    .action-buttons[_ngcontent-%COMP%] {\\n      padding: 20px 24px;\\n      background: #fff;\\n      text-align: center;\\n    }\\n\\n    .btn-refresh[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n      color: white;\\n      border: none;\\n      padding: 12px 24px;\\n      border-radius: 8px;\\n      font-weight: 600;\\n      font-size: 14px;\\n      cursor: pointer;\\n      transition: all 0.3s ease;\\n      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);\\n    }\\n\\n    .btn-refresh[_ngcontent-%COMP%]:hover:not(:disabled) {\\n      transform: translateY(-2px);\\n      box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);\\n    }\\n\\n    .btn-refresh[_ngcontent-%COMP%]:disabled {\\n      background: #6c757d;\\n      cursor: not-allowed;\\n      transform: none;\\n      box-shadow: none;\\n    }\\n\\n    \\n\\n    @keyframes _ngcontent-%COMP%_fadeIn {\\n      from { opacity: 0; }\\n      to { opacity: 1; }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_popIn {\\n      0% {\\n        opacity: 0;\\n        transform: scale(0.8) translateY(-20px);\\n      }\\n      100% {\\n        opacity: 1;\\n        transform: scale(1) translateY(0);\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_pulse {\\n      0%, 100% { transform: scale(1); }\\n      50% { transform: scale(1.05); }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_blink {\\n      0%, 50% { opacity: 1; }\\n      51%, 100% { opacity: 0.3; }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_shimmer {\\n      0% { left: -100%; }\\n      100% { left: 100%; }\\n    }\\n\\n    \\n\\n    @media (max-width: 576px) {\\n      .rate-limit-popup[_ngcontent-%COMP%] {\\n        margin: 10px;\\n        max-width: none;\\n      }\\n      \\n      .popup-header[_ngcontent-%COMP%] {\\n        padding: 20px;\\n      }\\n      \\n      .countdown-section[_ngcontent-%COMP%] {\\n        padding: 24px 20px;\\n      }\\n      \\n      .time-box[_ngcontent-%COMP%] {\\n        min-width: 70px;\\n        padding: 12px 8px;\\n      }\\n      \\n      .time-number[_ngcontent-%COMP%] {\\n        font-size: 24px;\\n      }\\n      \\n      .message-section[_ngcontent-%COMP%], \\n   .action-buttons[_ngcontent-%COMP%] {\\n        padding: 16px 20px;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [\n        // Add Angular animations if needed\n      ]\n    }\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RateLimitService", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "RateLimitNotificationComponent_div_0_Template_button_click_10_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "dismissNotification", "RateLimitNotificationComponent_div_0_Template_button_click_45_listener", "refreshPage", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "ɵɵpipeBind1", "rateLimit$", "remainingTime", "ɵɵtextInterpolate", "getMinutes", "getSeconds", "ɵɵstyleProp", "getProgressPercentage", "formatTime", "tmp_5_0", "tmp_6_0", "RateLimitNotificationComponent", "constructor", "rateLimitService", "ngOnInit", "console", "log", "subscribe", "status", "ngOnDestroy", "seconds", "formatTimeRemaining", "getCurrentStatus", "minutes", "Math", "floor", "toString", "padStart", "retryAfter", "elapsed", "clearRateLimit", "window", "location", "reload", "_", "ɵɵdirectiveInject", "i1", "_2", "selectors", "decls", "vars", "consts", "template", "RateLimitNotificationComponent_Template", "rf", "ctx", "ɵɵtemplate", "RateLimitNotificationComponent_div_0_Template", "tmp_0_0", "isRateLimited", "i2", "NgIf", "AsyncPipe", "styles", "data", "animation"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\rate-limit-notification\\rate-limit-notification.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Observable } from 'rxjs';\r\nimport { RateLimitService, RateLimitStatus } from '../../services/rate-limit.service';\r\n\r\n@Component({\r\n  selector: 'app-rate-limit-notification',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  template: `    <!-- Modern Rate Limit Popup Notification -->\r\n    <div *ngIf=\"(rateLimit$ | async)?.isRateLimited\" \r\n         class=\"rate-limit-popup-overlay\">\r\n      <div class=\"rate-limit-popup\">\r\n        \r\n        <!-- Header with Icon -->\r\n        <div class=\"popup-header\">\r\n          <div class=\"header-icon\">\r\n            <i class=\"fas fa-clock\"></i>\r\n          </div>\r\n          <div class=\"header-content\">\r\n            <h4 class=\"popup-title\">Rate Limit Exceeded</h4>\r\n            <p class=\"popup-subtitle\">Too many requests - please wait</p>\r\n          </div>\r\n          <button type=\"button\" \r\n                  class=\"close-btn\" \r\n                  (click)=\"dismissNotification()\"\r\n                  [disabled]=\"(rateLimit$ | async)?.remainingTime! > 0\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Countdown Timer Display -->\r\n        <div class=\"countdown-section\">\r\n          <div class=\"countdown-display\">\r\n            <div class=\"time-box\">\r\n              <span class=\"time-number\">{{ getMinutes() }}</span>\r\n              <span class=\"time-label\">MIN</span>\r\n            </div>\r\n            <div class=\"time-separator\">:</div>\r\n            <div class=\"time-box\">\r\n              <span class=\"time-number\">{{ getSeconds() }}</span>\r\n              <span class=\"time-label\">SEC</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- Progress Bar -->\r\n          <div class=\"progress-container\">\r\n            <div class=\"progress-bar\" \r\n                 [style.width.%]=\"getProgressPercentage()\">\r\n            </div>\r\n          </div>\r\n          \r\n          <p class=\"progress-text\">\r\n            Please wait <strong>{{ formatTime((rateLimit$ | async)?.remainingTime || 0) }}</strong> \r\n            before making another request\r\n          </p>\r\n        </div>\r\n\r\n        <!-- Message Section -->\r\n        <div class=\"message-section\">\r\n          <div class=\"message-content\">\r\n            <i class=\"fas fa-shield-alt message-icon\"></i>\r\n            <div class=\"message-text\">\r\n              <p><strong>Why am I seeing this?</strong></p>\r\n              <p>Rate limiting protects our service from overload and ensures fair usage for all users.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Action Buttons -->\r\n        <div class=\"action-buttons\">\r\n          <button class=\"btn-refresh\" \r\n                  [disabled]=\"(rateLimit$ | async)?.remainingTime! > 0\"\r\n                  (click)=\"refreshPage()\">\r\n            <i class=\"fas fa-refresh me-2\"></i>\r\n            Try Again\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    /* Popup Overlay */\r\n    .rate-limit-popup-overlay {\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: rgba(0, 0, 0, 0.6);\r\n      backdrop-filter: blur(8px);\r\n      z-index: 10000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 20px;\r\n      animation: fadeIn 0.3s ease-out;\r\n    }\r\n\r\n    /* Main Popup Container */\r\n    .rate-limit-popup {\r\n      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);\r\n      border-radius: 16px;\r\n      box-shadow: \r\n        0 20px 60px rgba(0, 0, 0, 0.3),\r\n        0 0 0 1px rgba(255, 255, 255, 0.1);\r\n      max-width: 480px;\r\n      width: 100%;\r\n      overflow: hidden;\r\n      animation: popIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);\r\n      border: 2px solid #ffc107;\r\n    }\r\n\r\n    /* Header Section */\r\n    .popup-header {\r\n      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\r\n      color: white;\r\n      padding: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n      position: relative;\r\n    }\r\n\r\n    .header-icon {\r\n      background: rgba(255, 255, 255, 0.2);\r\n      border-radius: 50%;\r\n      width: 56px;\r\n      height: 56px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 16px;\r\n      font-size: 24px;\r\n      animation: pulse 2s infinite;\r\n    }\r\n\r\n    .header-content {\r\n      flex: 1;\r\n    }\r\n\r\n    .popup-title {\r\n      font-size: 20px;\r\n      font-weight: 700;\r\n      margin: 0 0 4px 0;\r\n      letter-spacing: -0.5px;\r\n    }\r\n\r\n    .popup-subtitle {\r\n      font-size: 14px;\r\n      margin: 0;\r\n      opacity: 0.9;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .close-btn {\r\n      background: rgba(255, 255, 255, 0.2);\r\n      border: none;\r\n      border-radius: 50%;\r\n      width: 36px;\r\n      height: 36px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: white;\r\n      cursor: pointer;\r\n      transition: all 0.2s ease;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .close-btn:hover:not(:disabled) {\r\n      background: rgba(255, 255, 255, 0.3);\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    .close-btn:disabled {\r\n      opacity: 0.4;\r\n      cursor: not-allowed;\r\n    }\r\n\r\n    /* Countdown Section */\r\n    .countdown-section {\r\n      padding: 32px 24px;\r\n      text-align: center;\r\n      background: #fff;\r\n    }\r\n\r\n    .countdown-display {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n      gap: 8px;\r\n    }\r\n\r\n    .time-box {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n      padding: 16px 12px;\r\n      border-radius: 12px;\r\n      min-width: 80px;\r\n      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n      text-align: center;\r\n    }\r\n\r\n    .time-number {\r\n      display: block;\r\n      font-size: 28px;\r\n      font-weight: 700;\r\n      line-height: 1;\r\n      margin-bottom: 4px;\r\n      font-family: 'Segoe UI', system-ui, sans-serif;\r\n    }\r\n\r\n    .time-label {\r\n      display: block;\r\n      font-size: 10px;\r\n      font-weight: 600;\r\n      letter-spacing: 1px;\r\n      opacity: 0.9;\r\n    }\r\n\r\n    .time-separator {\r\n      font-size: 32px;\r\n      font-weight: 300;\r\n      color: #667eea;\r\n      animation: blink 1s infinite;\r\n    }\r\n\r\n    /* Progress Bar */\r\n    .progress-container {\r\n      background: #e9ecef;\r\n      height: 8px;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n      margin: 16px 0;\r\n      position: relative;\r\n    }\r\n\r\n    .progress-bar {\r\n      height: 100%;\r\n      background: linear-gradient(90deg, #ff6b6b 0%, #ffa500 100%);\r\n      border-radius: 4px;\r\n      transition: width 1s ease;\r\n      position: relative;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .progress-bar::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);\r\n      animation: shimmer 2s infinite;\r\n    }\r\n\r\n    .progress-text {\r\n      color: #6c757d;\r\n      font-size: 14px;\r\n      margin: 8px 0 0 0;\r\n    }\r\n\r\n    /* Message Section */\r\n    .message-section {\r\n      background: #f8f9fa;\r\n      padding: 20px 24px;\r\n      border-top: 1px solid #dee2e6;\r\n    }\r\n\r\n    .message-content {\r\n      display: flex;\r\n      align-items: flex-start;\r\n      gap: 12px;\r\n    }\r\n\r\n    .message-icon {\r\n      color: #17a2b8;\r\n      font-size: 18px;\r\n      margin-top: 2px;\r\n    }\r\n\r\n    .message-text p {\r\n      margin: 0 0 8px 0;\r\n      font-size: 13px;\r\n      line-height: 1.4;\r\n      color: #495057;\r\n    }\r\n\r\n    .message-text p:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    /* Action Buttons */\r\n    .action-buttons {\r\n      padding: 20px 24px;\r\n      background: #fff;\r\n      text-align: center;\r\n    }\r\n\r\n    .btn-refresh {\r\n      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\r\n      color: white;\r\n      border: none;\r\n      padding: 12px 24px;\r\n      border-radius: 8px;\r\n      font-weight: 600;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);\r\n    }\r\n\r\n    .btn-refresh:hover:not(:disabled) {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);\r\n    }\r\n\r\n    .btn-refresh:disabled {\r\n      background: #6c757d;\r\n      cursor: not-allowed;\r\n      transform: none;\r\n      box-shadow: none;\r\n    }\r\n\r\n    /* Animations */\r\n    @keyframes fadeIn {\r\n      from { opacity: 0; }\r\n      to { opacity: 1; }\r\n    }\r\n\r\n    @keyframes popIn {\r\n      0% {\r\n        opacity: 0;\r\n        transform: scale(0.8) translateY(-20px);\r\n      }\r\n      100% {\r\n        opacity: 1;\r\n        transform: scale(1) translateY(0);\r\n      }\r\n    }\r\n\r\n    @keyframes pulse {\r\n      0%, 100% { transform: scale(1); }\r\n      50% { transform: scale(1.05); }\r\n    }\r\n\r\n    @keyframes blink {\r\n      0%, 50% { opacity: 1; }\r\n      51%, 100% { opacity: 0.3; }\r\n    }\r\n\r\n    @keyframes shimmer {\r\n      0% { left: -100%; }\r\n      100% { left: 100%; }\r\n    }\r\n\r\n    /* Responsive Design */\r\n    @media (max-width: 576px) {\r\n      .rate-limit-popup {\r\n        margin: 10px;\r\n        max-width: none;\r\n      }\r\n      \r\n      .popup-header {\r\n        padding: 20px;\r\n      }\r\n      \r\n      .countdown-section {\r\n        padding: 24px 20px;\r\n      }\r\n      \r\n      .time-box {\r\n        min-width: 70px;\r\n        padding: 12px 8px;\r\n      }\r\n      \r\n      .time-number {\r\n        font-size: 24px;\r\n      }\r\n      \r\n      .message-section,\r\n      .action-buttons {\r\n        padding: 16px 20px;\r\n      }\r\n    }\r\n  `],\r\n  animations: [\r\n    // Add Angular animations if needed\r\n  ]\r\n})\r\nexport class RateLimitNotificationComponent implements OnInit, OnDestroy {\r\n  rateLimit$: Observable<RateLimitStatus>;\r\n\r\n  constructor(private rateLimitService: RateLimitService) {\r\n    this.rateLimit$ = this.rateLimitService.rateLimit$;\r\n  }\r\n  ngOnInit(): void {\r\n    console.log('🚀 RateLimitNotificationComponent initialized');\r\n    \r\n    // Debug: Subscribe to rate limit changes\r\n    this.rateLimit$.subscribe(status => {\r\n      console.log('📊 Rate limit status changed:', status);\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup if needed\r\n  }\r\n\r\n  /**\r\n   * Format time for display\r\n   */\r\n  formatTime(seconds: number): string {\r\n    return RateLimitService.formatTimeRemaining(seconds);\r\n  }\r\n\r\n  /**\r\n   * Get minutes from remaining time\r\n   */\r\n  getMinutes(): string {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    const minutes = Math.floor(status.remainingTime / 60);\r\n    return minutes.toString().padStart(2, '0');\r\n  }\r\n\r\n  /**\r\n   * Get seconds from remaining time\r\n   */\r\n  getSeconds(): string {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    const seconds = status.remainingTime % 60;\r\n    return seconds.toString().padStart(2, '0');\r\n  }\r\n\r\n  /**\r\n   * Get progress percentage for progress bar\r\n   */\r\n  getProgressPercentage(): number {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    if (status.retryAfter === 0) return 0;\r\n    \r\n    const elapsed = status.retryAfter - status.remainingTime;\r\n    return (elapsed / status.retryAfter) * 100;\r\n  }\r\n\r\n  /**\r\n   * Dismiss notification (only when countdown is finished)\r\n   */\r\n  dismissNotification(): void {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    if (status.remainingTime <= 0) {\r\n      this.rateLimitService.clearRateLimit();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Refresh the page when rate limit is cleared\r\n   */\r\n  refreshPage(): void {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    if (status.remainingTime <= 0) {\r\n      this.rateLimitService.clearRateLimit();\r\n      window.location.reload();\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAyB,mCAAmC;;;;;;;IAa3EC,EANN,CAAAC,cAAA,aACsC,aACN,aAGF,aACC;IACvBD,EAAA,CAAAE,SAAA,WAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAA4B,YACF;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAI,MAAA,sCAA+B;IAC3DJ,EAD2D,CAAAG,YAAA,EAAI,EACzD;IACNH,EAAA,CAAAC,cAAA,iBAG8D;;IADtDD,EAAA,CAAAK,UAAA,mBAAAC,uEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,mBAAA,EAAqB;IAAA,EAAC;IAErCZ,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAMAH,EAHN,CAAAC,cAAA,eAA+B,eACE,eACP,gBACM;IAAAD,EAAA,CAAAI,MAAA,IAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,WAAG;IAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEjCH,EADF,CAAAC,cAAA,eAAsB,gBACM;IAAAD,EAAA,CAAAI,MAAA,IAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,WAAG;IAEhCJ,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAE,SAAA,eAEM;IACRF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAI,MAAA,qBAAY;IAAAJ,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAI,MAAA,IAA0D;;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACvFH,EAAA,CAAAI,MAAA,uCACF;IACFJ,EADE,CAAAG,YAAA,EAAI,EACA;IAIJH,EADF,CAAAC,cAAA,eAA6B,eACE;IAC3BD,EAAA,CAAAE,SAAA,aAA8C;IAEzCF,EADL,CAAAC,cAAA,eAA0B,SACrB,cAAQ;IAAAD,EAAA,CAAAI,MAAA,6BAAqB;IAASJ,EAAT,CAAAG,YAAA,EAAS,EAAI;IAC7CH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAI,MAAA,8FAAsF;IAG/FJ,EAH+F,CAAAG,YAAA,EAAI,EACzF,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,eAA4B,kBAGM;;IAAxBD,EAAA,CAAAK,UAAA,mBAAAQ,uEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAK,WAAA,EAAa;IAAA,EAAC;IAC7Bd,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAI,MAAA,mBACF;IAGNJ,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;;;IArDQH,EAAA,CAAAe,SAAA,IAAqD;IAArDf,EAAA,CAAAgB,UAAA,eAAAC,OAAA,GAAAjB,EAAA,CAAAkB,WAAA,QAAAT,MAAA,CAAAU,UAAA,oBAAAF,OAAA,CAAAG,aAAA,MAAqD;IAS/BpB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAqB,iBAAA,CAAAZ,MAAA,CAAAa,UAAA,GAAkB;IAKlBtB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAqB,iBAAA,CAAAZ,MAAA,CAAAc,UAAA,GAAkB;IAQzCvB,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAAwB,WAAA,UAAAf,MAAA,CAAAgB,qBAAA,QAAyC;IAK1BzB,EAAA,CAAAe,SAAA,GAA0D;IAA1Df,EAAA,CAAAqB,iBAAA,CAAAZ,MAAA,CAAAiB,UAAA,GAAAC,OAAA,GAAA3B,EAAA,CAAAkB,WAAA,QAAAT,MAAA,CAAAU,UAAA,oBAAAQ,OAAA,CAAAP,aAAA,QAA0D;IAmBxEpB,EAAA,CAAAe,SAAA,IAAqD;IAArDf,EAAA,CAAAgB,UAAA,eAAAY,OAAA,GAAA5B,EAAA,CAAAkB,WAAA,SAAAT,MAAA,CAAAU,UAAA,oBAAAS,OAAA,CAAAR,aAAA,MAAqD;;;AAgUvE,OAAM,MAAOS,8BAA8B;EAGzCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAClC,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACY,gBAAgB,CAACZ,UAAU;EACpD;EACAa,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,CAACf,UAAU,CAACgB,SAAS,CAACC,MAAM,IAAG;MACjCH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEE,MAAM,CAAC;IACtD,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;EAAA;EAGF;;;EAGAX,UAAUA,CAACY,OAAe;IACxB,OAAOvC,gBAAgB,CAACwC,mBAAmB,CAACD,OAAO,CAAC;EACtD;EAEA;;;EAGAhB,UAAUA,CAAA;IACR,MAAMc,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACS,gBAAgB,EAAE;IACvD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACP,MAAM,CAAChB,aAAa,GAAG,EAAE,CAAC;IACrD,OAAOqB,OAAO,CAACG,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C;EAEA;;;EAGAtB,UAAUA,CAAA;IACR,MAAMa,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACS,gBAAgB,EAAE;IACvD,MAAMF,OAAO,GAAGF,MAAM,CAAChB,aAAa,GAAG,EAAE;IACzC,OAAOkB,OAAO,CAACM,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C;EAEA;;;EAGApB,qBAAqBA,CAAA;IACnB,MAAMW,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACS,gBAAgB,EAAE;IACvD,IAAIJ,MAAM,CAACU,UAAU,KAAK,CAAC,EAAE,OAAO,CAAC;IAErC,MAAMC,OAAO,GAAGX,MAAM,CAACU,UAAU,GAAGV,MAAM,CAAChB,aAAa;IACxD,OAAQ2B,OAAO,GAAGX,MAAM,CAACU,UAAU,GAAI,GAAG;EAC5C;EAEA;;;EAGAlC,mBAAmBA,CAAA;IACjB,MAAMwB,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACS,gBAAgB,EAAE;IACvD,IAAIJ,MAAM,CAAChB,aAAa,IAAI,CAAC,EAAE;MAC7B,IAAI,CAACW,gBAAgB,CAACiB,cAAc,EAAE;IACxC;EACF;EAEA;;;EAGAlC,WAAWA,CAAA;IACT,MAAMsB,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACS,gBAAgB,EAAE;IACvD,IAAIJ,MAAM,CAAChB,aAAa,IAAI,CAAC,EAAE;MAC7B,IAAI,CAACW,gBAAgB,CAACiB,cAAc,EAAE;MACtCC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;IAC1B;EACF;EAAC,QAAAC,CAAA,G;qCA1EUvB,8BAA8B,EAAA7B,EAAA,CAAAqD,iBAAA,CAAAC,EAAA,CAAAvD,gBAAA;EAAA;EAAA,QAAAwD,EAAA,G;UAA9B1B,8BAA8B;IAAA2B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9XvC9D,EAAA,CAAAgE,UAAA,IAAAC,6CAAA,mBACsC;;;;;QADhCjE,EAAA,CAAAgB,UAAA,UAAAkD,OAAA,GAAAlE,EAAA,CAAAkB,WAAA,OAAA6C,GAAA,CAAA5C,UAAA,oBAAA+C,OAAA,CAAAC,aAAA,CAAyC;;;mBAFvCrE,YAAY,EAAAsE,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,SAAA;IAAAC,MAAA;IAAAC,IAAA;MAAAC,SAAA,EA4XV;QACV;MAAA;IACD;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}