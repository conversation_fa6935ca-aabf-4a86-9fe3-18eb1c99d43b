=== Page 291 ===

291Y<PERSON> can also collect all subclasses of an object. You may use this if you want to collect
all instances of a specific class from the container:
This is the most useful part of the container. It is possible to retrieve objects from the
container by simply passing them into the parameter list of any object. Certain aspects of
Masonite are resolved such as controller methods, middleware and drivers.
For example, we can type hint that we want to get the Request class and put it into our
controller. All controller methods are resolved by the container.
In this example, before the show method is called, Masonite will look at the parameters
and look inside the container for the Request object.
Masonite will know that you are trying to get the Request class and will actually retrieve
that class from the container. Masonite will search the container for a Request class
regardless of what the key is in the container, retrieve it, and inject it into the controller
method. Effectively creating an IOC container with dependency injection. capabilities
Think of this as a get by value instead of a get by key like the earlier example.
Pretty powerful stuff, eh?
Masonite will also resolve your custom, application-specific classes including those that
you have not explicitly bound with app.bind().
Continuing with the example above, the following will work out of the box (assuming the
relevant classes exist), without needing to bind the custom classes in the container:from cleo import Command
...
app.collect(Command)
# Returns {'FirstCommand': <class ...>, 'AnotherCommand': ...}
def show(self, request: Request):
    request.user()Resolve6/12/25, 3:02 AM Masonite Documentation