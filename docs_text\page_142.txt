=== Page 143 ===

143On the frontend we need to listen to releases channel and subscribe to NewRelease
events to display an alert box with the release message.# app/broadcasts/NewRelease.py
from masonite.broadcasting import CanBroadcast, Channel
class NewRelease(CanBroadcast):
    def __init__(self, version):
        self.version = version
    def broadcast_on(self):
        return Channel("releases")
    def broadcast_with(self):
        return {"message": f"Version {self.version} has been released 
!"}
from masonite.facades import Broadcast
from app.broadcasts import NewRelease
Broadcast.channel(NewRelease("4.0.0"))
<html lang="en">
<head>
  <title>Document</title>
  <script src="https://js.pusher.com/7.0/pusher.min.js"></script>
</head>
<body>
  <script>
    const pusher = new Pusher("478b45309560f3456211", {
      cluster: "eu"
    });
    const channel = pusher.subscribe('releases');
    channel.bind('NewRelease', (data) => {
      alert(data.message)
    })
  </script>
</body>
</html>6/12/25, 3:02 AM Masonite Documentation