=== Page 191 ===

191To set a Model as Notifiable, just add the Notifiable mixin to it:
You can now send notifications to it with user.notify() method.
Then you can define how notifications should be routed for the different channels. This is
always done by defining a route_notification_for_{driver} method.
For example, with mail driver you can define:
This is actually the default behaviour of the mail driver so you won't have to write that but
you can customize it to your needs if your User model don't have email field or if you
want to add some logic to get the recipient email.
If you would like to queue the notification then you just need to inherit the Queueable
class and it will automatically send your notifications into the queue to be processed
later. This is a great way to speed up your application:from masonite.notification import Notifiable
class User(Model, Notifiable):
    # ...
from masonite.notification import Notifiable
class User(Model, Notifiable):
    ...
    def route_notification_for_mail(self):
        return self.emailRouting
Queueing Notifications6/12/25, 3:02 AM Masonite Documentation