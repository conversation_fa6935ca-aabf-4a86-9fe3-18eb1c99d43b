=== Page 111 ===

111When the above exception is raised, Masonite will look for the error view 
errors/401.html in the default project views folder and will render it with a 401
status code. The content of get_response() method will be passed as the message
context variable to the view.
If this view does not exist the HTML response will be directly the content of the 
get_response() method with a 401 status code.
If you want more flexibility to render your exception without using the
HTTPExceptionHandler above, you can just add a get_response() method to it. This
method will be given as first argument of response.view(), so that you can render
simple string or your own view template.class ExpiredToken(Exception):
    is_http_exception = True
    def __init__(self, token):
        super().__init__()
        self.expired_at = token.expired_at
    def get_response(self):
        return "Expired API Token"
    def get_status(self):
        return 401
    def get_headers(self):
        return {
            "X-Expired-At": self.expired_at
        }
Renderable Exceptions6/12/25, 3:02 AM Masonite Documentation