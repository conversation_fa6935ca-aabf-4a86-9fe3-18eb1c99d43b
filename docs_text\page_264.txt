=== Page 265 ===

265This is used to verify that the domain being passed in is a DNS resolvable domain name.
You can also do this for email addresses as well. The preferred search is domain.com but
Masonite will strip out http://, https:// and www automatically for you.
Used to make sure the date is a date after today. In this example, this will work for any
day that is 2019-10-21 or later.
You may also pass in a timezone for this rule:
Used to make sure the date is a date before today. In this example, this will work for any
day that is 2019-10-19 or earlier."""
{
  'domain': 'http://google.com',
  'email': '<EMAIL>'
}
"""
validate.active_domain(['domain', 'email'])
"""
{
  'date': '2019-10-20', # Or date in the future
}
"""
validate.after_today('date')
"""
{
  'date': '2019-10-20', # Or date in the future
}
"""
validate.after_today('date', tz='America/New_York')After_today
Before_today6/12/25, 3:02 AM Masonite Documentation