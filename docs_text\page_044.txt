=== Page 45 ===

45plethora of documentation even outside of this documentation. It needs to have notoriety
and if people are seeing the framework pop up in their favorite locations they will be more
inclined to use the framework and contribute to it as well.
Plus there will be fantastic tutorials out there for beginners to find and watch and you
could also build a following off the back of Masonite.
This documentation is fantastic but there are spots where it could be improved. Maybe
we haven't explained something fully or something just doesn't make sense to you.
Masonite uses Gitbook.com to host it's documentation and with that you are able to
comment directly on the documentation which will start a discussion between you and
the documentation collaborators. So if you want to cycle through the documentation
page by page and get acquainted with the framework but at the same time contribute to
the documentation, this is perfect for you.
If you just don't want to contribute code to the main project you may instead simply
report bugs or improvements. You can go ahead and build any of your applications as
usual and report any bugs you encounter to the GitHub.com issues page.
Look at the issues page on GitHub.com for any issues, bugs or enhancements that you
are willing to fix. If you don't know how to work on them, just comment on the issue and
<PERSON> or other core contributors will be more than happy explaining step by
step on how you can go about fixing or developing that issue.
Fix the Documentation
Report Bugs
Squash Bugs
Build a Community6/12/25, 3:02 AM Masonite Documentation