"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlerController = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const core_1 = require("@loopback/core");
const authentication_1 = require("@loopback/authentication");
const security_1 = require("@loopback/security");
const models_1 = require("../models");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let CrawlerController = class CrawlerController {
    constructor(crawlJobRepository, crawledContentRepository, crawlerService) {
        this.crawlJobRepository = crawlJobRepository;
        this.crawledContentRepository = crawledContentRepository;
        this.crawlerService = crawlerService;
    }
    async createCrawlJob(crawlJob, currentUser) {
        // Validate URL
        try {
            new URL(crawlJob.url);
        }
        catch {
            throw new rest_1.HttpErrors.BadRequest('Invalid URL provided');
        }
        // Create crawl job
        const newCrawlJob = await this.crawlJobRepository.create({
            ...crawlJob,
            userId: currentUser.id,
            status: 'pending',
        });
        // Start crawling process
        await this.crawlerService.startCrawl(newCrawlJob);
        return newCrawlJob;
    }
    async findCrawlJobs(currentUser, filter) {
        // Filter by current user
        const userFilter = {
            ...filter,
            where: {
                ...filter?.where,
                userId: currentUser.id,
            },
        };
        return this.crawlJobRepository.find(userFilter);
    }
    async findCrawlJobById(id, currentUser, filter) {
        const crawlJob = await this.crawlJobRepository.findById(id, filter);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return crawlJob;
    }
    async updateCrawlJob(id, currentUser, crawlJob) {
        // Check ownership
        const existingJob = await this.crawlJobRepository.findById(id);
        if (existingJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        await this.crawlJobRepository.updateById(id, crawlJob);
    }
    async deleteCrawlJob(id, currentUser) {
        // Check ownership
        const existingJob = await this.crawlJobRepository.findById(id);
        if (existingJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        // Stop crawling if running
        if (existingJob.status === 'running' || existingJob.status === 'paused') {
            await this.crawlerService.stopCrawl(id);
        }
        // Delete related content
        await this.crawledContentRepository.deleteAll({ crawlJobId: id });
        // Delete job
        await this.crawlJobRepository.deleteById(id);
    }
    async startCrawlJob(id, currentUser) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        if (crawlJob.status !== 'pending' && crawlJob.status !== 'failed') {
            throw new rest_1.HttpErrors.BadRequest('Crawl job cannot be started in current status');
        }
        await this.crawlerService.startCrawl(crawlJob);
        return { message: 'Crawl job started successfully' };
    }
    async stopCrawlJob(id, currentUser) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        if (crawlJob.status !== 'running' && crawlJob.status !== 'paused') {
            throw new rest_1.HttpErrors.BadRequest('Crawl job is not running');
        }
        await this.crawlerService.stopCrawl(id);
        return { message: 'Crawl job stopped successfully' };
    }
    async pauseCrawlJob(id, currentUser) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        if (crawlJob.status !== 'running') {
            throw new rest_1.HttpErrors.BadRequest('Crawl job is not running');
        }
        await this.crawlerService.pauseCrawl(id);
        return { message: 'Crawl job paused successfully' };
    }
    async resumeCrawlJob(id, currentUser) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        if (crawlJob.status !== 'paused') {
            throw new rest_1.HttpErrors.BadRequest('Crawl job is not paused');
        }
        await this.crawlerService.resumeCrawl(id);
        return { message: 'Crawl job resumed successfully' };
    }
    async getCrawlJobProgress(id, currentUser) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return this.crawlerService.getCrawlProgress(id);
    }
    async getCrawledContent(id, currentUser, filter) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        const contentFilter = {
            ...filter,
            where: {
                ...filter?.where,
                crawlJobId: id,
            },
        };
        return this.crawledContentRepository.find(contentFilter);
    }
    async getCrawlJobStatistics(id, currentUser) {
        const crawlJob = await this.crawlJobRepository.findById(id);
        // Check ownership
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return this.crawledContentRepository.getCrawlJobContentStatistics(id);
    }
};
exports.CrawlerController = CrawlerController;
tslib_1.__decorate([
    (0, rest_1.post)('/crawler/jobs'),
    (0, rest_1.response)(200, {
        description: 'Create a new crawl job',
        content: { 'application/json': { schema: (0, rest_1.getModelSchemaRef)(models_1.CrawlJob) } },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.CrawlJob, {
                    title: 'NewCrawlJob',
                    exclude: ['id', 'createdAt', 'updatedAt', 'userId'],
                }),
            },
        },
    })),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "createCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.get)('/crawler/jobs'),
    (0, rest_1.response)(200, {
        description: 'Array of CrawlJob model instances',
        content: {
            'application/json': {
                schema: {
                    type: 'array',
                    items: (0, rest_1.getModelSchemaRef)(models_1.CrawlJob, { includeRelations: true }),
                },
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, rest_1.param.filter(models_1.CrawlJob)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "findCrawlJobs", null);
tslib_1.__decorate([
    (0, rest_1.get)('/crawler/jobs/{id}'),
    (0, rest_1.response)(200, {
        description: 'CrawlJob model instance',
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.CrawlJob, { includeRelations: true }),
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(2, rest_1.param.filter(models_1.CrawlJob, { exclude: 'where' })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "findCrawlJobById", null);
tslib_1.__decorate([
    (0, rest_1.patch)('/crawler/jobs/{id}'),
    (0, rest_1.response)(204, {
        description: 'CrawlJob PATCH success',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(2, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.CrawlJob, { partial: true }),
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "updateCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.del)('/crawler/jobs/{id}'),
    (0, rest_1.response)(204, {
        description: 'CrawlJob DELETE success',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "deleteCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.post)('/crawler/jobs/{id}/start'),
    (0, rest_1.response)(200, {
        description: 'Start crawl job',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "startCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.post)('/crawler/jobs/{id}/stop'),
    (0, rest_1.response)(200, {
        description: 'Stop crawl job',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "stopCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.post)('/crawler/jobs/{id}/pause'),
    (0, rest_1.response)(200, {
        description: 'Pause crawl job',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "pauseCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.post)('/crawler/jobs/{id}/resume'),
    (0, rest_1.response)(200, {
        description: 'Resume crawl job',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "resumeCrawlJob", null);
tslib_1.__decorate([
    (0, rest_1.get)('/crawler/jobs/{id}/progress'),
    (0, rest_1.response)(200, {
        description: 'Get crawl job progress',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "getCrawlJobProgress", null);
tslib_1.__decorate([
    (0, rest_1.get)('/crawler/jobs/{id}/content'),
    (0, rest_1.response)(200, {
        description: 'Get crawled content for job',
        content: {
            'application/json': {
                schema: {
                    type: 'array',
                    items: (0, rest_1.getModelSchemaRef)(models_1.CrawledContent),
                },
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(2, rest_1.param.filter(models_1.CrawledContent)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "getCrawledContent", null);
tslib_1.__decorate([
    (0, rest_1.get)('/crawler/jobs/{id}/statistics'),
    (0, rest_1.response)(200, {
        description: 'Get crawl job statistics',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], CrawlerController.prototype, "getCrawlJobStatistics", null);
exports.CrawlerController = CrawlerController = tslib_1.__decorate([
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.CrawlJobRepository)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.CrawledContentRepository)),
    tslib_1.__param(2, (0, core_1.inject)('services.CrawlerService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.CrawlJobRepository,
        repositories_1.CrawledContentRepository,
        services_1.CrawlerService])
], CrawlerController);
//# sourceMappingURL=crawler.controller.js.map