=== Page 30 ===

30Notice we used a GET route here, It would be much better to use a POST method but for
simplicity sake will assume you can create one by now. We will just add a link to our
update method which will delete the post.
We can throw a delete link right inside our update template:from masonite.request import Request
...
def delete(self, request: Request):
    post = Post.find(request.param('id'))
    post.delete()
    return 'post deleted'
routes/web.py
Route.get('/post/@id/delete', 'PostController@delete'),
templates/update.htmlMake the route:
Update the Template6/12/25, 3:02 AM Masonite Documentation