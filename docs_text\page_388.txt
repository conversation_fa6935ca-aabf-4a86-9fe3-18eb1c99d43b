=== Page 389 ===

389W<PERSON> also removed all instances of helper functions by default since it was confusing
developers and was throwing red squiggly marks for text editors. They are still available
to be used but they will not be known to developers unless they discover them in the
documentation. Now all default code explicitly resolves via the container and helper
functions can be used on the developers own terms.
Helper functions are still available but you will need to use them on your own terms.
Now every application has a basic seeding structure setup which is the same as if
running the craft seed command. This is to promote more use of this awesome
feature which can be used in migration files for quick seeding of databases for
development.
We were previously not able to import code into our migration files or database seeders
because the command line tool would not pick up our current working directory to import
classes into. Now the migrations module and seeds module have 3 lines of code:
this helpers when running the command line to import code into these modules.
In development you would see a message like:import os
import sys
sys.path.append(os.getcwd())Removed Helper functions by default
Added seeds by default
Added code to __init__.py file in migrations and seeds
Route Printing6/12/25, 3:02 AM Masonite Documentation