=== Page 144 ===

144Let's imagine our User model has two roles basic and admin and that we want to send
alerts to admin users only. The basic users should not be authorized to subscribe to the
alerts.
To achieve this on the backend we need to:
•create a custom authentication route to authorize admin users only on channel 
private-admins
•create a AdminUserAlert Broadcast event
•trigger this event from the backend.
Let's first create the authentication route and controller
# routes/web.py
from masonite.routes import Route
ROUTES = [
    Route.post("/pusher/user-auth", "BroadcastController@authorize")
    #..
]Sending private alerts to admin users6/12/25, 3:02 AM Masonite Documentation