=== Page 326 ===

326Assert that the session does not contain an errors key or that this key is empty.
Assert that the route returned the given view name.
Assert that view context contains a given data key and value (if provided).
Assert that view context contains exactly the given data keys. It can be a list of keys or a
dictionary (here only keys will be verified).
Assert that given data key is not available in the view context.
Assert that a user is authenticated after the current request.self.get("/").assertSessionHasNoErrors()
self.get("/").assertViewIs("app")
self.get("/").assertViewHas(key, value=None)
self.get("/").assertViewHasExact(keys)
self.get("/").assertViewMissing(key)assertViewIs
assertViewHas
assertViewHasExact
assertViewMissing
assertAuthenticated6/12/25, 3:02 AM Masonite Documentation