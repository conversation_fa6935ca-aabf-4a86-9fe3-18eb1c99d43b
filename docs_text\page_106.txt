=== Page 107 ===

107which time you override the configuration option.
This is mostly useful during tests, when you want to override a configuration option to
test a specific behaviour:
But if you simply want to have different configuration depending on the environment
(development, testing or production) you should rely instead on environment variables
used to define configuration options.    def test_something(self):
        old_value = Config.get("mail.from_email")
        Config.set("mail.from_email", "<EMAIL>")
        # test...
        Config.set("mail.from_email", old_value)6/12/25, 3:02 AM Masonite Documentation