=== Page 140 ===

140Pusher is expecting the authentication route to be /pusher/user-authby default. If
you want to change this client-side you can do it when creating Pusher instance
You will also need to add the /pusher/user-auth route to the CSRF exemption.
The reason for this is that the broadcast client will not send the CSRF token along with
the POST authorization request.
If you want to keep CSRF protection you can read more about it here.
The default behaviour is to authorize everyone to access any private channels.
If you want to customize channels authorization logic you can add your own broadcast
authorization route with a custom controller. Let's imagine you want to authenticate
channels per users, meaning that user with ID 1 will be able to authenticate to channel 
private-1, user with ID 2 to channel private-2 and so on.ROUTES += Broadcast.routes(auth_route="/pusher/user-auth")
const pusher = new Pusher("478b45309560f3456211", {
  cluster: "eu",
  userAuthentication: {
    endpoint: "/broadcast/auth",
  },
});
from masonite.middleware import VerifyCsrfToken as Middleware
class VerifyCsrfToken(Middleware):
    exempt = [
        '/pusher/user-auth'
    ]
Authorizing6/12/25, 3:02 AM Masonite Documentation