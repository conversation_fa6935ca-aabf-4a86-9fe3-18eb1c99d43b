"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreservedUserDataRepository = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const datasources_1 = require("../datasources");
const models_1 = require("../models");
let PreservedUserDataRepository = class PreservedUserDataRepository extends repository_1.DefaultCrudRepository {
    constructor(dataSource) {
        super(models_1.PreservedUserData, dataSource);
    }
    /**
     * Find preserved data by deletion record ID
     */
    async findByDeletionRecordId(deletionRecordId) {
        return this.find({
            where: { deletionRecordId },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find preserved data by type for a deletion record
     */
    async findByDeletionRecordAndType(deletionRecordId, dataType) {
        return this.find({
            where: {
                deletionRecordId,
                dataType,
            },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find expired preserved data for cleanup
     */
    async findExpiredData() {
        return this.find({
            where: {
                expiresAt: { lt: new Date() },
            },
        });
    }
    /**
     * Get data size summary for a deletion record
     */
    async getDataSizeSummary(deletionRecordId) {
        const preservedData = await this.findByDeletionRecordId(deletionRecordId);
        const totalSize = preservedData.reduce((sum, data) => sum + (data.dataSizeBytes || 0), 0);
        const dataTypes = {};
        preservedData.forEach(data => {
            dataTypes[data.dataType] = (dataTypes[data.dataType] || 0) + (data.dataSizeBytes || 0);
        });
        return { totalSize, dataTypes };
    }
};
exports.PreservedUserDataRepository = PreservedUserDataRepository;
exports.PreservedUserDataRepository = PreservedUserDataRepository = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('datasources.db')),
    tslib_1.__metadata("design:paramtypes", [datasources_1.DbDataSource])
], PreservedUserDataRepository);
//# sourceMappingURL=preserved-user-data.repository.js.map