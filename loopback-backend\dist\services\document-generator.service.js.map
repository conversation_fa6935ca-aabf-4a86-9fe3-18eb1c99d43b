{"version": 3, "file": "document-generator.service.js", "sourceRoot": "", "sources": ["../../src/services/document-generator.service.ts"], "names": [], "mappings": ";;;;AAAA,yCAAwD;AACxD,qDAAgD;AAChD,kDAAsF;AAEtF,iDAAkD;AAClD,mDAA6B;AAC7B,wDAAkC;AAClC,uDAAiC;AAyB1B,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAKnC,YAEE,2BAA+D,EAE/D,wBAAyD;QAFlD,gCAA2B,GAA3B,2BAA2B,CAA6B;QAExD,6BAAwB,GAAxB,wBAAwB,CAA0B;QARnD,qBAAgB,GAA8B,IAAI,GAAG,EAAE,CAAC;QAU9D,+CAA+C;QAC/C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,qCAAqC,CAAC,CAAC;QACvF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,mCAAmC,CAAC,CAAC;QACjF,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,UAAkB,EAClB,MAAc,EACd,OAAkC;QAElC,yBAAyB;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACjF,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC;YAC7D,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,SAAS;YACjB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,iBAAiB,EAAE,OAAO;YAC1B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;YAC5C,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACtC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAExD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,UAAkB,EAClB,OAAkC;QAElC,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,UAAU,EAAE;gBAC5D,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAElF,0BAA0B;YAC1B,MAAM,cAAc,GAAG;gBACrB,UAAU;gBACV,OAAO;gBACP,OAAO,EAAE,eAAe;gBACxB,eAAe,EAAE,IAAI,CAAC,eAAe;aACtC,CAAC;YAEF,iCAAiC;YACjC,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAE/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,UAAoB;QACnD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;YACxD,KAAK,EAAE;gBACL,EAAE,EAAE,EAAC,GAAG,EAAE,UAAU,EAAC;gBACrB,MAAM,EAAE,WAAW;aACpB;YACD,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;SACtC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,UAAkB,EAClB,cAAmB;QAEnB,MAAM,IAAI,GAAG;YACX,IAAI,CAAC,mBAAmB;YACxB,eAAe,EAAE,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;YACxC,gBAAgB,EAAE,uDAAuD;SAC1E,CAAC;QAEF,MAAM,SAAS,GAAG,IAAA,qBAAK,EAAC,QAAQ,EAAE,IAAI,EAAE;YACtC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC/B,GAAG,EAAE;gBACH,GAAG,OAAO,CAAC,GAAG;gBACd,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC;aAClD;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAEjD,0BAA0B;QAC1B,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACnC,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAChE,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACnC,OAAO,CAAC,KAAK,CAAC,aAAa,UAAU,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,qBAAqB,IAAI,EAAE,CAAC,CAAC;YAChE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,aAAa,UAAU,SAAS,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,MAAc;QACpE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAE7D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBACjC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBAChE,CAAC;qBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;oBACzC,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;oBACtD,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAkB,EAAE,IAAY;QAChE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC,CAAC;QAChG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,KAAU;QAChE,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,UAAU,EAAE;YAC5D,MAAM,EAAE,QAAQ;YAChB,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,KAAK,CAAC,OAAO,IAAI,wBAAwB;SACxD,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,UAAkB,EAAE,YAAiB;QAC1E,MAAM,IAAI,CAAC,2BAA2B,CAAC,cAAc,CACnD,UAAU,EACV,YAAY,CAAC,cAAc,EAC3B,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,MAAM,CACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CAAC,UAAkB,EAAE,cAAmB;QAC9E,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;QACzC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAEzD,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,UAAU,EAAE;YAC5D,MAAM,EAAE,WAAW;YACnB,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,kBAAkB,EAAE,GAAG;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,UAAkB;QAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE7E,OAAO;YACL,UAAU;YACV,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,cAAc,EAAE,QAAQ,CAAC,cAAc;YACvC,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,YAAY,EAAE,QAAQ,CAAC,YAAY;YACnC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,UAAU,EAAE;YAC5D,MAAM,EAAE,QAAQ;YAChB,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,8BAA8B;SAC7C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAc,EAAE,gBAAwB;QAC/D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,MAAM,GAAG,gBAAgB,KAAK,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,4BAA4B,CAAC;QACrG,OAAO,GAAG,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9C,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAAkB;QAC5C,OAAO,kBAAkB,UAAU,WAAW,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,6BAA6B;QAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;IAClD,CAAC;CACF,CAAA;AApTY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,iBAAU,EAAC,EAAC,KAAK,EAAE,mBAAY,CAAC,SAAS,EAAC,CAAC;IAOvC,mBAAA,IAAA,uBAAU,EAAC,0CAA2B,CAAC,CAAA;IAEvC,mBAAA,IAAA,uBAAU,EAAC,uCAAwB,CAAC,CAAA;6CADD,0CAA2B;QAE9B,uCAAwB;GAThD,wBAAwB,CAoTpC"}