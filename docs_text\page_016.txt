=== Page 17 ===

17Notice we used the singular form for our model. By default, Masonite ORM will check for
the plural name of the class in our database (in this case posts) by assuming the name of
the table is the plural word of the model name. We will talk about how to specify the table
explicitly in a bit.
The model created now resides inside app/models/Post.py and when we open it up it
should look like:
Simple enough, right? Like previously stated, we don't have to manipulate the model. The
model will take shape of the table as we create or change migrations.
Again, the table name that the model is attached to is the plural version of the model
name but if you called your table something different such as "user_posts" instead of
"posts" we can specify the table name explicitly:terminal
$ python craft model Post
app/models/Post.py
"""Post Model."""
from masoniteorm.models import Model
class Post(Model):
    pass
app/Post.pyTable Name6/12/25, 3:02 AM Masonite Documentation