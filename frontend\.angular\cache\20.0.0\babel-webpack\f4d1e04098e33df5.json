{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/common\";\nfunction EmailVerificationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8);\n    i0.ɵɵelement(2, \"i\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Verifying your email...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please wait while we verify your email address.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EmailVerificationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"i\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Email Verified Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 13)(8, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_6_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelement(9, \"i\", 15);\n    i0.ɵɵtext(10, \" Continue to Login \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nfunction EmailVerificationComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Verification Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 13)(8, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_7_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelement(9, \"i\", 15);\n    i0.ɵɵtext(10, \" Go to Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_7_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resendVerification());\n    });\n    i0.ɵɵelement(12, \"i\", 20);\n    i0.ɵɵtext(13, \" Request New Verification \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nfunction EmailVerificationComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Verification Link Expired\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 13)(8, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_8_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resendVerification());\n    });\n    i0.ɵɵelement(9, \"i\", 20);\n    i0.ɵɵtext(10, \" Request New Verification \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_8_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelement(12, \"i\", 15);\n    i0.ɵɵtext(13, \" Go to Login \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nexport let EmailVerificationComponent = /*#__PURE__*/(() => {\n  class EmailVerificationComponent {\n    constructor(route, router, authService) {\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.status = 'verifying';\n      this.message = '';\n      this.token = '';\n    }\n    ngOnInit() {\n      // Get token from query parameters\n      this.route.queryParams.subscribe(params => {\n        this.token = params['token'];\n        if (this.token) {\n          this.verifyEmail();\n        } else {\n          this.status = 'error';\n          this.message = 'No verification token provided.';\n        }\n      });\n    }\n    verifyEmail() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.status = 'verifying';\n          // Call the email verification endpoint\n          const response = yield _this.authService.verifyEmail(_this.token).toPromise();\n          _this.status = 'success';\n          _this.message = 'Your email has been successfully verified! You can now access all features.';\n          // Redirect to login after 3 seconds\n          setTimeout(() => {\n            _this.router.navigate(['/auth/login']);\n          }, 3000);\n        } catch (error) {\n          _this.status = 'error';\n          if (error.status === 400) {\n            _this.message = 'Invalid or expired verification token.';\n            _this.status = 'expired';\n          } else if (error.status === 404) {\n            _this.message = 'User not found or already verified.';\n          } else {\n            _this.message = 'An error occurred during verification. Please try again.';\n          }\n        }\n      })();\n    }\n    goToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    resendVerification() {\n      // This could trigger a resend verification email flow\n      // For now, redirect to login where they can request a new verification\n      this.router.navigate(['/auth/login']);\n    }\n    static #_ = this.ɵfac = function EmailVerificationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmailVerificationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EmailVerificationComponent,\n      selectors: [[\"app-email-verification\"]],\n      standalone: false,\n      decls: 9,\n      vars: 4,\n      consts: [[1, \"email-verification-container\"], [1, \"verification-card\"], [1, \"header\"], [\"class\", \"status-content verifying\", 4, \"ngIf\"], [\"class\", \"status-content success\", 4, \"ngIf\"], [\"class\", \"status-content error\", 4, \"ngIf\"], [\"class\", \"status-content expired\", 4, \"ngIf\"], [1, \"status-content\", \"verifying\"], [1, \"spinner\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"status-content\", \"success\"], [1, \"success-icon\"], [1, \"fas\", \"fa-check-circle\"], [1, \"actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-sign-in-alt\"], [1, \"status-content\", \"error\"], [1, \"error-icon\"], [1, \"fas\", \"fa-times-circle\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-envelope\"], [1, \"status-content\", \"expired\"], [1, \"warning-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"]],\n      template: function EmailVerificationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Email Verification\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, EmailVerificationComponent_div_5_Template, 7, 0, \"div\", 3)(6, EmailVerificationComponent_div_6_Template, 11, 1, \"div\", 4)(7, EmailVerificationComponent_div_7_Template, 14, 1, \"div\", 5)(8, EmailVerificationComponent_div_8_Template, 14, 1, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"verifying\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"success\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"error\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"expired\");\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".email-verification-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.verification-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;box-shadow:0 20px 60px #0000001a;max-width:500px;width:100%;overflow:hidden;animation:_ngcontent-%COMP%_slideUp .6s ease-out}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:30px;text-align:center}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600}.status-content[_ngcontent-%COMP%]{padding:40px 30px;text-align:center}.status-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:20px 0 15px;font-size:24px;font-weight:600}.status-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:16px;line-height:1.5;margin-bottom:30px}.spinner[_ngcontent-%COMP%]{font-size:48px;color:#667eea;margin-bottom:20px}.success-icon[_ngcontent-%COMP%]{font-size:64px;color:#4caf50;margin-bottom:20px}.error-icon[_ngcontent-%COMP%]{font-size:64px;color:#f44336;margin-bottom:20px}.warning-icon[_ngcontent-%COMP%]{font-size:64px;color:#ff9800;margin-bottom:20px}.verifying[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#667eea}.success[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#4caf50}.error[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#f44336}.expired[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#ff9800}.actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px;margin-top:30px}.btn[_ngcontent-%COMP%]{padding:14px 28px;border:none;border-radius:8px;font-size:16px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:10px;text-decoration:none}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #667eea4d}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#667eea;border:2px solid #e9ecef}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef;border-color:#667eea;transform:translateY(-2px)}@media (max-width: 600px){.email-verification-container[_ngcontent-%COMP%]{padding:10px}.verification-card[_ngcontent-%COMP%]{margin:10px}.header[_ngcontent-%COMP%]{padding:20px}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px}.status-content[_ngcontent-%COMP%]{padding:30px 20px}.status-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px}.actions[_ngcontent-%COMP%]{margin-top:20px}}\"]\n    });\n  }\n  return EmailVerificationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}