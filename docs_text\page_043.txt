=== Page 44 ===

44Feature maintainers must already have significant contributions to development of the
repository they are trying to be a Feature Maintainer for. Although they do not have to be
contributors to the actual feature they plan to maintain.
If you don't want to touch the code and instead want to just look at it and figure it out,
contribute some comments! Comments are an excellent way for future developers to
read and understand the framework. Masonite strives on being extremely commented.
Although most of the code itself does not need to be commented, some of the classes,
modules, methods and functions do (although a lot of them already are).
Comments don't affect the working code so if you want to get used to contributing to
open source or you just don't quite understand what a class method is doing or you are
afraid of contributing and breaking the project (there are tests) then contributing
comments is right for you!
Masonite package requires testing. If you want to search through all the tests in the
tests directories of those repositories and write additional tests and use cases then that
will be great! There are already over 100 tests but you can always write more. With more
testing comes more stability. Especially as people start to contribute to the project.
Check the tests that are already there and write any use cases that are missing. These
tests can be things such as special characters in a url or other oddities that may not have
been thought of when using TDD for that feature.
Once familiar with the project (by either contributing or by building application using the
framework) it would be excellent if you could write or record tutorials and put them on 
Medium or YouTube. In order for the framework to be successful, it needs to have a
Comment the Code
Write Tests
Contribute to Tutorials6/12/25, 3:02 AM Masonite Documentation