=== Page 70 ===

70Y<PERSON> can redirect by flashing form input into session, to re-populate the form when there
are errors:
Response headers are any headers that will be found on the response. Masonite takes
care of all the important headers for you but there are times where you want to set you
own custom headers.
Most incoming headers you want to get can be found on the request class but if you need
to get any headers on the response:
Any responses you want to be returned should be set on the response class:def show(self, request: Request, response: Response):
    errors = request.validate({
        "name": required
    })
    return response.redirect('/home').with_errors(errors)
def show(self, request: Request, response: Response):
    errors = request.validate({
        "name": required
    })
    return response.redirect('/home').with_errors(errors).with_input()
from masonite.response import Response
def show(self, response: Response):
response.header('key')with_input
Headers6/12/25, 3:02 AM Masonite Documentation