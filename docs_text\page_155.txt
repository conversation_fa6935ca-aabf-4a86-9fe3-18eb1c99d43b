=== Page 156 ===

156Commands
Commands in Masonite are generally designed to make your development life easier.
Commands can range from creating controllers and models to installing packages and
publishing assets.
Masonite uses the cleo package for shell command feature.
Available commands for Masonite can be displayed by running:
This will show a list of commands already available for Masonite.
Every command has a documentation screen which describes the command's available
arguments and options. In order to view a command documentation, prefix the name of
the command with help. For example, to see help of serve command you can run:
Commands can be created with a simple command class inheriting from Masonite 
Command class:
python craft
python craft help serve
Creating Commands6/12/25, 3:02 AM Masonite Documentation