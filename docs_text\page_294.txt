=== Page 295 ===

295For example we may want to mock the functionality above by doing something like this in
the boot method of a Service Provider:
Notice that we specified which class should be returned whenever we resolve the Mail
class. In this case we want to resolve the default driver specified in the projects
configurations.
Instead of directly passing in a value as the second parameter we can pass in a callable
instead. The callable MUST take 2 parameters. The first parameter will be the annotation
we are trying to resolve and the second will be the container itself. Here is an example of
how the above would work with a callable:
Notice that the second parameter is a callable object. This means that it will be called
whenever we try to resolve the Mail class.
Remember: If the second parameter is a callable, it will be called. If it is a value, it will
simply be returned instead of the resolving object.from masonite import Mail
def boot(self, mail: MailManager):
    self.application.swap(Mail, 
manager.driver(self.application.make('MailConfig').DRIVER))
from masonite import Mail
from somewhere import NewObject
...
def mail_callback(obj, container):
    return NewObject
...
def boot(self):
    self.application.swap(Mail, mail_callback)Using a callable
Container Hooks6/12/25, 3:02 AM Masonite Documentation