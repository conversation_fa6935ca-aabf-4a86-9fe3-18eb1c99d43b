=== Page 263 ===

263All other rules within an explicit exception error will throw the ValueError.
In addition to using the methods provided below, you can also use each one as a pipe
delimitted string. For example these two validations are identical:
These rules are identical so use whichever feels more comfortable.try:
    errors = request.validate(
        validate.required(['user.email', 'user.id'], raises={
            'user.id': AttributeError,
            'user.email': CustomException
        }),
    )
except AttributeError as e:
    str(e) #== 'user.id is required'
except CustomException as e:
    str(e) #== 'user.email is required'
# Normal
errors = request.validate(
  validate.required(['email', 'username', 'password', 'bio']),
  validate.accepted('terms'),
  validate.length('bio', min=5, max=50),
  validate.strong('password')
)
# With Strings
errors = request.validate({
  'email': 'required',
  'username': 'required',
  'password': 'required|strong',
  'bio': 'required|length:5..50'
  'terms': 'accepted'
})String Validation6/12/25, 3:02 AM Masonite Documentation