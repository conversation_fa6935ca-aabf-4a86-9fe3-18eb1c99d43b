=== Page 471 ===

471redirection took longer than 2 seconds or what happens when the client times are not
synced correctly.
Now we have took a "get and delete" approach. So now the flash data is deleted when it is
retrieved. This means that flash data can stay in session until it is fetched.
To do this we have a new method for the "get and delete" of flash data.
If you are using the bag() helper in your templates then this:
If you are using the session() helper than you will need to take a similiar approach:
For upgrading from Orator to Masonite please read the Orator to Masonite ORM guide@if bag().any()
-  @for error in bag().messages()
+  @for error in bag().get_errors()
    <div class="alert alert-danger" role="alert">
        {{ error }}
    </div>
  @endfor
@endif
@if session().has('errors')
  @for key, error_list in session().get_flashed('errors').items()
    <div class="alert alert-danger" role="alert">
        {{ error }}
    </div>
  @endfor
@endif
Upgrading Orator6/12/25, 3:02 AM Masonite Documentation