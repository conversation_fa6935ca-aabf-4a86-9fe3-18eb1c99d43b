=== Page 304 ===

304To check that a plain-text string corresponds to a given hash you can do:
You can determine if the work factor used by the hashing protocol has changed since the
string was hashed using needs_rehash:
You can change hashing protocol configuration on the fly for all Hash methods:
You can also change protocol on the fly:from masonite.facades import Hash
Hash.check("secret", "$2b$10$3Nm9sWFYhi.GUJ...") #== True
from masonite.facades import Hash
Hash.needs_rehash("$2b$10$3Nm9sWFYhi.GUJ...") #== True
from masonite.facades import Hash
Hash.make("secret", options={"rounds": 5})
from masonite.facades import Hash
Hash.make("secret", driver="argon2", options={"memory": 512, "threads": 
8, "time": 2})Verifying a Hash needs to be re-hashed
Options6/12/25, 3:02 AM Masonite Documentation