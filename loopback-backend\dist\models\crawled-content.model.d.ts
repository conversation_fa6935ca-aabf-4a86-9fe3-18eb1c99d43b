import { Entity } from '@loopback/repository';
import { CrawlJob } from './crawl-job.model';
export declare class CrawledContent extends Entity {
    id: string;
    url: string;
    title: string;
    content?: string;
    htmlContent?: string;
    markdownContent?: string;
    contentType: string;
    depth: number;
    contentLength: number;
    statusCode: number;
    status: string;
    extractedLinks: string[];
    extractedImages: string[];
    metadata: object;
    headers: object;
    parentUrl?: string;
    errorMessage?: string;
    processingTimeMs: number;
    filePath?: string;
    fileSize?: number;
    fileHash?: string;
    isSelected: boolean;
    selectionGroup?: string;
    crawledAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    crawlJobId: string;
    constructor(data?: Partial<CrawledContent>);
}
export interface CrawledContentRelations {
    crawlJob?: CrawlJob;
}
export type CrawledContentWithRelations = CrawledContent & CrawledContentRelations;
