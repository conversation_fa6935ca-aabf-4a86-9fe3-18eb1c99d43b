=== Page 365 ===

365Cross-Site Request Forgery is a crucial security milestone to hit and Masonite 1.4 brings
that ability. With a new Service Provider and middleware, we can now add a simple {{ 
csrf_field }} to our forms and ensure we are protected from CSRF attacks.
Managers were very redundant before this release so we made it much easier to create
managers with 2 simple class attributes instead of the redundant method. Managers are
used to manage features and drivers to Masonite.
Now the constructor of all middleware is resolved by the container. This means you may
use the IOC dependency injection techniques like controller methods and drivers.
There were two unused imports in the models that Masonite created. These have been
removed completely.Changed Managers
Middleware is now resolved by the container
Fixed unused imports6/12/25, 3:02 AM Masonite Documentation