{"version": 3, "file": "account-deletion.controller.js", "sourceRoot": "", "sources": ["../../src/controllers/account-deletion.controller.ts"], "names": [], "mappings": ";;;;AAAA,6DAAsD;AACtD,yCAAsC;AACtC,qDAAgD;AAChD,yCASwB;AACxB,iDAA6E;AAC7E,kDAGyB;AACzB,0CAAmD;AAG5C,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YAES,kBAA+B,EAE/B,cAA8B,EAE9B,kBAAmD,EAEnD,sBAA8C;QAN9C,uBAAkB,GAAlB,kBAAkB,CAAa;QAE/B,mBAAc,GAAd,cAAc,CAAgB;QAE9B,uBAAkB,GAAlB,kBAAkB,CAAiC;QAEnD,2BAAsB,GAAtB,sBAAsB,CAAwB;IACpD,CAAC;IAmBE,AAAN,KAAK,CAAC,sBAAsB,CAkB1B,WAOC;QAOD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,MAAM,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC;IAiBK,AAAN,KAAK,CAAC,iBAAiB;QAIrB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAExD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7E,OAAO;YACL,kBAAkB,EAAE,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,cAAc,KAAK,sBAAsB;YAChG,cAAc,EAAE,cAAc,IAAI,SAAS;SAC5C,CAAC;IACJ,CAAC;IAgBK,AAAN,KAAK,CAAC,cAAc;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAExD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE;gBACL,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,cAAc,EAAE,sBAAsB;aACvC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,iBAAU,CAAC,QAAQ,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;QAED,qCAAqC;QACrC,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE5D,OAAO;YACL,OAAO,EAAE,0DAA0D;SACpE,CAAC;IACJ,CAAC;IAmBK,AAAN,KAAK,CAAC,cAAc;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;QAEnD,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAgBK,AAAN,KAAK,CAAC,iBAAiB;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,MAAM,CAAC,CAAC;QAErE,OAAO,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAiBK,AAAN,KAAK,CAAC,qBAAqB;QAIzB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,OAAO,IAAI,CAAC,sBAAsB,CAAC,6BAA6B,EAAE,CAAC;IACrE,CAAC;CACF,CAAA;AA5MY,8DAAyB;AA6B9B;IAjBL,IAAA,WAAI,EAAC,2BAA2B,CAAC;IACjC,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,2CAA2C;QACxD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACzB,UAAU,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBAC5B,oBAAoB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;wBACvC,iBAAiB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACpC;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,mBAAmB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBACtD,0BAA0B,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBAC7D,mBAAmB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBACtD,oBAAoB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBACvD,qBAAqB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAC;wBACpD,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACzB;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;uEAqBH;AAiBK;IAfL,IAAA,UAAG,EAAC,0BAA0B,CAAC;IAC/B,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,6BAA6B;QAC1C,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,kBAAkB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;wBACrC,cAAc,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACjC;iBACF;aACF;SACF;KACF,CAAC;;;;kEAcD;AAgBK;IAdL,IAAA,WAAI,EAAC,0BAA0B,CAAC;IAChC,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,iCAAiC;QAC9C,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC1B;iBACF;aACF;SACF;KACF,CAAC;;;;+DAsBD;AAmBK;IAjBL,IAAA,UAAG,EAAC,sBAAsB,CAAC;IAC3B,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,uCAAuC;QACpD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,0BAA0B,EAAE;gBAC1B,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,QAAQ;iBACjB;aACF;SACF;KACF,CAAC;;;;+DAMD;AAgBK;IAdL,IAAA,WAAI,EAAC,yBAAyB,CAAC;IAC/B,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,+BAA+B;QAC5C,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC1B;iBACF;aACF;SACF;KACF,CAAC;;;;kEAMD;AAiBK;IAfL,IAAA,WAAI,EAAC,0BAA0B,CAAC;IAChC,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,mCAAmC;QAChD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACzB,YAAY,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC/B;iBACF;aACF;SACF;KACF,CAAC;;;;sEAQD;oCA3MU,yBAAyB;IADrC,IAAA,6BAAY,EAAC,KAAK,CAAC;IAGf,mBAAA,IAAA,aAAM,EAAC,2BAAgB,CAAC,IAAI,CAAC,CAAA;IAE7B,mBAAA,IAAA,uBAAU,EAAC,6BAAc,CAAC,CAAA;IAE1B,mBAAA,IAAA,uBAAU,EAAC,8CAA+B,CAAC,CAAA;IAE3C,mBAAA,IAAA,aAAM,EAAC,iCAAiC,CAAC,CAAA;qDAHnB,6BAAc;QAEV,8CAA+B;QAE3B,iCAAsB;GAT5C,yBAAyB,CA4MrC;AAED,kEAAkE;AAClE,IAAa,+BAA+B,GAA5C,MAAa,+BAA+B;IAC1C,YAES,kBAAmD,EAEnD,sBAA8C;QAF9C,uBAAkB,GAAlB,kBAAkB,CAAiC;QAEnD,2BAAsB,GAAtB,sBAAsB,CAAwB;IACpD,CAAC;IAkBE,AAAN,KAAK,CAAC,eAAe,CAcnB,OAAwB;QAMxB,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAElG,OAAO,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3E,CAAC;IAkBK,AAAN,KAAK,CAAC,kBAAkB,CACM,KAAa;QAMzC,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAiBK,AAAN,KAAK,CAAC,WAAW,CAmBf,OAOC;QAKD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE;YACjC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;YAC5D,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;SACjD,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAChD,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,KAAK,EACb;YACE,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;YAC5D,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;SACjD,CACF,CAAC;IACJ,CAAC;IAgBK,AAAN,KAAK,CAAC,mBAAmB,CAcvB,OAAwB;QAExB,OAAO,CAAC,GAAG,CAAC,oDAAoD,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAEjF,OAAO,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACxE,CAAC;CACF,CAAA;AAlLY,0EAA+B;AAwBpC;IAhBL,IAAA,WAAI,EAAC,2BAA2B,CAAC;IACjC,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACzB,UAAU,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBAC5B,oBAAoB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACvC;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,OAAO,CAAC;oBACnB,UAAU,EAAE;wBACV,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACxB;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;sEAUH;AAkBK;IAjBL,IAAA,UAAG,EAAC,uCAAuC,CAAC;IAC5C,6BAAY,CAAC,IAAI,EAAE;IACnB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,kDAAkD;QAC/D,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,gBAAgB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;wBACnC,cAAc,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBAChC,oBAAoB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACvC;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,YAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;;;;yEAS5B;AAiBK;IAhBL,IAAA,WAAI,EAAC,uBAAuB,CAAC;IAC7B,6BAAY,CAAC,IAAI,EAAE;IACnB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,sCAAsC;QACnD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACzB,YAAY,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC/B;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;oBAC7B,UAAU,EAAE;wBACV,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACxB,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACvB,kBAAkB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBACrD,yBAAyB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBAC5D,kBAAkB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;wBACrD,mBAAmB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAC;qBACvD;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;kEAgCH;AAgBK;IAdL,IAAA,UAAG,EAAC,gCAAgC,CAAC;IACrC,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,uCAAuC;QACpD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC1B;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,OAAO,CAAC;oBACnB,UAAU,EAAE;wBACV,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACxB;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;0EAMH;0CAjLU,+BAA+B;IAEvC,mBAAA,IAAA,uBAAU,EAAC,8CAA+B,CAAC,CAAA;IAE3C,mBAAA,IAAA,aAAM,EAAC,iCAAiC,CAAC,CAAA;6CADf,8CAA+B;QAE3B,iCAAsB;GAL5C,+BAA+B,CAkL3C"}