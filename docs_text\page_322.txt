=== Page 323 ===

323Assert that the response has as status code between 200 and 300
Assert that the response has as 401 status code
Assert that the response has as 403 status code
Assert that the response has as 500 status code
Assert that the response has the given header name and value (if given).
Assert that the response does not have the given header.self.get("/").assertSuccessful()
self.get("/").assertUnauthorized()
self.get("/").assertForbidden()
self.get("/").assertError()
self.get("/").assertHasHeader(name, value=None)assertSuccessful
assertUnauthorized
assertForbidden
assertError
assertHasHeader
assertHeaderMissing6/12/25, 3:02 AM Masonite Documentation