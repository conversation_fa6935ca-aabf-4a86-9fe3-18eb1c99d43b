=== Page 227 ===

227To be able to add throttling per users or to implement more complex logic you should use
Limiter. Limiter are simple classes with a allow(request) method that will be
called each time a throttled HTTP request is made.
Some handy limiters are bundled into Masonite:
•GlobalLimiter
•UnlimitedLimiter
•GuestsOnlyLimiter
But you can create your own limiter:
Here we are creating a limiter authorizing 2 requests/day for guests users, 10
requests/day for authenticated non-premium users and unlimited requests for premium
users. Here we are using by(key) to define how we should identify users. (TODO:
explain more)# web.py
Route.post("/api/uploads/", 
"UploadController@create").middleware("throttle:100/day")
Route.post("/api/videos/", 
"UploadController@create").middleware("throttle:10/minute")
from masonite.rates import Limiter
class PremiumUsersLimiter(Limiter):
    def allow(self, request):
        user = request.user()
        if user:
            if user.role == "premium":
                return Limit.unlimited()
            else:
                return Limit.per_day(10).by(request.ip())
        else:
            return Limit.per_day(2).by(request.ip())Using Limiters6/12/25, 3:02 AM Masonite Documentation