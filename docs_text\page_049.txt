=== Page 50 ===

50In addition to these route verbs you can use built in routes:
There are multiple ways to bind a controller to a route.
You can use a string binding defining the controller class and its method 
{ControllerClass}@{controller_method}:
When using string binding, you must ensure that this controller class can be imported
correctly and that the controller class is in a registered controller location.
Note that this is the prefered way as it will avoid circular dependencies as no import is
required in your route file.
You can import your controllers in your route file and provide a class name or a method
class:
Here as no method has been defined the __call__ method of the class will be bound to
this route. It means that you should define this method in your controller:Route.redirect('/old', '/new', status=301)
Route.permanent_redirect('/old', '/new')
Route.get('/welcome', 'WelcomeController@show')
from app.controllers import WelcomeController
Route.get('/welcome', WelcomeController)Controller Binding
String Binding
Class Binding6/12/25, 3:02 AM Masonite Documentation