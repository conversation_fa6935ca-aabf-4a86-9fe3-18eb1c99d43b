=== Page 72 ===

72Request
The request and the response in Masonite work together to form a well formed response
that a browser can read and render. The Request class is used to fetch any incoming
cookies, headers, URL's, paths, request methods and other incoming data.
Although both the request and response classes have headers and cookies, in most
instances, when fetching cookies and headers, it should be fetched from the
Request class. When setting headers and cookies it should be done on the response
class.
To get cookies you can do so simply:
This will fetch the cookie from the incoming request headers.
You can also set cookies on the request:
Note that setting cookies on the request will NOT return the cookie as part of the
response and therefore will NOT keep the cookie from request to request.from masonite.request import Request
#..
def show(self, request: Request):
  request.cookie('Accepted-Cookies')
from masonite.request import Request
#..
def show(self, request: Request):
  request.cookie('Accepted-Cookies', 'True')Cookies6/12/25, 3:02 AM Masonite Documentation