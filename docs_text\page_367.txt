=== Page 368 ===

368The Cache driver now has an update method which can update a cache value by key. This
is useful if you want to change a key value or increment it. Storing a cache file also now
auto creates that directory. Read more about this in the Caching documentation.
Craft commands have been built from the ground up with the cleo package. It's an
excellent package that is built around the extendability of commands by using primarily
classes (instead of decorator functions). Read more under The Craft Command
documentation
It is now possible to add craft commands to craft. You can read more about how under 
The Craft Command documentation
You can now add more migration directories by adding it to the container with a key
ending in MigrationDirectory. This will add the directory to the list of directory that
run when migrate commands are ran. You can read more about this in the Creating
Packages documentation.
You can now add data to sessions using the new Sessions feature which comes with a
memory and cookie driver for storing data.
Caching Driver Update
An All New Craft Command
Adding Commands
Adding Migration Directories
Added Sessions6/12/25, 3:02 AM Masonite Documentation