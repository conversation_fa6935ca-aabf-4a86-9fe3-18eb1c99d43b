=== Page 257 ===

257errors.has('email') #== True
errors.all()
"""
{
  'email': ['Your email is required'],
  'name': ['Your name is required']
}
"""
errors.first()
"""
{
  'email': ['Your email is required']
}
"""
errors.count() #== 2
errors.json()
"""
'{"email": ["Your email is required"],"name": ["Your name is 
required"]}'
"""Checking For a Specific Error
Getting the first Key:
Getting the Number of Errors:
Converting to JSON
Get the Amount of Messages:6/12/25, 3:02 AM Masonite Documentation