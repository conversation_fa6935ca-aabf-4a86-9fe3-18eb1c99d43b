=== Page 197 ===

197To send a Slack notification without having a notifiable entity you must use the route
method
The second parameter can be a channel name, a channel IDor a webhook URL.
When specifying channel names you must keep # in the name as in the example. Based on
this name a reverse lookup will be made to find the corresponding Slack channel ID. If you
want to avoid this extra call, you can get the channel ID in your Slack workspace (right click
on a Slack channel > Copy Name > the ID is at the end of url)class User(Model, Notifiable):
    def route_notification_for_slack(self):
        """Examples for Incoming Webhooks."""
        # one webhook
        return "https://hooks.slack.com/services/..."
        # multiple webhooks
        return ["https://hooks.slack.com/services/...", 
"https://hooks.slack.com/services/..."]
class User(Model, Notifiable):
    def route_notification_for_slack(self):
        """Examples for Slack Web API."""
        # one channel name
        return "#general"
        # multiple channel name
        return ["#users", "#general"]
        # one channel ID
        return "C1234567890"
notification.route("slack", "#general").notify(Welcome())
Routing to anonymous
SMS6/12/25, 3:02 AM Masonite Documentation