=== Page 372 ===

372Previously, controllers were created as they were specified. For example:
created a DashboardController. Now the "Controller" part of the controller is appended by
default for you. Now we can just specify:
to create our DashboardController. You may was to actually just create a controller called
Dashboard. We can do this by specifying a flag:
short for "exact"
It's also really good practice to create 1 controller per "action type." For example we might
have a BlogController and a PostController. It's easy to not be sure what action
should be in what controllers or what to name your actions. Now you can create a
"Resource Controller" which will give you a list of actions such as show, store, create,
update etc etc. If what you want to do does not fit with an action you have then you may
want to consider making another controller (such as an AuthorController)
You can now create these Resource Controllers like:$ craft controller DashboardController
$ craft controller Dashboard
$ craft controller Dashboard -e
craft controller Dashboard -rAdded Resource Controllers
Added Global Views6/12/25, 3:02 AM Masonite Documentation