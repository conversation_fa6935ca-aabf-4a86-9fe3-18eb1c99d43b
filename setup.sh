#!/bin/bash

# Website Crawler & Document Generator Setup Script
# This script helps set up the development environment

set -e

echo "🚀 Website Crawler & Document Generator Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js 18+ or 20+"
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm not found. Please install npm"
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python found: $PYTHON_VERSION"
    elif command_exists python; then
        PYTHON_VERSION=$(python --version)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python not found. Please install Python 3.8+"
        exit 1
    fi
    
    # Check pip
    if command_exists pip3; then
        PIP_VERSION=$(pip3 --version)
        print_success "pip found: $PIP_VERSION"
    elif command_exists pip; then
        PIP_VERSION=$(pip --version)
        print_success "pip found: $PIP_VERSION"
    else
        print_error "pip not found. Please install pip"
        exit 1
    fi
    
    # Check PostgreSQL
    if command_exists psql; then
        PSQL_VERSION=$(psql --version)
        print_success "PostgreSQL found: $PSQL_VERSION"
    else
        print_warning "PostgreSQL not found. Please install PostgreSQL 12+"
        print_warning "You can continue setup and install PostgreSQL later"
    fi
    
    # Check conda
    if command_exists conda; then
        CONDA_VERSION=$(conda --version)
        print_success "Conda found: $CONDA_VERSION"
    else
        print_warning "Conda not found. Using system Python instead"
    fi
}

# Setup Python environment
setup_python_env() {
    print_status "Setting up Python environment..."
    
    if command_exists conda; then
        print_status "Activating conda environment: masonite-secure-env"
        # Note: conda activate doesn't work in scripts, user needs to do this manually
        print_warning "Please run: conda activate masonite-secure-env"
        print_warning "Then run this script again, or continue with system Python"
    fi
    
    print_status "Installing Python dependencies..."
    if command_exists pip3; then
        pip3 install -r loopback-backend/scripts/requirements.txt
    else
        pip install -r loopback-backend/scripts/requirements.txt
    fi
    
    print_success "Python dependencies installed"
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd loopback-backend
    
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_status "Building backend..."
    npm run build
    
    print_success "Backend setup complete"
    cd ..
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_success "Frontend setup complete"
    cd ..
}

# Create storage directories
create_storage_dirs() {
    print_status "Creating storage directories..."
    
    mkdir -p loopback-backend/storage/crawled_content
    mkdir -p loopback-backend/storage/generated_documents
    mkdir -p loopback-backend/storage/temp
    
    print_success "Storage directories created"
}

# Create environment file
create_env_file() {
    print_status "Creating environment configuration..."
    
    ENV_FILE="loopback-backend/.env"
    
    if [ -f "$ENV_FILE" ]; then
        print_warning "Environment file already exists: $ENV_FILE"
        return
    fi
    
    cat > "$ENV_FILE" << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=website_crawler
DB_USER=postgres
DB_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_this_in_production

# Storage Configuration
STORAGE_PATH=./storage

# Application Configuration
NODE_ENV=development
PORT=3002

# CORS Configuration
CORS_ORIGIN=http://localhost:4200
EOF
    
    print_success "Environment file created: $ENV_FILE"
    print_warning "Please update the database credentials and JWT secret in $ENV_FILE"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    if ! command_exists psql; then
        print_warning "PostgreSQL not found. Skipping database setup."
        print_warning "Please install PostgreSQL and run: npm run db:migrate:crawler"
        return
    fi
    
    print_status "Running database migrations..."
    cd loopback-backend
    
    # Check if we can connect to database
    if npm run db:migrate:status > /dev/null 2>&1; then
        npm run db:migrate:crawler
        print_success "Database migrations completed"
    else
        print_warning "Could not connect to database. Please:"
        print_warning "1. Ensure PostgreSQL is running"
        print_warning "2. Create database: website_crawler"
        print_warning "3. Update credentials in .env file"
        print_warning "4. Run: npm run db:migrate:crawler"
    fi
    
    cd ..
}

# Main setup function
main() {
    echo
    print_status "Starting setup process..."
    echo
    
    # Check if we're in the right directory
    if [ ! -f "README.md" ] || [ ! -d "frontend" ] || [ ! -d "loopback-backend" ]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    check_prerequisites
    echo
    
    create_storage_dirs
    echo
    
    create_env_file
    echo
    
    setup_python_env
    echo
    
    setup_backend
    echo
    
    setup_frontend
    echo
    
    setup_database
    echo
    
    print_success "Setup completed successfully! 🎉"
    echo
    print_status "Next steps:"
    echo "1. Update database credentials in loopback-backend/.env"
    echo "2. Start backend: cd loopback-backend && npm run dev"
    echo "3. Start frontend: cd frontend && ng serve"
    echo "4. Open http://localhost:4200 in your browser"
    echo
    print_status "For more information, see README.md"
}

# Run main function
main "$@"
