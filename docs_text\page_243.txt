=== Page 244 ===

244This validation will read like "user and email are required and the terms must be
accepted" (more on available rules and what they mean in a bit)
Note you can either pass in a single value or a list of values
You can simply display validation errors in views like this:from masonite.validation import Validator
from masonite.request import Request
from masonite.response import Response
def show(self, request: Request, response: Response, validate: 
Validator):
    """
    Incoming Input: {
        'user': 'username123',
        'email': '<EMAIL>',
        'terms': 'on'
    }
    """
    errors = request.validate(
        validate.required(['user', 'email']),
        validate.accepted('terms')
    )
    if errors:
        return response.back().with_errors(errors)
Displaying Errors in Views6/12/25, 3:02 AM Masonite Documentation