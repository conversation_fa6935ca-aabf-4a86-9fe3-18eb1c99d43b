=== Page 219 ===

219The default key is used to specify which queue driver in the configuration to use by
default. This needs to be the same value as one of the other keys in the configuration
dictionary.
To use the database driver you should first create a jobs table:
This will create a migration file in your migrations directory.DRIVERS = {
    "default": "async",
    "database": {
        "connection": "mysql",
        "table": "jobs",
        "failed_table": "failed_jobs",
        "attempts": 3,
        "poll": 5,
        "tz": "UTC"
    },
    "amqp": {
        "username": "guest",
        "password": "guest",
        "port": "5672",
        "vhost": "",
        "host": "localhost",
        "channel": "default",
        "queue": "masonite4",
    },
    "async": {
        "blocking": False,
        "callback": "handle",
        "mode": "threading",
        "workers": 1,
    },
}
$ python craft queue:tableDefault Queue
Database Driver6/12/25, 3:02 AM Masonite Documentation