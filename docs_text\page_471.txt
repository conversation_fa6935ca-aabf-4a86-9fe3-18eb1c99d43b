=== Page 472 ===

472Masonite 3.0 to 4.0
Masonite 4 is the biggest change in a Masonite release we have ever had. Smaller
applications may benefit from creating a new app and then copying and pasting
controllers, routes and views to the new installation.
For medium to large scale applications, you will need to go through the codebase and
upgrade everything to use the new structures we have available in Masonite 4.
It is highly recommended that you start at Masonite 3 before upgrading to Masonite 4. If
you are running any version less than Masonite 3 then please use the upgrade guide to
upgrade to each version first.
Masonite 4 drops support for Python 3.6. You will have to upgrade to Python 3.7+ in order
to run Masonite
First step of the upgrade guide is to uninstall Masonite 3 and install Masonite 4:
The next step of the upgrade guide is to replace your craft file. This is a basic file in the
root of your project that will be used whenever you run python craft. We will use this
to keep testing our server:$ pip uninstall masonite
$ pip install masonite==4.0.0Python Version
Install Masonite 4
Craft File6/12/25, 3:02 AM Masonite Documentation