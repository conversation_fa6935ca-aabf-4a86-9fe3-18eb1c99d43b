=== Page 58 ===

58Note that the controllers inherit Masonite base Controller class. This is required
for Masonite to pick up your controller class in routes.
Your controller's constructor and controller methods are resolved by Masonite's Service
Container. Because of this, you can simply typehint most of Masonite's classes in either
the constructor or the methods:
Read more about the benefits of the Service Container.
Controllers can have different response types based on what you need to return.
If you want to return a JSON response you can return a dictionary or a list:
This will return an application/json response.def __init__(self, request: Request):
  self.request = request
  #..
def show(self, request: Request):
  return request.param('1')
def show(self):
  return {"key": "value"}Dependency Injection
Responses
JSON
Strings6/12/25, 3:02 AM Masonite Documentation