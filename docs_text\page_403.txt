=== Page 404 ===

404This is why the Auth class no longer needs to accept the request class. Masonite will
inject the request class for you when you resolve the class.
This works with all classes and even your custom classes to help manage your
application dependencies
You can now do something like:
Learn more in the Authentication documentation here.
Previously, each route's regex was being compiled when Masonite checked for it but we
realized this was redundant. So now all route compiling is done before the server starts.
This has given Masonite a bit of a speed boost.from masonite.auth import Auth
def show(self, auth: Auth):
    auth.login(..)
from masonite.auth import Auth
def show(self, auth: Auth):
    auth.register({
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'password': 'secret'
    })
Added new register method to the Auth class.
Changed all regex compiling to be done before
the server starts6/12/25, 3:02 AM Masonite Documentation