=== Page 324 ===

324Assert the response has the given URI value in Location header.
Assert that the response is a redirection to the given URI (if provided) or to the given
route name with the given parameters (if provided).
Assert that the request contains the given cookie name and value (if provided).
Assert that the request contains the given unencrypted cookie nameself.get("/").assertHeaderMissing(name)
self.get("/").assertLocation(uri)
self.get("/logout").assertRedirect(url=None, name=None, params={})
self.get("/logout").assertRedirect()
self.get("/logout").assertRedirect("/login")
self.get("/login").assertRedirect(name="profile", params={"user": 1})
self.get("/").assertCookie(name, value=None)
self.get("/").assertPlainCookie(name)assertLocation
assertRedirect
assertCookie
assertPlainCookie
assertCookieExpired6/12/25, 3:02 AM Masonite Documentation