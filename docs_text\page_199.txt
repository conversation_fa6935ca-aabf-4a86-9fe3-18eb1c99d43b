=== Page 200 ===

200This method should return str, dict or JSON data (as it will be saved into a TEXT
column in the notifications table). You also need to add database channel to the via
method to enable database notification storage.
Before you can store notifications in database you must create the database 
notifications table.
Then you can migrate your database
The ORM Model describing a Notification is DatabaseNotification and has the
following fields:
•id is the primary key of the model (defined with UUID4)
•type will store the notification type as a string (e.g. WelcomeNotification)
•read_at is the timestamp indicating when notification has been read
•data is the serialized representation of to_database()
•notifiable is the relationship returning the Notifiable entity a notification
belongs to (e.g. User)
•created_at, updated_at timestampsclass Welcome(Notification):
    def to_database(self, notifiable):
        return {"data": "Welcome {0}!".format(notifiable.name)}
    def via(self):
        return ["mail", "database"]
$ python craft notification:table
$ python craft migrateInitial setup6/12/25, 3:02 AM Masonite Documentation