import { UserRepository } from '../repositories';
import { User } from '../models';
import { EmailService } from './email.service';
import { SecurityService } from './security.service';
export declare class OAuthService {
    protected userRepository: UserRepository;
    protected emailService: EmailService;
    protected securityService: SecurityService;
    private googleClient;
    constructor(userRepository: UserRepository, emailService: EmailService, securityService: SecurityService);
    verifyGoogleToken(token: string): Promise<any>;
    verifyGitHubToken(accessToken: string): Promise<any>;
    verifyMicrosoftToken(accessToken: string): Promise<any>;
    findOrCreateOAuthUser(provider: string, oauthData: any): Promise<User>;
    private generateSecurePassword;
    generateOAuthUrl(provider: string, state?: string): string;
    exchangeCodeForToken(provider: string, code: string): Promise<string>;
}
