=== Page 285 ===

285We can see that we have a simple provider that registers the User model into the
container. There are three key features we have to go into detail here.
In our register method, it's important that we only bind things into the container. When
the provider is first registered to the container, the register method is ran and your
classes will be registered to the container.
The boot method will have access to everything that is registered in the container. The
boot method is ran during requests and is actually resolved by the container. Because of
this, we can actually rewrite our provider above as this:from masonite.providers import Provider
class YourProvider(Provider):
    def __init__(self, application):
        self.application = application
    def register(self):
        self.application.bind('User', User)
    def boot(self):
        print(self.application.make('User'))
Register
Boot6/12/25, 3:02 AM Masonite Documentation