=== Page 222 ===

222In order to process things on the queue, you will need to create a job. This job will be
treated as an entity that can be serialized and ran later.
For a shortcut you can run the job command to create the job:
You will now have a job class you can build out the logic for:
Any logic should be inside the handle method:
You can put jobs on the queue to process by simply passing them onto the queue:$ python craft job CreateInvoice
from masonite.queues import Queueable
class CreateInvoice(Queueable):
    def handle(self):
        pass
class CreateInvoice(Queueable):
    def __init__(self, order_id):
        self.order_id = order_id
    def handle(self):
        # Generate invoice documents
        pass
Queueing Jobs6/12/25, 3:02 AM Masonite Documentation