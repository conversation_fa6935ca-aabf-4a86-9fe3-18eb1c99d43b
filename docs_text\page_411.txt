=== Page 412 ===

412This is an internal change mostly and completely transparent to all who install Masonite.
This allows installing third party packages into Masonite with the same namespace
without namespace conflicts.
Masonite now handles the response as bytes. This allows for different classes to handle
the response in different ways.
Previously Masonite ultimately converted everything to a string at the end but some
things needed to be returned to the WSGI server as bytes (like the new file download
feature). So if you need to handle the raw response then you will now expect bytes
instead of a string.
The Scheduler has a few changes.
There are a few new methods on the tasks you can use like every, every_minute, 
every_15_minutes, every_30_minutes, every_45_minutes, daily, hourly, 
weekly, monthly.
These can either be used inside the provider or inside the command to make a more
expressive scheduling syntax.
Providers can now inherit the CanSchedule class which gives it access to the new 
self.call() method (which is used to schedule commands) and self.schedule()Changed The Way The WSGI Server Handles
Responses
Scheduler
New Methods
New Scheduling Helper Class6/12/25, 3:02 AM Masonite Documentation