=== Page 21 ===

21For simplicity sake, we won't be styling our blog with something like <PERSON>tra<PERSON> but it is
important to learn how static files such as CSS works with Masonite so let's walk through
how to add a CSS file and add it to our blog.
Firstly, head to storage/static/ and make a blog.css file and throw anything you
like in it. For this tutorial we will make the html page slightly grey.
Now we can add it to our template like so right at the top:
That's it. Static files are really simple. It's important to know how they work but for this
tutorial we will ignore them for now and focus on more of the backend.storage/static/blog.css
html {
    background-color: #ddd;
}
templates/blog.html
<link href="/static/blog.css" rel="stylesheet">
@if auth()
    <form action="/blog/create" method="POST">
        {{ csrf_field }}
        <label> Title </label>
        <input type="name" name="title"><br>
        <label> Body </label>
        <textarea name="body"></textarea>
        <input type="submit" value="Post!">
    </form>
@else
    <a href="/login">Please Login</a>
@endifStatic Files6/12/25, 3:02 AM Masonite Documentation