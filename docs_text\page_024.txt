=== Page 25 ===

25Great! So now in our show method we will show all posts and then we will create a 
single method to show a specific post.
Let's get the show method to return the posts view with all the posts:
We need to add a route for this method:
Our posts view can be very simple:$ python craft controller Post
app/controllers/PostController.py
from app.models.Post import Post
...
def show(self, view: View):
    posts = Post.all()
    return view.render('posts', {'posts': posts})
routes/web.py
Route.get('/posts', 'PostController@show')Show Method
Posts Route
Posts View6/12/25, 3:02 AM Masonite Documentation