import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DbDataSource} from '../datasources';
import {CrawlJob, CrawlJobRelations, User, CrawledContent, GeneratedDocument} from '../models';
import {UserRepository} from './user.repository';
import {CrawledContentRepository} from './crawled-content.repository';
import {GeneratedDocumentRepository} from './generated-document.repository';

export class CrawlJobRepository extends DefaultCrudRepository<
  CrawlJob,
  typeof CrawlJob.prototype.id,
  CrawlJobRelations
> {
  public readonly user: BelongsToAccessor<User, typeof CrawlJob.prototype.id>;
  public readonly crawledContents: HasManyRepositoryFactory<CrawledContent, typeof CrawlJob.prototype.id>;
  public readonly generatedDocuments: HasManyRepositoryFactory<GeneratedDocument, typeof CrawlJob.prototype.id>;

  constructor(
    @inject('datasources.db') dataSource: DbDataSource,
    @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
    @repository.getter('CrawledContentRepository') protected crawledContentRepositoryGetter: Getter<CrawledContentRepository>,
    @repository.getter('GeneratedDocumentRepository') protected generatedDocumentRepositoryGetter: Getter<GeneratedDocumentRepository>,
  ) {
    super(CrawlJob, dataSource);
    
    this.generatedDocuments = this.createHasManyRepositoryFactoryFor('generatedDocuments', generatedDocumentRepositoryGetter);
    this.registerInclusionResolver('generatedDocuments', this.generatedDocuments.inclusionResolver);
    
    this.crawledContents = this.createHasManyRepositoryFactoryFor('crawledContents', crawledContentRepositoryGetter);
    this.registerInclusionResolver('crawledContents', this.crawledContents.inclusionResolver);
    
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
  }

  /**
   * Find crawl jobs by user ID
   */
  async findByUserId(userId: string): Promise<CrawlJob[]> {
    return this.find({
      where: {userId},
      order: ['createdAt DESC'],
      include: ['crawledContents', 'generatedDocuments'],
    });
  }

  /**
   * Find active crawl jobs (running or pending)
   */
  async findActiveCrawlJobs(): Promise<CrawlJob[]> {
    return this.find({
      where: {
        status: {inq: ['pending', 'running', 'paused']},
      },
      order: ['createdAt ASC'],
    });
  }

  /**
   * Find crawl jobs by status
   */
  async findByStatus(status: string): Promise<CrawlJob[]> {
    return this.find({
      where: {status},
      order: ['createdAt DESC'],
    });
  }

  /**
   * Update crawl job progress
   */
  async updateProgress(
    id: string,
    processedPages: number,
    totalPages: number,
    status?: string,
  ): Promise<void> {
    const progressPercentage = totalPages > 0 ? Math.round((processedPages / totalPages) * 100) : 0;
    
    const updateData: Partial<CrawlJob> = {
      processedPages,
      totalPages,
      progressPercentage,
      updatedAt: new Date(),
    };

    if (status) {
      updateData.status = status;
      if (status === 'running' && !updateData.startedAt) {
        updateData.startedAt = new Date();
      } else if (status === 'completed' || status === 'failed') {
        updateData.completedAt = new Date();
      }
    }

    await this.updateById(id, updateData);
  }

  /**
   * Get crawl job statistics for a user
   */
  async getUserCrawlStatistics(userId: string): Promise<object> {
    const jobs = await this.find({where: {userId}});
    
    const stats = {
      totalJobs: jobs.length,
      completedJobs: jobs.filter(job => job.status === 'completed').length,
      failedJobs: jobs.filter(job => job.status === 'failed').length,
      runningJobs: jobs.filter(job => job.status === 'running').length,
      pendingJobs: jobs.filter(job => job.status === 'pending').length,
      totalPagesProcessed: jobs.reduce((sum, job) => sum + job.processedPages, 0),
      totalPagesCrawled: jobs.reduce((sum, job) => sum + job.totalPages, 0),
    };

    return stats;
  }

  /**
   * Clean up old completed crawl jobs
   */
  async cleanupOldJobs(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const oldJobs = await this.find({
      where: {
        status: {inq: ['completed', 'failed', 'cancelled']},
        completedAt: {lt: cutoffDate},
      },
    });

    // Delete related content and documents first
    for (const job of oldJobs) {
      await this.crawledContents(job.id).delete();
      await this.generatedDocuments(job.id).delete();
    }

    // Delete the jobs
    await this.deleteAll({
      status: {inq: ['completed', 'failed', 'cancelled']},
      completedAt: {lt: cutoffDate},
    });

    return oldJobs.length;
  }
}
