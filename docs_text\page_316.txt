=== Page 317 ===

317To check if a route exists, we can simple use either get or post:
You may use the withHeaders() method to customize the request's headers before it is
sent to the application. This method allows you to add any headers you would like to the
request by providing them as a dict:
You may use the withCookies() method to set cookie values before making a request.
This method accepts a dictionary of name / value pairs:
If you want to make authenticated requests in your tests, you can use the actingAs()
method that takes a given User record and authenticate him during the request.def test_route_exists(self):
    self.assertTrue(self.get('/testing'))
    self.assertTrue(self.post('/testing'))
request = self.withHeaders({"X-TEST": "value"}).get("/").request
self.assertEqual(request.header("X-Test"), "value")
self.withCookies({"test": "value"}).get("/").assertCookie("test", 
"value")
user = User.find(1)
self.actingAs(user).get("/")Checking if a route exists
Request Headers
Request Cookies
Authentication6/12/25, 3:02 AM Masonite Documentation