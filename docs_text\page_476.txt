=== Page 477 ===

477To this:
Masonite 4 uses the same Masonite ORM package as Masonite 3 but changes a lot of
the authentication.
Inherit a new Authenticates classclass AdminMiddleware:
def __init__(self, request: Request):
        """Inject Any Dependencies From The Service Container.
        Arguments:
            Request {masonite.request.Request} -- The Masonite request 
object
        """
        self.request = request
    def before(self):
      if not optional(self.request.user()).admin == 1:
            self.request.redirect('/')
    
    def after(self):
      pass
class AdminMiddleware:
    def before(self, request, response):
      if not optional(request.user()).admin == 1:
            return response.redirect('/')
    
    def after(self, request, response):
      pass
from masonite.authentication import Authenticates
from masoniteorm.models import Model
class User(Model, Authenticates):
  # ..User Model & Authentication6/12/25, 3:02 AM Masonite Documentation