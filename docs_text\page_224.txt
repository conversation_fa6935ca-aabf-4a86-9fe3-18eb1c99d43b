=== Page 225 ===

225Rate Limiting
Masonite includes a rate limiting feature which make it really easy to limit an action
during a given time window.
The most frequent use of this feature is to add throttling to some API endpoints but you
could also use this to limit how many time a model can be updated, a job can be run in
the queue or a mail should be sent.
This feature is based on the application's Cache features. It will store the number of
attempts of a given key and will also store the associated time window.
Rate Limiting feature can be accessed via the container application.make("rate") or
by using the RateLimiter facade.
To limit an action, we need:
•a key that will uniquely identify the action
•a number of authorized attempts
•a delay after which number of attempts will be reset
Before we can start using the rate limiting features of Masonite, we need to register the 
RateProvider class in our providers list:
from masonite.providers import RateProvider
# ..
PROVIDERS = [
    #..
    RateProvider,
]Overview
Setup6/12/25, 3:02 AM Masonite Documentation