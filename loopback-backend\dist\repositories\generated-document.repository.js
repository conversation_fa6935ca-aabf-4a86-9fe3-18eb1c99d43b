"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneratedDocumentRepository = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const datasources_1 = require("../datasources");
const models_1 = require("../models");
let GeneratedDocumentRepository = class GeneratedDocumentRepository extends repository_1.DefaultCrudRepository {
    constructor(dataSource, crawlJobRepositoryGetter, userRepositoryGetter) {
        super(models_1.GeneratedDocument, dataSource);
        this.crawlJobRepositoryGetter = crawlJobRepositoryGetter;
        this.userRepositoryGetter = userRepositoryGetter;
        this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter);
        this.registerInclusionResolver('user', this.user.inclusionResolver);
        this.crawlJob = this.createBelongsToAccessorFor('crawlJob', crawlJobRepositoryGetter);
        this.registerInclusionResolver('crawlJob', this.crawlJob.inclusionResolver);
    }
    /**
     * Find documents by crawl job ID
     */
    async findByCrawlJobId(crawlJobId) {
        return this.find({
            where: { crawlJobId },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find documents by user ID
     */
    async findByUserId(userId) {
        return this.find({
            where: { userId },
            order: ['createdAt DESC'],
            include: ['crawlJob'],
        });
    }
    /**
     * Find documents by status
     */
    async findByStatus(status) {
        return this.find({
            where: { status },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find documents by format
     */
    async findByFormat(format, userId) {
        const where = { format };
        if (userId) {
            where.userId = userId;
        }
        return this.find({
            where,
            order: ['createdAt DESC'],
        });
    }
    /**
     * Update document generation progress
     */
    async updateProgress(id, processedPages, totalPages, status) {
        const progressPercentage = totalPages > 0 ? Math.round((processedPages / totalPages) * 100) : 0;
        const updateData = {
            processedPages,
            totalPages,
            progressPercentage,
            updatedAt: new Date(),
        };
        if (status) {
            updateData.status = status;
            if (status === 'generating' && !updateData.startedAt) {
                updateData.startedAt = new Date();
            }
            else if (status === 'completed' || status === 'failed') {
                updateData.completedAt = new Date();
            }
        }
        await this.updateById(id, updateData);
    }
    /**
     * Record document download
     */
    async recordDownload(id) {
        const document = await this.findById(id);
        await this.updateById(id, {
            downloadCount: document.downloadCount + 1,
            lastDownloadedAt: new Date(),
            updatedAt: new Date(),
        });
    }
    /**
     * Find expired documents
     */
    async findExpiredDocuments() {
        const now = new Date();
        return this.find({
            where: {
                expiresAt: { lt: now },
                status: 'completed',
            },
        });
    }
    /**
     * Get user document statistics
     */
    async getUserDocumentStatistics(userId) {
        const documents = await this.find({ where: { userId } });
        const stats = {
            totalDocuments: documents.length,
            completedDocuments: documents.filter(doc => doc.status === 'completed').length,
            failedDocuments: documents.filter(doc => doc.status === 'failed').length,
            pendingDocuments: documents.filter(doc => doc.status === 'pending').length,
            generatingDocuments: documents.filter(doc => doc.status === 'generating').length,
            totalDownloads: documents.reduce((sum, doc) => sum + doc.downloadCount, 0),
            totalFileSize: documents.reduce((sum, doc) => sum + doc.fileSize, 0),
            documentsByFormat: this.groupDocumentsByFormat(documents),
            documentsByOrganization: this.groupDocumentsByOrganization(documents),
        };
        return stats;
    }
    /**
     * Group documents by format
     */
    groupDocumentsByFormat(documents) {
        const grouped = {};
        for (const doc of documents) {
            const format = doc.format || 'unknown';
            grouped[format] = (grouped[format] || 0) + 1;
        }
        return grouped;
    }
    /**
     * Group documents by organization type
     */
    groupDocumentsByOrganization(documents) {
        const grouped = {};
        for (const doc of documents) {
            const orgType = doc.organizationType || 'unknown';
            grouped[orgType] = (grouped[orgType] || 0) + 1;
        }
        return grouped;
    }
    /**
     * Clean up expired documents
     */
    async cleanupExpiredDocuments() {
        const expiredDocs = await this.findExpiredDocuments();
        // In a real implementation, you would also delete the actual files
        // For now, just delete the database records
        if (expiredDocs.length > 0) {
            const expiredIds = expiredDocs.map(doc => doc.id);
            await this.deleteAll({
                id: { inq: expiredIds },
            });
        }
        return expiredDocs.length;
    }
    /**
     * Find documents ready for cleanup (old completed documents)
     */
    async findDocumentsForCleanup(daysOld = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);
        return this.find({
            where: {
                status: 'completed',
                completedAt: { lt: cutoffDate },
            },
        });
    }
};
exports.GeneratedDocumentRepository = GeneratedDocumentRepository;
exports.GeneratedDocumentRepository = GeneratedDocumentRepository = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('datasources.db')),
    tslib_1.__param(1, repository_1.repository.getter('CrawlJobRepository')),
    tslib_1.__param(2, repository_1.repository.getter('UserRepository')),
    tslib_1.__metadata("design:paramtypes", [datasources_1.DbDataSource, Function, Function])
], GeneratedDocumentRepository);
//# sourceMappingURL=generated-document.repository.js.map