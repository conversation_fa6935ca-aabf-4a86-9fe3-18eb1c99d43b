=== Page 374 ===

374If we put the annotation in the beginning it would have thrown an error because of how
the container resolved.
Now we can put them in any order and the container will grab each one and resolve it.
This will now work when previously it did not.
The container will now resolve instances of classes as well. It's a common paradigm to
"code to an interface and not an implementation." Because of this paradigm, Masonite
comes with contracts that act as interfaces but in addition to this, we can also resolve
instances of classes.
For example, all Upload drivers inherit the UploadContract contract so we can simply
resolve the UploadContract which will return an Upload Driver:
Notice here that we annotated an UploadContract but got back the actual upload driver.
You can now search the container and "collect" objects from it by key using the new
collect method:from masonite.request import Request
def show(self, Upload, request: Request, Broadcast):
    pass
from masonite.contracts.UploadContract import UploadContract
def show(self, upload: UploadContract):
    upload.store(...)Resolving Instances
Container Collection6/12/25, 3:02 AM Masonite Documentation