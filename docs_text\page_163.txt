=== Page 164 ===

164Events
Masonite ships with a "pub and sub" style events feature that allows you to subscirbe to
various events and run listeners, or additional logic, when those events get emitted.
The first step in events is creating an event to listen to.
Events are simple classes that you can create wherever you like:
This will create a simple class we can later emit.
You can also fire events without an Event class. The event will just be a specific key you
can listen to.
The listener will run the logic when the event is emitted. You can create as many listeners
as you like and register as many listeners to events as you need to.
To create a listener simply run the command:
This will create a class like this:$ python craft event UserAdded
$ python craft listener WelcomeEmail
class WelcomeEmail:
    def handle(self, event):
        passCreating an Event
Creating A Listener6/12/25, 3:02 AM Masonite Documentation