=== Page 208 ===

208Y<PERSON> can now start developing your package !
There are man ways to create a virtual environment in Python but here is a simple way to
do it:
Activating your virtual environment on Mac and Linux is simple:
or if you are on Windows:
The default package layout contains a Makefile that help getting started quickly. You just
have to run:
This will install Masonite, development tools such as flake8 and pytest and it will
also install your package locally so that you can start using and testing it in the test
project directly.
Now you can run the tests to make sure everything is working properly:$ python3 -m venv venv
$ source venv/bin/activate
$ ./venv/Scripts/activate
$ make initDevelopment process
Creating and activating your virtual environment
Initializing your environment
Running the tests6/12/25, 3:02 AM Masonite Documentation