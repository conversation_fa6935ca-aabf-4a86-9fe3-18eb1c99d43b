=== Page 232 ===

232Sessions
Masonite comes with a simple way to store sessions. Masonite supports the following
session drivers: <PERSON><PERSON> and <PERSON><PERSON>.
Session configuration is located at config/session.py file. In this file, you can
configure which driver to use.
Masonite is configured to use the <PERSON><PERSON> session driver by default, named cookie.
Cookie driver will store all session data in the users cookies. It can be used as is.
Redis driver is requiring the redis python package, that you can install with:DRIVERS = {
    "default": "cookie",
    "cookie": {},
    "redis": {
        "host": "127.0.0.1",
        "port": 6379,
        "password": "",
        "options": {"db": 1},  # redis module driver specific options
        "timeout": 60 * 60,
        "namespace": "masonite4",
    },
}
pip install redisConfiguration
Cookie
Redisconfig/session.py6/12/25, 3:02 AM Masonite Documentation