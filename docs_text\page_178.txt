=== Page 179 ===

179Helpers
Masonite has several helpers available. Many helpers are used internally to help write
clean Masonite code but can be used in your projects as well.
The Url helper allows you to create a full path URL:
The URL will come from the APP_URL in your application config file.
It accepts a dictionary to add query string parameters when building the url:
You can also generate a URL for an asset:
You can also generate a URL for a route by its route name:from masonite.helpers import url
url.url("/dashboard") #== example.com/dashboard
url.url("/search/users",  query_params={"search": "John Doe" "order": 
"asc"})
#== http://masonite.app/users/?search=John%2Doe&order=asc
url.asset("s3.invoices", "invoice-01-2021.pdf")Url
Url
Asset
Route6/12/25, 3:02 AM Masonite Documentation