=== Page 128 ===

128Authorization
Masonite also provides a simple way to authorize user actions against a given resource.
This is achieved with two concepts: gates and policies. Gates are as the name suggests
an authorization check that you will be able to invoke to verify user access. Policies are a
way to groupe authorization logic around a model.
Gates are simple callable that will define if a user is authorized to perform a given action.
A handy Gate facade is available to easily manipulate gates.
Gates receive a user instance as their first argument and may receive additionals
arguments such as a Model instance. You needs to define Gates in boot() method of
your service provider.
In the following example we are adding gates to verify if a user can create and update
posts. Users can create posts if they are administrators and can update posts they have
created only.
You can then check if a gate exists or has been registed by using has() which is
returning a boolean:from masonite.facades import Gate
class MyAppProvider(Provider):
    def boot(self):
        Gate.define("create-post", lambda user: user.is_admin)
        Gate.define("update-post", lambda user, post: post.user_id == 
user.id)
Gate.has("create-post")Gates
Registering Gates6/12/25, 3:02 AM Masonite Documentation