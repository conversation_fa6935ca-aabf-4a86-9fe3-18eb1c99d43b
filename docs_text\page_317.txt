=== Page 318 ===

318The authentication guard can also be specified to authenticate the user with the given
guard:
The user will be persisted only during the lifetime of the test. Each request made during
the test will be authenticated with the given user. If you need to logout the user in the test,
you can use actingAsGuest() method:
By default, all calls to your routes with the above methods will be without CSRF
protection. The testing code will allow you to bypass that protection.
This is very useful since you don't need to worry about setting CSRF tokens on every
request but you may want to enable this protection. You can do so by calling the 
withCsrf() method on your test.
This will enable it on a specific test but you may want to enable it on all your tests. You
can do this by adding the method to your setUp() method:
Again you can disable this behaviour with withoutCsrf() method.user = User.find(1)
self.actingAs(user, "web").get("/")
def test_auth(self):
    user = User.find(1)
    self.actingAs(user).get("/home")
    self.actingAsGuest().get("/about")
def test_csrf(self):
    self.withCsrf()
    self.post("/unit/test/json", {"test": "testing"})
def setUp(self):
    super().setUp()
    self.withCsrf()CSRF Protection6/12/25, 3:02 AM Masonite Documentation