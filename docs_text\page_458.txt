=== Page 459 ===

459upgrades for a package like Masonite Validation requires waiting for the the next major
upgrade to make new breaking features and improve the package.
Now Masonite no longer requires these packages by default and requires you as the
developer to handle the versioning of them. This allows for more rapid development of
some of Masonite packages.
Masonite packages also now use SEMVER versioning. This is in the format of 
MAJOR.MINOR.PATCH. Here are the required versions you will need for Masonite 2.3:
•masonite-validation>=3.0.0
•masonite-scheduler>=3.0.0
These are the only packages that came with Masonite so you will need to now manage
the dependencies on your own. It's much better this way.
Masonite now uses a concept called guards so you will need a quick crash course on
guards. Guards are simply logic related to logging in, registering, and retrieving users. For
example we may have a web guard which handles users from a web perspective. So
registering, logging in and getting a user from a database and browser cookies.
We may also have another guard like api which handles users via a JWT token or logs
in users against the API itself.
Guards are not very hard to understand and are actually unnoticable unless you need
them.
In order for the guards to work properly you need to change your config/auth.py file to
use the newer configuration settings.
You'll need to change your settings from this:Authentication has been completely refactored6/12/25, 3:02 AM Masonite Documentation