=== Page 59 ===

59Y<PERSON> can return strings:
If you want to return a view you can resolve the view class and use the render method:
If you are using Masonite ORM, you can return a model directly:
If you want to return a redirect you can resolve the response class and use the redirect
method:
You can return any class that contains a get_response() method. This method needs
to return any one of the above response types.def show(self):
  return "welcome"
def show(self, view: View):
  return view.render("views.welcome")
from app.User import User
#..
def show(self, response: Response):
  return User.find(1)
def show(self, response: Response):
  return response.redirect('/home')Views
Models
Redirects
Other6/12/25, 3:02 AM Masonite Documentation