=== Page 269 ===

269This is useful for verifying that a value is a valid email address
Used to make sure a dictionary value is equal to a specific value
Checks to see if a key exists in the dictionary.
This is good when used with the when rule:"""
{
  'domain': 'http://google.com',
  'email': '<EMAIL>'
}
"""
validate.email('email')
"""
{
  'age': 25
}
"""
validate.equals('age', 25)
"""
{
  'email': '<EMAIL>',
  'terms': 'on'
  'age': 18
}
"""
validate.exists('terms')Equals
Exists6/12/25, 3:02 AM Masonite Documentation