=== Page 40 ===

40For example, if you want to make a new feature and you know it will not break anything
(like adding the ability to queue something) then you will branch from the master branch
following the Pull Request flow from above. The PR will be open to the master branch.
This feature will then be merged into the current release branch and be released as a new
minor version bump (4.1.0).
Bug fixes that do not break anything is the same process as above.
New features will be the same process but be branched off of the develop branch. You
will make your change and then open a pull request to the develop branch. This is a
long running branch and will be merged once the next major version of Masonite is ready
to be released.
In the interest of fostering an open and welcoming environment, we as contributors and
maintainers pledge to making participation in our project and our community a
harassment-free experience for everyone, regardless of age, body size, disability,
ethnicity, gender identity and expression, level of experience, nationality, personal
appearance, race, religion, or sexual identity and orientation.
Examples of behavior that contributes to creating a positive environment include:
•Using welcoming and inclusive language
•Being respectful of differing viewpoints and experiences
•Gracefully accepting constructive criticism
•Focusing on what is best for the community
•Showing empathy towards other community membersExamples:
Code of Conduct
Our Pledge
Our Standards6/12/25, 3:02 AM Masonite Documentation