=== Page 446 ===

446What this will do is actually type cast accordingly. If you pass a numeric value it will cast
it to an int and if you want a boolean if will cast True, true, False, false to
booleans like this:
if you don't want to cast the value you can set the cast parameter to False
We removed the store_prepend() method on the upload drivers for the filename
keyword arg on the store method.
So this:
now becomes:KEY = env('key', 'default', cast=False)
upload.store_prepend('random-string', request.input('file'))
upload.store(request.input('file'), filename='random-string')Removed Store Prepend method6/12/25, 3:02 AM Masonite Documentation