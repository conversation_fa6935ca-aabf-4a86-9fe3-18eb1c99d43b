=== Page 476 ===

476Same applies to back redirection:
Middleware has been moved to this new Kernel file. Middleware now works a little
different in M4. Middleware has changed in the following ways:
1.middleware no longer needs an __init__ method.
2.Middleware requires the request and response parameters inside the before and after
methods.
3.Middleware requires either the request or response to be returned
Middleware will change in the following example:
Old:from masonite.response import Response
def store(self, response: Response):
return response.redirect('/home')
- return request.back()
+ return response.back()
Middleware6/12/25, 3:02 AM Masonite Documentation