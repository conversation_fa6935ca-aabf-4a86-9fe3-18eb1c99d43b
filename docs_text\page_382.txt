=== Page 383 ===

383Masonite 2.1
Masonite 2.1 introduces a few new changes that are designed to correct course for the
2.x family and ensure we can go far into the 2.x family without having to make huge
breaking changes. It was questionable whether we should break from the 2.x family and
start a new 3.x line. The biggest question was removing (actually disabling) the ability to
resolve parameters and go with the more favorable annotation resolving. That could have
made Masonite a 3.x line but we have ultimately decided to go with the 2.1 as a course
correction. Below you will find all changes that went into making 2.1 awesome. Nearly all
of these changes are breaking changes.
It is much easier to contribute to Masonite now since nearly classes have awesome
docstrings explaining what they do, their dependencies and what they return.
We have completely removed parameter resolving. We can no longer resolve like this:
in favor of the more explicit:
This is a bit of a big change and will be most of the time spent on refactoring your
application to upgrade to Masonite 2.1. If you already used the more explicit version thendef show(self, Request):
    return Request.redirect('/')
from masonite.request import Request
def show(self, request: Request):
    return request.redirect('/')Introduction
All classes in core now have docstrings
Auto Resolving Parameters6/12/25, 3:02 AM Masonite Documentation