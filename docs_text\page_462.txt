=== Page 463 ===

463to this:
It's unlikely this effects you and query string parsing didn't change much but if you relied
on query strings like this:
/?filter[name]=<PERSON>&filter[user]=bob&email=<EMAIL>
Or html elements like this:
then query strings will now parse to:
You'll have to update any code that uses this. If you are not using this then don't worry
you can ignore it.
Not many breaking changes were done to the scheduler but there are alot of new
features. Head over to the Whats New in Masonite 2.3 section to read more.for provider in providers.PROVIDERS:
<input name="options[name]" value="<PERSON>">
<input name="options[user]" value="bob">
{
    "email": "<EMAIL>",
    "options": {
        "name": "<PERSON>",
        "user": "bob"
    }
}Changed How Query Strings Are Parsed
Scheduler Namespace6/12/25, 3:02 AM Masonite Documentation