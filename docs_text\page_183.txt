=== Page 184 ===

184Mail
Masonite has a simple yet powerful mail feature which is used to send emails from your
application.
To create and send an email with Masonite, you must first built a Mailable class. This
class will act as the settings for your email such as the address you are sending from, the
subject, the text of the email, the html of the email, etc.
All of these settings on the mailable can be changed or overwritten when you are ready to
send you email, more on this later on.
The first step of building a mailable is running the command:
This will create your mailable and it will look something like this:
You can send your mailable inside your controller easily by resolving the Mail class in
your controller:$ python craft mailable Welcome
class Welcome(Mailable):
    def build(self):
        (
            self.subject("Welcome to our site!")
            .from_("<EMAIL>")
            .view("mailables.welcome", {})
        )Creating Emails
Sending Mailables6/12/25, 3:02 AM Masonite Documentation