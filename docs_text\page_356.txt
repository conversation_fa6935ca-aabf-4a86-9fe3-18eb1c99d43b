=== Page 357 ===

357In your Kernel.py file adjust the route_middleware similar to this:
NOTE: the VerifyCsrfToken comes AFTER the AuthenticationMiddleware. This
means that in this example only the routes tagged as auth will check the csrf token in
form data.
•The MASONITE_LOGIN_REQUIRED can be anything you like, so making it a const of
some kind is probably advisable to prevent issues with typos.
•The above javascript assumes you have a route named loginroute_middleware = {
    "web": [SessionMiddleware, LoadUserMiddleware],
    "auth": [AuthenticationMiddleware, VerifyCsrfToken],
...
}
Notes:6/12/25, 3:02 AM Masonite Documentation