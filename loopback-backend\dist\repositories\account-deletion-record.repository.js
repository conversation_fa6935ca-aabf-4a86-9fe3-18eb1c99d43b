"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountDeletionRecordRepository = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const datasources_1 = require("../datasources");
const models_1 = require("../models");
let AccountDeletionRecordRepository = class AccountDeletionRecordRepository extends repository_1.DefaultCrudRepository {
    constructor(dataSource) {
        super(models_1.AccountDeletionRecord, dataSource);
    }
    /**
     * Find deletion record by email
     */
    async findByEmail(email) {
        const records = await this.find({
            where: { email: { ilike: email.toLowerCase() } },
            order: ['deletionRequestedAt DESC'],
            limit: 1,
        });
        return records[0] || null;
    }
    /**
     * Find active deletion records that can be restored
     */
    async findRestorableByEmail(email) {
        console.log('🔍 [findRestorableByEmail] Searching for restorable data for:', email);
        const searchCriteria = {
            email: { ilike: email.toLowerCase() },
            canRestore: true,
            deletionStatus: { inq: ['soft_deleted', 'data_preserved'] },
            dataExpiryDate: { gt: new Date() },
        };
        console.log('🔍 [findRestorableByEmail] Search criteria:', searchCriteria);
        const records = await this.find({
            where: searchCriteria,
            order: ['deletionRequestedAt DESC'],
            limit: 1,
        });
        console.log('🔍 [findRestorableByEmail] Found', records.length, 'records');
        if (records.length > 0) {
            console.log('🔍 [findRestorableByEmail] First record:', {
                id: records[0].id,
                email: records[0].email,
                status: records[0].deletionStatus,
                canRestore: records[0].canRestore,
                dataExpiryDate: records[0].dataExpiryDate
            });
        }
        return records[0] || null;
    }
    /**
     * Find expired deletion records for cleanup
     */
    async findExpiredRecords() {
        return this.find({
            where: {
                or: [
                    {
                        dataExpiryDate: { lt: new Date() },
                        deletionStatus: { neq: 'hard_deleted' },
                    }, {
                        deletionTokenExpires: { lt: new Date() },
                        deletionToken: { neq: '' },
                    }
                ]
            },
        });
    }
    /**
     * Update deletion status and completion date
     */
    async completeDeletion(id, status) {
        await this.updateById(id, {
            deletionStatus: status,
            deletionCompletedAt: new Date(),
            updatedAt: new Date(),
        });
    }
};
exports.AccountDeletionRecordRepository = AccountDeletionRecordRepository;
exports.AccountDeletionRecordRepository = AccountDeletionRecordRepository = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('datasources.db')),
    tslib_1.__metadata("design:paramtypes", [datasources_1.DbDataSource])
], AccountDeletionRecordRepository);
//# sourceMappingURL=account-deletion-record.repository.js.map