const { Client } = require('pg');

async function createDatabase() {
  // Connect to default postgres database first
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: 'password'
  });

  try {
    await client.connect();
    console.log('Connected to PostgreSQL');
    
    // Check if database exists
    const result = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'website_crawler'"
    );
    
    if (result.rows.length === 0) {
      // Create database
      await client.query('CREATE DATABASE website_crawler');
      console.log('Database website_crawler created successfully');
    } else {
      console.log('Database website_crawler already exists');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    console.log('\nPlease ensure:');
    console.log('1. PostgreSQL is running');
    console.log('2. Username/password is correct');
    console.log('3. You have permission to create databases');
  } finally {
    await client.end();
  }
}

createDatabase();
