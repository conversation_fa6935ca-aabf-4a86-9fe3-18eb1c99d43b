=== Page 145 ===

145# app/controllers/BroadcastController.py
from masonite.controllers import Controller
from masonite.request import Request
from masonite.broadcasting import Broadcast
from masonite.helpers import optional
class BroadcastController(Controller):
    def authorize(self, request: Request, broadcast: Broadcast):
        channel_name = request.input("channel_name")
        authorized = True
        # check permissions for private-admins channel else authorize 
every other channels
        if channel_name == "private-admins":
            # check that user is logged in and admin
            if optional(request.user()).role != "admin":
                authorized = False
        if authorized:
            return broadcast.driver("pusher").authorize(
                channel_name, request.input("socket_id")
            )
        else:
            return False
# app/broadcasts/UserAlert.py
from masonite.broadcasting import CanBroadcast, Channel
class AdminUserAlert(CanBroadcast):
    def __init__(self, message, level="error"):
        self.message = message
        self.level = level
    def broadcast_on(self):
        return Channel("private-admins")
    def broadcast_with(self):
        return {"message": self.message, "level": self.level}6/12/25, 3:02 AM Masonite Documentation