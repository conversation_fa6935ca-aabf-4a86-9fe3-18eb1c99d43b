=== Page 127 ===

127Guards are encapsulated logic for logging in, registering and fetching users. The web
guard uses a cookie driver which sets a token cookie which is used later to fetch the
user.
You can switch the guard on the fly to attempt authentication on different guards:POST /login Attempts a login for the user
GET /homeA home page for the user after a login attempt
succeeds
GET /register Displays a registration form for the user
POST /registerSaved the posted information and creates a
new user
GET /password_reset Displays a password reset form
POST /password_reset Attempts to reset the users password
GET /change_password Displays a form to request a new password
from masonite.authentication import Auth
from masonite.request import Request
def login(self, auth: Auth, request: Request):
  user = auth.guard("custom").attempt(request.input('email'), 
request.input("password")) #== <app.User.User>Guards6/12/25, 3:02 AM Masonite Documentation