=== Page 282 ===

282Used to make sure that value is a valid video file.
Valid video types are defined by all MIME types starting with video/. For more details
you can check mimetypes Python package which gives known MIME types with 
mimetypes.types_map.
Additionally you can check video size as with basic file validator
Conditional rules. This is used when you want to run a specific set of rules only if a first
set of rules succeeds.
For example if you want to make terms be accepted ONLY if the user is under 18"""
{
  'document': '/my/movie.mp4'
}
"""
validate.video('document')
validate.video('document', size="2MB")
"""
{
  'age': 15,
  'email': '<EMAIL>',
  'terms': 'on'
}
"""
validate.when(
    validate.less_than('age', 18)
).then(
    validate.required('terms'),
    validate.accepted('terms')
)When6/12/25, 3:02 AM Masonite Documentation