=== Page 296 ===

296Sometimes we might want to run some code when things happen inside our container.
For example we might want to run some arbitrary function about we resolve the Request
object from the container or we might want to bind some values to a View class anytime
we bind a Response to the container. This is excellent for testing purposes if we want to
bind a user object to the request whenever it is resolved.
We have three options: on_bind, on_make, on_resolve. All we need for the first
option is the key or object we want to bind the hook to, and the second option will be a
function that takes two arguments. The first argument is the object in question and the
second argument is the whole container.
The code might look something like this:
Notice that we create a function that accepts two values, the object we are dealing with
and the container. Then whenever we run on_make, the function is ran.
We can also bind to specific objects instead of keys:from masonite.request import Request
def attribute_on_make(request_obj, container):
    request_obj.attribute = 'some value'
...
container = App()
# sets the hook
container.on_make('request', attribute_on_make)
container.bind('request', Request)
# runs the attribute_on_make function
request = container.make('request')
request.attribute # 'some value'6/12/25, 3:02 AM Masonite Documentation