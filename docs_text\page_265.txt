=== Page 266 ===

266Y<PERSON> may also pass in a timezone for this rule:
This rule is used to make sure a key is "confirmed". This is simply a key_confirmation
representation of the key.
For example, if you need to confirm a password you would set the password
confirmation to password_confirmation.
This is used to make sure a value exists inside an iterable (like a list or string). You may
want to check if the string contains the value Masonite for example:"""
{
  'date': '2019-10-20', # Or date in the past
}
"""
validate.before_today('date')
"""
{
  'date': '2019-10-20', # Or date in the past
}
"""
validate.before_today('date', tz='America/New_York')
"""
{
  'password': 'secret',
  'password_confirmation': 'secret'
}
"""
validate.confirmed('password')Confirmed
Contains6/12/25, 3:02 AM Masonite Documentation