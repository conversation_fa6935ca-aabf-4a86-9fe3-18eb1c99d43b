=== Page 429 ===

429Masonite 1.6 to 2.0
Masonite 2 brings an incredible new release to the Masonite family. This release brings a
lot of new features to Masonite to include new status codes, database seeding, built in
cron scheduling, controller constructor resolving, auto-reloading server, a few new
internal ways that Masonite handles things, speed improvements to some code elements
and so much more. We think developers will be extremely happy with this release.
Upgrading from Masonite 1.6 to Masonite 2.0 shouldn't take very long although it does
have the largest amount of changes in a single release. On an average sized project, this
upgrade should take around 30 minutes. We'll walk you through the changes you have to
make to your current project and explain the reasoning behind it
Masonite 2 adds some improvements with imports. Previously we had to import
providers and drivers like:
Because of this, all framework Service Providers will need to cut out the redundant last
part. The above code should be changed to:
Masonite 2 brings a more explicit way of declaring Service Providers in your application.
You'll need to take your current PROVIDERS list inside the config/application.py file
and move it into a new config/providers.py file.
Now all Service Providers should be imported at top of the file and added to the list:from masonite.providers.UploadProvider import UploadProvider
from masonite.providers import UploadProviderApplication and Provider Configuration
config/providers.py6/12/25, 3:02 AM Masonite Documentation