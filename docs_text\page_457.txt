=== Page 458 ===

458to
Go ahead and install masonite now:
Masonite changed the way the response is generated internally so you will need to
modify how the response is retrieved internally. To do this you can go to your 
bootstrap/start.py file and scroll down to the bottom.
Change it from this:
to this:
This will allow Masonite to better handle responses. Instead of converting everything to a
string like the first snippet we can now return bytes. This is useful for returning images
and documents.
Previously Masonite used several packages and required them by default to make setting
everything up easier. This slows down package development because now any breakingmasonite>=2.2,<2.3
masonite>=2.3,<2.4
pip install "masonite>=2.3,<2.4"
return iter([bytes(container.make('Response'), 'utf-8')])
return iter([container.make('Response')])Change Server Response
Package Requirements6/12/25, 3:02 AM Masonite Documentation