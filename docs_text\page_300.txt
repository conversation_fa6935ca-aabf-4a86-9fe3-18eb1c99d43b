=== Page 301 ===

301The default is to allow all origins to access a resource on the server. Instead you can
define a list of origins allowed to access the resources defined above (paths). Wildcards
(*) can be used. This will set the Access-Control-Allow-Origin header in the
response.
Here blog.example.com and forum.example.com will e.g. be authorized to make
requests to the application paths defined above.
The default is to authorized all request headers during a CORS request, but you can
define a list of headers confirming that these are permitted headers to be used with the
actual request. This will set the Access-Control-Allow-Headers header in the
response.
The default is an empty list but you can define which headers will be accessible to the
broswser e.g. with Javascript (with getResponseHeader()). This will set the Access-
Control-Expose-Headers header in the response.    "allowed_origins": ["*.example.com"]
    "allowed_headers": ["X-Test-1", "X-Test-2"]
    "exposed_headers": ["X-Client-Test-1"]Allowed Origins
Allowed Headers
Exposed Headers
Max Age6/12/25, 3:02 AM Masonite Documentation