import { GeneratedDocumentRepository, CrawledContentRepository } from '../repositories';
import { GeneratedDocument } from '../models';
export interface DocumentGenerationOptions {
    format: 'pdf' | 'docx' | 'markdown' | 'html' | 'txt';
    organizationType: 'single_file' | 'separate_files' | 'grouped_folders';
    selectedContentIds: string[];
    destinationFolder?: string;
    template?: string;
    includeImages?: boolean;
    includeToc?: boolean;
    customStyles?: object;
    metadata?: object;
}
export interface DocumentGenerationProgress {
    documentId: string;
    status: string;
    processedPages: number;
    totalPages: number;
    currentPage?: string;
    errorMessage?: string;
    filePath?: string;
}
export declare class DocumentGeneratorService {
    generatedDocumentRepository: GeneratedDocumentRepository;
    crawledContentRepository: CrawledContentRepository;
    private activeGenerators;
    private generatorScriptPath;
    private outputDirectory;
    constructor(generatedDocumentRepository: GeneratedDocumentRepository, crawledContentRepository: CrawledContentRepository);
    /**
     * Ensure output directory exists
     */
    private ensureOutputDirectory;
    /**
     * Generate document from crawled content
     */
    generateDocument(crawlJobId: string, userId: string, options: DocumentGenerationOptions): Promise<GeneratedDocument>;
    /**
     * Start document generation process
     */
    private startGenerationProcess;
    /**
     * Get selected content for document generation
     */
    private getSelectedContent;
    /**
     * Spawn Python document generator process
     */
    private spawnGeneratorProcess;
    /**
     * Handle generator output for progress updates
     */
    private handleGeneratorOutput;
    /**
     * Handle generator process exit
     */
    private handleGeneratorExit;
    /**
     * Handle generation errors
     */
    private handleGenerationError;
    /**
     * Update generation progress
     */
    private updateGenerationProgress;
    /**
     * Handle generation completion
     */
    private handleGenerationCompletion;
    /**
     * Get document generation progress
     */
    getGenerationProgress(documentId: string): Promise<DocumentGenerationProgress>;
    /**
     * Cancel document generation
     */
    cancelGeneration(documentId: string): Promise<void>;
    /**
     * Generate filename for document
     */
    private generateFilename;
    /**
     * Generate access token for document download
     */
    private generateAccessToken;
    /**
     * Calculate document expiry date (30 days from now)
     */
    private calculateExpiryDate;
    /**
     * Generate download URL for document
     */
    private generateDownloadUrl;
    /**
     * Get active generators count
     */
    getActiveGeneratorsCount(): number;
    /**
     * Get all active generator document IDs
     */
    getActiveGeneratorDocumentIds(): string[];
}
