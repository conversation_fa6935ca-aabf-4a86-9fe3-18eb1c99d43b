=== Page 134 ===

134The create() or view_any() methods do not take a model instance, that is why the
model class should be provided so that Gate mechanism can infer from which policy
those methods are belonging to.from masonite.facades import Gate
post = Post.find(1)
Gate.allows("update", post)
Gate.denies("view", post)
Gate.allows("force_delete", post)
Gate.allows("create", Post)
Gate.allows("view_any", Post)6/12/25, 3:02 AM Masonite Documentation