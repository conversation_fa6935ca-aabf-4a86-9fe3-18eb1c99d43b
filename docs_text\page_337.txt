=== Page 338 ===

338Assert that console standard output is equal to given output.
Assert that console standard output contains given output.
Assert that console standard output does not contain the given output.
Assert that something has been output to console standard error.
Assert that console standard error is equal to given error.print("Success !")
self.assertConsoleExactOutput("Success !\n")
print("Success !")
self.assertConsoleOutputContains("Success")
print("Success !")
self.assertConsoleOutputMissing("hello")
print("An error occured !", file=sys.stderr)
self.assertConsoleExactError("An error occured !\n")assertConsoleExactOutput
assertConsoleOutputContains
assertConsoleOutputMissing
assertConsoleHasErrors
assertConsoleExactError
assertConsoleErrorContains6/12/25, 3:02 AM Masonite Documentation