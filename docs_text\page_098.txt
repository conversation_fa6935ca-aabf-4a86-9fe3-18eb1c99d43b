=== Page 99 ===

99As you see blocks are fundamental and can be defined with Jinja2 and line statements. It
allows you to structure your templates and have less repeating code.
The blocks defined in the child template will be passed to the parent template.<!-- components/blocks.html -->
<div data-gb-custom-block data-tag="extends" data-
0='components/base.html'></div>
<div data-gb-custom-block data-tag="block">
    <link rel=".." ..>
</div>
<div data-gb-custom-block data-tag="block">
    <p> This is content </p>
</div>
<div data-gb-custom-block data-tag="block">
    <script src=".." />
</div>6/12/25, 3:02 AM Masonite Documentation