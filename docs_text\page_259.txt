=== Page 260 ===

260Here is an example to make sure that street is a required field:
All errors returned will be very generic. Most times you will need to specify some custom
error that is more tailored to your user base.
Each rule has a messages keyword arg that can be used to specify your custom errors."""
{
  'domain': 'http://google.com',
  'email': '<EMAIL>'
  'user': {
     'id': 1,
     'email': '<EMAIL>',
     'addresses': [{
         'id': 1, 'street': 'A Street',
         'id': 2, 'street': 'B Street'
     }]
  }
}
"""
errors = request.validate(
    validate.required('user.addresses.*.street'),
    validate.integer('user.addresses.*.id'),
)
Custom Messages6/12/25, 3:02 AM Masonite Documentation