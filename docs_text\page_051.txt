=== Page 52 ===

52Y<PERSON> can add one or multiple Routes Middlewares:
This will attach the middleware key(s) to the route which will be picked up from your
middleware configuration later in the request.
You can exclude one or multiple Routes Middlewares for a specific route:
You can specify a name for your route. This is used to easily compile route information in
other parts of your application by using the route name which is much more static than a
URL.
You can specify the parameters in the URL that will later be able to be retrieved in other
parts of your application. You can do this easily by specify the parameter name attached
to a @ symbol:Route.get('/welcome', 'WelcomeController@show').middleware('web')
Route.get('/settings', 'WelcomeController@settings').middleware('auth', 
'web')
Route.get('/about', 
'WelcomeController@about').exclude_middleware('auth', 'custom')
Route.get('/welcome', 'WelcomeController@show').name('welcome')
Route.get('/dashboard/@user_id', 'WelcomeController@show')Name
Parameters
Optional Parameters6/12/25, 3:02 AM Masonite Documentation