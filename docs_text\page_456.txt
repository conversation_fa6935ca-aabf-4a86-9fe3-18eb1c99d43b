=== Page 457 ===

457Masonite 2.2 to 2.3
Welcome to Masonite 2.3! In this guide we will walk you through how to upgrade your
Masonite application from version 2.2 to version 2.3.
In this guide we will only be discussing the breaking changes and won't talk about how to
refactor your application to use the awesome new features that 2.3 provides. For that
information you can check the Whats New in 2.3 documentation to checkout the new
features and how to refactor.
We'll walk through both Masonite upgrades and breaking packages as well
Craft is now a part of Masonite core so you can uninstall the masonite-cli tool. You now
no longer need to use that as a package.
This is a little weird but we'll get craft back when we install Masonite 2.3
Next, we can upgrade your Masonite version. Depending on your dependancy manager it
will look something like this:
Change it from this:$ pip uninstall masonite-cliPreface
Masonite
Pip uninstall masonite-cli
Upgrade Your Masonite and CLI Version6/12/25, 3:02 AM Masonite Documentation