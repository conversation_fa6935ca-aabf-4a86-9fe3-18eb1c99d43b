=== Page 157 ===

157Com<PERSON>'s name, description and arguments are parsed from the Command docstring.
The docstring should start with the description of the command
and then after a blank line you can define the command name.
After the name of the command in the docstring should come the arguments. Arguments
are defined with one indent and enclosed into brackets.from masonite.commands import Command
class MyCommand(Command):
    """
    Description of Command
    command:signature
        {user : A positional argument for the command}
        {--f|flag : An optional argument for the command}
        {--o|option=default: An optional argument for the command with 
default value}
    """
    def handle(self):
        pass
"""
Description of Command
"""
"""
Description of Command
command_name
"""Name and Description
Positional Arguments6/12/25, 3:02 AM Masonite Documentation