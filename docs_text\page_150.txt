=== Page 151 ===

151Y<PERSON> can also see if a value exists in the cache (and it is not expired):
If you want to forget an item in the cache you can:
This will remove this item from the cache.
You can increment and decrement a value if it is an integer:
Remembering is a great way to save something to a cache using a callable:cache.has('age')
cache.forget('age')
cache.get('age') #== 21
cache.increment('age') #== 22
cache.decrement('age') #== 21
from app.models import User
cache.remember("total_users", lambda cache: (
    cache.put("total_users", User.all().count(), 300)
))Checking Data Exists
Forgetting Data
Increment / Decrement Value
Remembering6/12/25, 3:02 AM Masonite Documentation