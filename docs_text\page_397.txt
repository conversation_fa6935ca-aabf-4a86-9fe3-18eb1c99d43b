=== Page 398 ===

398Masonite 2.2
Previously you had to append all routes with a / character. This would look something
like:
You can now optionally prefix this without a / character:
Previously we had to do something like:
Now we can optionally get the parameter from the method definition:
Learn more in the Controllers documentation here.Get('/some/url')
Get('some/url')
def show(self, view: View, request: Request):
    user = User.find(request.param('user_id'))
    return view.render('some.template', {'user': user})
def show(self, user_id, view: View):
    user = User.find(user_id)
    return view.render('some.template', {'user': user})
Route Prefixes
URL parameters can now optionally be retrieved
from the controller definition6/12/25, 3:02 AM Masonite Documentation