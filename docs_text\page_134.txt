=== Page 135 ===

135Broadcasting
Masonite comes with a powerful way to broadcast events in your application. These
events can be listened to client-side using Javascript.
These can be things like a new notification which you can show on the frontend without
reloading the page.
Masonite comes with one server-side driver: Pusher.
Server side configuration for broadcasting is done in config/broadcast.py
configuration file. For now there is one driver available Pusher.
You should create an account on Pusher Channels and then create a Pusher application
on your account and get the related credentials (client, app_id, secret) and the cluster
location name and put this into the broadcast pusher options.
Finally make sure you install the pusher python package
config/broadcast.py
#..
BROADCASTS = {
    "default": "pusher",
    "pusher": {
        "driver": "pusher",
        "app_id": "3456678",
        "client": "478b45309560f3456211", # key
        "secret": "ab4229346et64aa8908",
        "cluster": "eu",
        "ssl": False,
    },
}Configuration
Server Side6/12/25, 3:02 AM Masonite Documentation