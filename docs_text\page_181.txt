=== Page 182 ===

182This will dump data in console in a nice format to ease debugging.
If you want the code to stop and renders a dump page instead you can use the dump and
die helper named dd:
This will stop code at this line and renders a nice dump page where you can see all
variables dumped until now.
Note that dumps will accumulate into session. If you want to clear dumps, you can use 
Dump.clear() or you can enable the HTTP middleware 
ClearDumpsBetweenRequestsMiddleware to clear dumps between every requests.from masonite.facades import Dump
test = 1
data = {"key": "value"}
Dump.dump(test, data)
# OR
dump(test, data)
from masonite.facades import Dump
test = 1
data = {"key": "value"}
Dump.dd(test, data)
# OR
dd(test, data)
Dump and Die
Kernel.py6/12/25, 3:02 AM Masonite Documentation