=== Page 229 ===

229A ThrottleRequestsException exception is raised and a response with status code 
429: Too Many Requests and content Too many attempts is returned.
The response can be customized to provide different status code, content and headers. It
can be done by adding a get_response() method to the limiter.
In the previous example it would look like this:
To use Rate Limiting feature we should define limits. A limit is a number of attempts
during a given time frame. It could be 100 times per day or 5 times per hour. In Masonite
limits are abstracted through the Limit class.
Here is an overview of the different ways to create limits:class PremiumUsersLimiter(Limiter):
    # ...
    def get_response(self, request, response, headers):
        if request.user():
            return response.view("Too many attempts. Upgrade to premium 
account to remove limitations.", 400)
        else:
            return response.view("Too many attempts. Please try again 
tomorrow or create an account.", 400)
from masonite.rates import Limit
Limit.from_str("100/day")
Limit.per_day(100)  # equivalent to above
Limit.per_minute(10)
Limit.per_hour(5)
Limit.unlimited()  # to define an unlimited limitCustomizing response
Defining Limits6/12/25, 3:02 AM Masonite Documentation