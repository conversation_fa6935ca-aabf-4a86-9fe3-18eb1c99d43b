=== Page 122 ===

122If you do not set a value for the expires key in the configuration file then your JWT
tokens will not expire and will be valid forever.
If you do set an expiration time in your configuration file then the JWT token will expire
after that amount of minutes. If the token expires, you will need to reauthenticate to get a
new token. This can be done easily by sending the old token to get back a new one:
You can do this by sending a POST request to /api/reauth with a token input
containing the current JWT token. This will check the table for the token and if found, will
generate a new token.
One of the issues with JWT tokens is there is little that can be done to invalidate JWT
tokens. Once a JWT token is valid, it is typically valid forever.
One way to invalid JWT tokens, and force users to reauthenticate, is to specify a version.
A JWT token authenticated will contain a version number if one exists. When the JWT
token is validated, the version number in the token will check against the version number
in the configuration file. If they do not match then the token is considered invalid and the
user will have to reauthenticate to get back a new token.
Since we store the active api_token on the table we are able to retrieve the user using the 
LoadUserMiddleware and a new guard route middleware stack:
First add the guard middleware stack and add the LoadUserMiddleware to the api
stack.Versions
Loading Users6/12/25, 3:02 AM Masonite Documentation