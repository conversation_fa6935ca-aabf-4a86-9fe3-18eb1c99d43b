=== Page 195 ===

195Notifications can be sent to a Slack workspace in two ways in Masonite:
•Slack Incoming Webhooks more here
•Slack Web API more here
Incoming Webhooks
You will need to configure an "Incoming Webhook" integration for your Slack
workspace. This integration will provide you with a URL you may use when routing Slack
notifications. This URL will target a specific Slack channel.
Web API
You will need to generate a token to interact with your Slack workspace.
This token should have at minimum the channels:read, chat:write:bot, 
chat:write:user and files:write:user permission scopes. If your token does not
have these scopes then parts of this feature will not work.
Then you can define this token globally in config/notifications.py file as 
SLACK_TOKEN environment variable. Or you can configure different tokens (with
eventually different scopes) per notifications.
Slack notifications can use Slack Blocks Kit to build more complex notifications. Before
using this you just have to install slackblocks python API to handle Block Kit
formatting..as_snippet() arguments. The file_type, 
name, and title. This
uses a different API endpoint
so some previous methods
may not be used..as_snippet(file_type='python',
name='snippet',
title='Awesome Snippet')
.token()Override the globally
configuredtoken.token('xoxp-359926262626-
35')
Advanced Formatting6/12/25, 3:02 AM Masonite Documentation