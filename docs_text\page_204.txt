=== Page 205 ===

205Using channels parameter we can send to other channels (if correctly defined in
notification class):class Welcome(Notification):
    def to_mail(self, notifiable):
        #...
    def to_slack(self, notifiable):
        #...
    def to_database(self, notifiable):
        #...
    def via(self):
        """Default behaviour is to send only by email."""
        return ["mail"]
user.notify(Welcome(), channels=["slack", "database"])
# or
notification.send(Welcome(), channels=["slack", "database"])6/12/25, 3:02 AM Masonite Documentation