=== Page 432 ===

432Again, with the addition of the above change, any place you have a duplicated class name
like:
You can change it to:
Renamed Request.redirectTo to Request.redirect_to. Be sure to change any of these
instances accordingly.
All instances of:
should be changed to:
Also removed the .send() method completely on the Request class so all instances
of:
Need to be changed to:from masonite.drivers.UploadDriver import UploadDriver
from masonite.drivers import UploadDriver
return request().redirectTo('home')
return request().redirect_to('home')
def show(self):
    return request().redirect('/dashboard/@id').send({'id': league.id})Redirection
Redirect Send Method6/12/25, 3:02 AM Masonite Documentation