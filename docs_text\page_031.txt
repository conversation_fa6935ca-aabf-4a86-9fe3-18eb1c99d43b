=== Page 32 ===

32Release Cycle
Masonite uses SEMVER versioning schema and your requirements should be fixed on any
one of these requirements depending on your dependency manager:
This will allow your application to stay up to date for all minor releases of the framework.
Masonite currently has an 8 month release cycle. Roughly 8 months we will come out
with a new major release. Masonite follows a SEMVER versioning schema
Major releases almost always contain some form of breaking changes. You should only
upgrade to major versions after careful local upgrades and testing.
Minor versions come with new features and could release every few days or every few
months depending on how many features are being added to the codebase and when
those features are ready to be released.
Minor version updates will never come with breaking changes. You should always stay up
to date with the latest minor versions of the framework.
Patch versions are small fixes or security releases.masonite>=4,<5
masonite~=4
Major Releases
Minor Versions
Patch Versions6/12/25, 3:02 AM Masonite Documentation