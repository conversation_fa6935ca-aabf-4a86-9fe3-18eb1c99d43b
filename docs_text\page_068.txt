=== Page 69 ===

69You may also pass parameters to a route if the URL requires it:
Finally you may also pass query string parameters to the url or route to redirect:
You can redirect by flashing a success message into session:
You can redirect by flashing an error message or errors messages into session:
You can directly use validation errors:Route.get('/dashboard/@user_id', 
'DashboardController@show').name('users.home')
from masonite.response import Response
def show(self, response: Response):
    return response.redirect(name='users.home', params={"user_id", 1})
def show(self, response: Response):
    return response.redirect(name='users.home', params={"user_id", 1}, 
query_params={"mode": "preview"})
#== will redirect to /dashboard/1?mode=preview
def show(self, response: Response):
    return response.redirect('/home').with_success("Preferences have 
been saved !")
def show(self, request: Request, response: Response):
    return response.redirect('/home').with_errors("This account is 
disabled !")with_success
with_errors6/12/25, 3:02 AM Masonite Documentation