=== Page 479 ===

479Change the AUTH constant to the new GUARDS configuration:
Add a new config/exceptions.py file:
The config/storage.py file has been replaced with a config/filesystem.py file:
Go to this file and copy it into your project. Then move the STATICFILES from your
storage config into this new filesystem config
Change the config/session.py to the following:GUARDS = {
    "default": "web",
    "web": {"model": User},
    "password_reset_table": "password_resets",
    "password_reset_expiration": 1440,  # in minutes. 24 hours. None if 
disabled
}
HANDLERS = {"stack_overflow": True, "solutions": True}
DRIVERS = {
    "default": "cookie",
    "cookie": {},
}Auth
Storage -> Filesystem
Session
Middleware6/12/25, 3:02 AM Masonite Documentation