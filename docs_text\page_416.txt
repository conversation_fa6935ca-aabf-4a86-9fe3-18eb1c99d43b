=== Page 417 ===

417Now if you want to set headers on the request or response and you can know which
header is for which.
Also logically it makes more sense to set response headers on the response class.
This internal rewrite also negates the need to prefix headers using HTTP_.
Cookies were suffering the same fate as headers so we changed cookies to use the
same class structure as the HeaderBag and there is now a CookieJar class that is
used to maintain Cookie classes.
The request class has also been reworked. Before, the request class was a single class
that was created when the framework booted up. This presented some challenges
because the class needed to be maintained between requests. Meaning certain inputs
and headers set on the request class needed to be reset once the request was over and
set back to a state before the request. This obviously created some weird caching bugs
between requests and instead of fixing the issues we actually just created hacky work
arounds like resetting inputs.
Now the request class is only created once a request is received. Because of this there
are now certain places that the request class is no longer accessible. For example, you
can no longer fetch the request class in the register method of service providers where
the wsgi attribute is False. You mat also not be able to fetch the request class in
some of your classes __init__ method depending on when and where the class is
being initialized.
If you are upgrading to Masonite 3 and run across an error like the request class is not
found in the container then you will need to fetch the request class later down the code.Cookies
Request Singleton6/12/25, 3:02 AM Masonite Documentation