=== Page 63 ===

63Middleware will run on every inbound request to the application whether or not a route
was found or not.
Route middleware is also simple but instead of a list, it is a dictionary with a custom
name as the key and a list of middleware. This is so we can specify the middleware
based on the key in our routes file.
In our config/middleware.py file this might look something like:
By default, all routes inside the web.py file will run the web middleware listfrom masonite.middleware import EncryptCookies
class Kernel:
    http_middleware = [
        EncryptCookies
    ]
from app.middleware.RouteMiddleware import RouteMiddleware
class Kernel:
  #..
  route_middleware = {
      "web": [
          SessionMiddleware, 
          HashIDMiddleware,
          VerifyCsrfToken,
      ],
  }Route Middleware
Middleware Parameters6/12/25, 3:02 AM Masonite Documentation