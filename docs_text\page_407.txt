=== Page 408 ===

408Recently there has been a good amount of talks about the limitations of compiling sass
with the Python libsass provider. Because of these limitations, Masonite 2.3 will
remove it completely.
A Laravel Mix file will now come with new installed applications created by craft.
The Responsable class will be able to be inherited by any class and be returned in the
controller. If the class has a get_response() method then the class will be rendered
using that method. The View class now acts in this way.
Masonite has always supported the previous 4 Python versions. Since the release of
Python 3.8 recently, Masonite will now only support Python 3.5, 3.6, 3.7 and 3.8. When
Python 3.9 comes out, Python 3.5 will be dropped.
You can still use previous versions of Masonite if you need a previous supported Python
version.
Masonite has moved away from the Auth class and towards Guard classes. These
classes will be responsable for handling everything from getting the user to handling
logging in and logging out.
There is also a much better way to register new guards so packages will be able to
register their own guards with your app which you can then use or swap on the fly.Added Laravel Mix By Default
Added Responsable Class
Dropped Support For Python 3.4
Completely Rewrote The Authentication6/12/25, 3:02 AM Masonite Documentation