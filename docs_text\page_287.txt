=== Page 288 ===

288This will load the key value pair in the providers dictionary in the container. The
dictionary after this call will look like:
The service container is available in the Request object and can be retrieved by:
Sometimes you really don't care what the key is for the object you are binding. For
example you may be binding a Markdown class into the container but really don't care
what the key binding is called. This is a great reason to use simple binding which will set
the key as the object class:from masonite.provider import ServiceProvider
from app.User import User
class UserModelProvider(ServiceProvider):
    def register(self):
        self.application.bind('User', User)
    def boot(self):
        pass
>>> app.providers
{'User': <class app.User.User>}
def show(self, request: Request):
    request.app # will return the service container
from masonite.provider import ServiceProvider
from app.User import User
class UserModelProvider(ServiceProvider):
    def register(self):
        self.application.simple(User)
    def boot(self):
        passSimple Binding6/12/25, 3:02 AM Masonite Documentation