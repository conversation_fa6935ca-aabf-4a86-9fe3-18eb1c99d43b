=== Page 302 ===

302This will set the Access-Control-Max-Age header in the response and will indicates
how long the results of a preflight request can be cached. The default is None meaning
it preflight request results will never be cached.
You can indicate a cache duration in seconds:
This will set the Access-Control-Allow-Credentials and will indicate whether or not
the response to the request can be exposed when the credentials flag is true. The
default is False.
You can read more about it here.    "max_age": 3600
    "supports_credentials": TrueSupports Credentials6/12/25, 3:02 AM Masonite Documentation