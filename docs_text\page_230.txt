=== Page 231 ===

231W<PERSON> can reset the number of attempts:
We can get the seconds in which will be able to attempt the action again:
We can get the UNIX timestamps in seconds when will be able to attempt the action
again:
Here is a complete use case, that will determine if an email should be send to the given
user:if RateLimiter.too_many_attempts(f"send_mail-{user.id}", 3):
    print("limited")
else:
    print("ok")
RateLimiter.reset_attempts(f"send_mail-{user.id}")
RateLimiter.attempts(f"send_mail-{user.id}") #== 0
RateLimiter.available_in(f"send_mail-{user.id}") #== 356
RateLimiter.available_at(f"send_mail-{user.id}") #== 1646998321
class WelcomeController(Controller):
    def welcome(self, request: Request):
        user = request.user()
        rate_key = f"send_mail_{user.id}"
        if (RateLimiter.remaining(rate_key, 2)):
            WelcomeMail(user).send()
            RateLimiter.hit(rate_key, delay=3600)
        else:
            seconds = RateLimiter.available_in(rate_key)
            return "You may try again in {seconds} seconds."6/12/25, 3:02 AM Masonite Documentation