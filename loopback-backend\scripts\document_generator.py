#!/usr/bin/env python3
"""
Document Generator Script
Generates documents from crawled content in various formats
"""

import argparse
import json
import sys
import os
import time
from typing import List, Dict, Any
from dataclasses import dataclass
import logging
from pathlib import Path

# Document generation libraries
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False

import markdown
import html2text

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ContentItem:
    url: str
    title: str
    content: str
    html_content: str
    markdown_content: str
    depth: int
    content_type: str

@dataclass
class GenerationOptions:
    format: str
    organization_type: str
    include_images: bool = False
    include_toc: bool = True
    custom_styles: Dict = None
    template: str = None
    metadata: Dict = None

class DocumentGenerator:
    def __init__(self, document_id: str, options: GenerationOptions, content: List[ContentItem], output_dir: str):
        self.document_id = document_id
        self.options = options
        self.content = content
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.processed_pages = 0
        self.total_pages = len(content)

    def generate(self) -> str:
        """Generate document based on format"""
        logger.info(f"Generating {self.options.format} document with {self.total_pages} pages")
        
        if self.options.format == 'pdf':
            return self.generate_pdf()
        elif self.options.format == 'docx':
            return self.generate_docx()
        elif self.options.format == 'markdown':
            return self.generate_markdown()
        elif self.options.format == 'html':
            return self.generate_html()
        elif self.options.format == 'txt':
            return self.generate_txt()
        else:
            raise ValueError(f"Unsupported format: {self.options.format}")

    def generate_pdf(self) -> str:
        """Generate PDF document"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab is required for PDF generation")
        
        filename = f"{self.document_id}.pdf"
        filepath = self.output_dir / filename
        
        doc = SimpleDocTemplate(str(filepath), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Add title page
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        story.append(Paragraph("Website Content Export", title_style))
        story.append(Spacer(1, 0.5*inch))
        
        # Add table of contents if requested
        if self.options.include_toc:
            story.append(Paragraph("Table of Contents", styles['Heading2']))
            for i, item in enumerate(self.content):
                toc_entry = f"{i+1}. {item.title}"
                story.append(Paragraph(toc_entry, styles['Normal']))
            story.append(PageBreak())
        
        # Add content
        for i, item in enumerate(self.content):
            self.send_progress_update(i, "Processing page: " + item.title)
            
            # Add page title
            story.append(Paragraph(item.title, styles['Heading2']))
            story.append(Paragraph(f"URL: {item.url}", styles['BodyText']))
            story.append(Spacer(1, 0.2*inch))
            
            # Add content
            # Convert HTML to plain text for PDF
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.ignore_images = not self.options.include_images
            text_content = h.handle(item.html_content)
            
            # Split content into paragraphs
            paragraphs = text_content.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    story.append(Paragraph(para.strip(), styles['BodyText']))
                    story.append(Spacer(1, 0.1*inch))
            
            if i < len(self.content) - 1:
                story.append(PageBreak())
            
            self.processed_pages += 1
        
        # Build PDF
        doc.build(story)
        
        return str(filepath)

    def generate_docx(self) -> str:
        """Generate DOCX document"""
        if not PYTHON_DOCX_AVAILABLE:
            raise ImportError("python-docx is required for DOCX generation")
        
        filename = f"{self.document_id}.docx"
        filepath = self.output_dir / filename
        
        doc = Document()
        
        # Add title
        title = doc.add_heading('Website Content Export', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add table of contents if requested
        if self.options.include_toc:
            doc.add_heading('Table of Contents', level=1)
            for i, item in enumerate(self.content):
                toc_entry = doc.add_paragraph(f"{i+1}. {item.title}")
            doc.add_page_break()
        
        # Add content
        for i, item in enumerate(self.content):
            self.send_progress_update(i, "Processing page: " + item.title)
            
            # Add page title
            doc.add_heading(item.title, level=2)
            doc.add_paragraph(f"URL: {item.url}")
            
            # Add content
            # Convert HTML to plain text
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.ignore_images = not self.options.include_images
            text_content = h.handle(item.html_content)
            
            doc.add_paragraph(text_content)
            
            if i < len(self.content) - 1:
                doc.add_page_break()
            
            self.processed_pages += 1
        
        doc.save(str(filepath))
        
        return str(filepath)

    def generate_markdown(self) -> str:
        """Generate Markdown document"""
        filename = f"{self.document_id}.md"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # Add title
            f.write("# Website Content Export\n\n")
            
            # Add table of contents if requested
            if self.options.include_toc:
                f.write("## Table of Contents\n\n")
                for i, item in enumerate(self.content):
                    f.write(f"{i+1}. [{item.title}](#{self.slugify(item.title)})\n")
                f.write("\n---\n\n")
            
            # Add content
            for i, item in enumerate(self.content):
                self.send_progress_update(i, "Processing page: " + item.title)
                
                # Add page title
                f.write(f"## {item.title}\n\n")
                f.write(f"**URL:** {item.url}\n\n")
                
                # Add content
                if item.markdown_content:
                    f.write(item.markdown_content)
                else:
                    # Convert HTML to markdown
                    h = html2text.HTML2Text()
                    h.ignore_links = False
                    h.ignore_images = not self.options.include_images
                    markdown_content = h.handle(item.html_content)
                    f.write(markdown_content)
                
                f.write("\n\n---\n\n")
                
                self.processed_pages += 1
        
        return str(filepath)

    def generate_html(self) -> str:
        """Generate HTML document"""
        filename = f"{self.document_id}.html"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # HTML header
            f.write("""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Content Export</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .url { color: #888; font-style: italic; margin-bottom: 20px; }
        .content { margin-bottom: 40px; }
        .toc { background: #f9f9f9; padding: 20px; border-radius: 5px; }
        .toc ul { list-style-type: none; padding-left: 0; }
        .toc li { margin: 5px 0; }
        .toc a { text-decoration: none; color: #007bff; }
        .toc a:hover { text-decoration: underline; }
    </style>
</head>
<body>
""")
            
            # Add title
            f.write("<h1>Website Content Export</h1>\n")
            
            # Add table of contents if requested
            if self.options.include_toc:
                f.write('<div class="toc">\n<h2>Table of Contents</h2>\n<ul>\n')
                for i, item in enumerate(self.content):
                    f.write(f'<li><a href="#{self.slugify(item.title)}">{item.title}</a></li>\n')
                f.write('</ul>\n</div>\n<hr>\n')
            
            # Add content
            for i, item in enumerate(self.content):
                self.send_progress_update(i, "Processing page: " + item.title)
                
                f.write(f'<div class="content" id="{self.slugify(item.title)}">\n')
                f.write(f"<h2>{item.title}</h2>\n")
                f.write(f'<div class="url">URL: {item.url}</div>\n')
                
                # Add content (use HTML content directly)
                f.write(item.html_content)
                
                f.write("</div>\n<hr>\n")
                
                self.processed_pages += 1
            
            # HTML footer
            f.write("</body>\n</html>")
        
        return str(filepath)

    def generate_txt(self) -> str:
        """Generate plain text document"""
        filename = f"{self.document_id}.txt"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # Add title
            f.write("WEBSITE CONTENT EXPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Add table of contents if requested
            if self.options.include_toc:
                f.write("TABLE OF CONTENTS\n")
                f.write("-" * 20 + "\n")
                for i, item in enumerate(self.content):
                    f.write(f"{i+1}. {item.title}\n")
                f.write("\n" + "=" * 50 + "\n\n")
            
            # Add content
            for i, item in enumerate(self.content):
                self.send_progress_update(i, "Processing page: " + item.title)
                
                f.write(f"{item.title}\n")
                f.write("-" * len(item.title) + "\n")
                f.write(f"URL: {item.url}\n\n")
                
                # Convert HTML to plain text
                h = html2text.HTML2Text()
                h.ignore_links = True
                h.ignore_images = True
                text_content = h.handle(item.html_content)
                
                f.write(text_content)
                f.write("\n\n" + "=" * 50 + "\n\n")
                
                self.processed_pages += 1
        
        return str(filepath)

    def slugify(self, text: str) -> str:
        """Convert text to URL-friendly slug"""
        import re
        text = re.sub(r'[^\w\s-]', '', text.lower())
        return re.sub(r'[-\s]+', '-', text).strip('-')

    def send_progress_update(self, current_page: int, current_title: str = ""):
        """Send progress update"""
        progress_data = {
            'documentId': self.document_id,
            'status': 'generating',
            'processedPages': current_page,
            'totalPages': self.total_pages,
            'currentPage': current_title,
        }
        
        print(f"PROGRESS:{json.dumps(progress_data)}")
        sys.stdout.flush()

    def send_completion_data(self, filepath: str):
        """Send completion data"""
        file_size = os.path.getsize(filepath)
        completion_data = {
            'documentId': self.document_id,
            'filePath': filepath,
            'fileSize': file_size,
        }
        
        print(f"COMPLETED:{json.dumps(completion_data)}")
        sys.stdout.flush()

def main():
    parser = argparse.ArgumentParser(description='Document Generator')
    parser.add_argument('--document-id', required=True, help='Document ID')
    parser.add_argument('--data', required=True, help='Generation data JSON')
    parser.add_argument('--callback-url', required=True, help='Callback URL for updates')
    
    args = parser.parse_args()
    
    try:
        data = json.loads(args.data)
        
        # Parse options
        options_dict = data['options']
        options = GenerationOptions(
            format=options_dict['format'],
            organization_type=options_dict['organizationType'],
            include_images=options_dict.get('includeImages', False),
            include_toc=options_dict.get('includeToc', True),
            custom_styles=options_dict.get('customStyles'),
            template=options_dict.get('template'),
            metadata=options_dict.get('metadata'),
        )
        
        # Parse content
        content_items = []
        for item_data in data['content']:
            content_item = ContentItem(
                url=item_data['url'],
                title=item_data['title'],
                content=item_data['content'],
                html_content=item_data['htmlContent'],
                markdown_content=item_data.get('markdownContent', ''),
                depth=item_data['depth'],
                content_type=item_data['contentType'],
            )
            content_items.append(content_item)
        
        # Generate document
        generator = DocumentGenerator(
            args.document_id,
            options,
            content_items,
            data['outputDirectory']
        )
        
        filepath = generator.generate()
        generator.send_completion_data(filepath)
        
        logger.info(f"Document generation completed: {filepath}")
        
    except Exception as e:
        logger.error(f"Document generation failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
