=== Page 91 ===

91Environments in views are directories of templates. If you are development packages or
building a modular based application, you may have to register different directories for
templates. This will allow Masonite to locate your views when referencing them to render.
A good place to do this is inside a service provider's register method.
There are 2 separate kinds of loaders.
The first loader is a "package" loader. This is used when registering a package. To do this
you can simply register it with the package module path. This will work for most
directories that are packages.
The other loader is a FileSystem loader. This should be used when the directory path is
NOT a module but rather just a file system path:
Below are some examples of the Jinja2 syntax which Masonite uses to build views.from masonite.facades import View
View.add_location('module_name/directory')
from jinja2.loaders import FileSystemLoader
from masonite.facades import View
View.add_location(
    os.path.join(
        os.getcwd(), 'package_views'
    )
, loader=FileSystemLoader)Adding Environments
View Syntax
Jinja26/12/25, 3:02 AM Masonite Documentation