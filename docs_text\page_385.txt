=== Page 386 ===

386<PERSON><PERSON> also noticed that for some reason we were parsing parameters before we found
routes but we only ever needed those parameters inside our routes so we were parsing
them whether we found a route or not. We moved the parsing of parameters into the if
statement that executes when a route is found.
When we say "parsing route parameters" we mean the logic required to parse this:
into a usable form to use on the request class this:
This provider has been completely removed for the more recommended
ResponseMiddleware which will need to be added to your HTTP middleware list:
We also noticed that for some reason we were parsing parameters before we found
routes but we only ever needed those parameters inside our routes so we were parsing
them whether we found a route or not. We moved the parsing of parameters into the if
statement that executes when a route is found.
When we say "parsing route parameters" we mean the logic required to parse this:/dashboard/@user/@id
from masonite.request import Request
def show(self, request: Request):
    request.param('user')
    request.param('id')
from masonite.middleware import ResponseMiddleware
..
HTTP_MIDDLEWARE=[
    ...
    ResponseMiddleware,
]StartResponse Provider
Moved parameter parsing into if statement6/12/25, 3:02 AM Masonite Documentation