=== Page 390 ===

390When you hit a route in development mode. Well you would also hit it in production mode
too since that was never turned off. Although this is likely fine, it would slow down the
framework significantly under load since it takes a bit of resources to print something
that didn't need to be printed. This enables a bit of a performance boost.
This command gets the statuses of all migrations in all directories. To include third party
migration directories that are added to your project.
Sometimes you do not need to bind an object to any key, you just want the object in the
container. For this you can now do simple bindings like this:
This new mail helper can be used globally which points to the default mail driver:GET Route: /dashboard
app.simple(Obj())
def show(self):
    mail_helper().to(..)Added migrate:status Command
Added simple container bindings
Added a new global mail helper
Removed the need for |safe filters on built in
template helpers.6/12/25, 3:02 AM Masonite Documentation