{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/two-factor.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/dialog\";\nfunction Disable2FADialogComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-icon\", 23);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"All your recovery codes have been used. You can request to disable 2FA via email verification.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Disable2FADialogComponent_mat_icon_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Disable2FADialogComponent_mat_icon_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"email\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class Disable2FADialogComponent {\n  constructor(formBuilder, twoFactorService, snackBar, dialogRef, data) {\n    this.formBuilder = formBuilder;\n    this.twoFactorService = twoFactorService;\n    this.snackBar = snackBar;\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.loading = false;\n    this.disableForm = this.formBuilder.group({\n      email: [{\n        value: data.email,\n        disabled: true\n      }, [Validators.required, Validators.email]],\n      reason: [data.allCodesUsed ? 'recovery_codes_exhausted' : 'lost_device', [Validators.required]]\n    });\n  }\n  onSubmit() {\n    if (this.disableForm.invalid) {\n      return;\n    }\n    this.loading = true;\n    const formValue = this.disableForm.getRawValue();\n    this.twoFactorService.requestDisable2FA(formValue.email, formValue.reason).subscribe({\n      next: response => {\n        this.snackBar.open('Disable confirmation email sent! Check your inbox and click the link to confirm.', 'Close', {\n          duration: 8000,\n          panelClass: ['success-snackbar']\n        });\n        this.dialogRef.close({\n          success: true,\n          response\n        });\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Failed to request 2FA disable:', error);\n        this.snackBar.open(error.error?.message || 'Failed to send disable email. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onCancel() {\n    this.dialogRef.close({\n      success: false\n    });\n  }\n  static #_ = this.ɵfac = function Disable2FADialogComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Disable2FADialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: Disable2FADialogComponent,\n    selectors: [[\"app-disable-2fa-dialog\"]],\n    decls: 65,\n    vars: 7,\n    consts: [[1, \"disable-2fa-dialog\"], [\"mat-dialog-title\", \"\", 1, \"dialog-title\"], [1, \"warning-icon\"], [1, \"dialog-content\"], [1, \"security-warning\"], [1, \"security-icon\"], [1, \"warning-text\"], [\"class\", \"status-info\", 4, \"ngIf\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"readonly\", \"\"], [\"matSuffix\", \"\"], [\"formControlName\", \"reason\", \"required\", \"\"], [\"value\", \"recovery_codes_exhausted\"], [\"value\", \"lost_device\"], [\"value\", \"other\"], [1, \"alternatives-section\"], [1, \"process-info\"], [1, \"dialog-actions\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"status-info\"], [1, \"info-icon\"]],\n    template: function Disable2FADialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" Disable Two-Factor Authentication \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-dialog-content\", 3)(6, \"div\", 4)(7, \"mat-icon\", 5);\n        i0.ɵɵtext(8, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"h3\");\n        i0.ɵɵtext(11, \"Security Warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"Disabling 2FA will make your account less secure. Are you sure you want to proceed?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(14, Disable2FADialogComponent_div_14_Template, 5, 0, \"div\", 7);\n        i0.ɵɵelementStart(15, \"form\", 8);\n        i0.ɵɵlistener(\"ngSubmit\", function Disable2FADialogComponent_Template_form_ngSubmit_15_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(16, \"mat-form-field\", 9)(17, \"mat-label\");\n        i0.ɵɵtext(18, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"input\", 10);\n        i0.ɵɵelementStart(20, \"mat-icon\", 11);\n        i0.ɵɵtext(21, \"email\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"mat-form-field\", 9)(23, \"mat-label\");\n        i0.ɵɵtext(24, \"Reason for Disabling\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"mat-select\", 12)(26, \"mat-option\", 13);\n        i0.ɵɵtext(27, \"All recovery codes used\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"mat-option\", 14);\n        i0.ɵɵtext(29, \"Lost access to authenticator device\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"mat-option\", 15);\n        i0.ɵɵtext(31, \"Other reason\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"mat-icon\", 11);\n        i0.ɵɵtext(33, \"help_outline\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 16)(35, \"h4\")(36, \"mat-icon\");\n        i0.ɵɵtext(37, \"lightbulb\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(38, \" Consider These Alternatives:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"ul\")(40, \"li\");\n        i0.ɵɵtext(41, \"Generate new recovery codes if you still have access to your authenticator\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"li\");\n        i0.ɵɵtext(43, \"Set up a new authenticator app on a different device\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"li\");\n        i0.ɵɵtext(45, \"Use email-based 2FA verification instead\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(46, \"div\", 17)(47, \"h4\")(48, \"mat-icon\");\n        i0.ɵɵtext(49, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(50, \" Email Verification Process:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"ol\")(52, \"li\");\n        i0.ɵɵtext(53, \"We'll send a secure link to your email address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"li\");\n        i0.ɵɵtext(55, \"Click the link to confirm disabling 2FA\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"li\");\n        i0.ɵɵtext(57, \"The link expires in 1 hour for security\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(58, \"mat-dialog-actions\", 18)(59, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function Disable2FADialogComponent_Template_button_click_59_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵtext(60, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function Disable2FADialogComponent_Template_button_click_61_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(62, Disable2FADialogComponent_mat_icon_62_Template, 2, 0, \"mat-icon\", 21)(63, Disable2FADialogComponent_mat_icon_63_Template, 2, 0, \"mat-icon\", 21);\n        i0.ɵɵtext(64);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngIf\", ctx.data.allCodesUsed);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.disableForm);\n        i0.ɵɵadvance(44);\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.disableForm.invalid || ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Sending...\" : \"Send Disable Email\", \" \");\n      }\n    },\n    styles: [\".disable-2fa-dialog[_ngcontent-%COMP%] {\\n      max-width: 500px;\\n      width: 100%;\\n    }\\n\\n    .dialog-title[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 12px;\\n      color: #f57c00;\\n      margin-bottom: 0;\\n    }\\n\\n    .warning-icon[_ngcontent-%COMP%] {\\n      color: #ff9800;\\n      font-size: 24px;\\n      width: 24px;\\n      height: 24px;\\n    }\\n\\n    .dialog-content[_ngcontent-%COMP%] {\\n      padding: 20px 0;\\n    }\\n\\n    .security-warning[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: flex-start;\\n      gap: 16px;\\n      background: #fff3e0;\\n      border: 1px solid #ffcc02;\\n      border-radius: 8px;\\n      padding: 16px;\\n      margin-bottom: 20px;\\n    }\\n\\n    .security-icon[_ngcontent-%COMP%] {\\n      color: #ff9800;\\n      font-size: 32px;\\n      width: 32px;\\n      height: 32px;\\n      flex-shrink: 0;\\n    }\\n\\n    .warning-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      margin: 0 0 8px 0;\\n      color: #f57c00;\\n      font-size: 16px;\\n    }\\n\\n    .warning-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 0;\\n      color: #e65100;\\n    }\\n\\n    .status-info[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 12px;\\n      background: #e3f2fd;\\n      border: 1px solid #2196f3;\\n      border-radius: 8px;\\n      padding: 12px;\\n      margin-bottom: 20px;\\n    }\\n\\n    .info-icon[_ngcontent-%COMP%] {\\n      color: #2196f3;\\n    }\\n\\n    .full-width[_ngcontent-%COMP%] {\\n      width: 100%;\\n      margin-bottom: 16px;\\n    }\\n\\n    .alternatives-section[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%] {\\n      background: #f8f9fa;\\n      border-radius: 8px;\\n      padding: 16px;\\n      margin: 16px 0;\\n    }\\n\\n    .alternatives-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n      margin: 0 0 12px 0;\\n      color: #1976d2;\\n      font-size: 14px;\\n    }\\n\\n    .alternatives-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n      margin: 0;\\n      padding-left: 20px;\\n    }\\n\\n    .alternatives-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n      margin-bottom: 8px;\\n      color: #424242;\\n    }\\n\\n    .process-info[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n      margin: 0;\\n      padding-left: 20px;\\n    }\\n\\n    .process-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n      margin-bottom: 8px;\\n      color: #424242;\\n    }\\n\\n    .dialog-actions[_ngcontent-%COMP%] {\\n      padding: 16px 0 0 0;\\n      justify-content: flex-end;\\n    }\\n\\n    .dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n      margin-left: 8px;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL2Rpc2FibGUtMmZhLWRpYWxvZy9kaXNhYmxlLTJmYS1kaWFsb2cuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGdCQUFnQjtNQUNoQixXQUFXO0lBQ2I7O0lBRUE7TUFDRSxhQUFhO01BQ2IsbUJBQW1CO01BQ25CLFNBQVM7TUFDVCxjQUFjO01BQ2QsZ0JBQWdCO0lBQ2xCOztJQUVBO01BQ0UsY0FBYztNQUNkLGVBQWU7TUFDZixXQUFXO01BQ1gsWUFBWTtJQUNkOztJQUVBO01BQ0UsZUFBZTtJQUNqQjs7SUFFQTtNQUNFLGFBQWE7TUFDYix1QkFBdUI7TUFDdkIsU0FBUztNQUNULG1CQUFtQjtNQUNuQix5QkFBeUI7TUFDekIsa0JBQWtCO01BQ2xCLGFBQWE7TUFDYixtQkFBbUI7SUFDckI7O0lBRUE7TUFDRSxjQUFjO01BQ2QsZUFBZTtNQUNmLFdBQVc7TUFDWCxZQUFZO01BQ1osY0FBYztJQUNoQjs7SUFFQTtNQUNFLGlCQUFpQjtNQUNqQixjQUFjO01BQ2QsZUFBZTtJQUNqQjs7SUFFQTtNQUNFLFNBQVM7TUFDVCxjQUFjO0lBQ2hCOztJQUVBO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixTQUFTO01BQ1QsbUJBQW1CO01BQ25CLHlCQUF5QjtNQUN6QixrQkFBa0I7TUFDbEIsYUFBYTtNQUNiLG1CQUFtQjtJQUNyQjs7SUFFQTtNQUNFLGNBQWM7SUFDaEI7O0lBRUE7TUFDRSxXQUFXO01BQ1gsbUJBQW1CO0lBQ3JCOztJQUVBO01BQ0UsbUJBQW1CO01BQ25CLGtCQUFrQjtNQUNsQixhQUFhO01BQ2IsY0FBYztJQUNoQjs7SUFFQTtNQUNFLGFBQWE7TUFDYixtQkFBbUI7TUFDbkIsUUFBUTtNQUNSLGtCQUFrQjtNQUNsQixjQUFjO01BQ2QsZUFBZTtJQUNqQjs7SUFFQTtNQUNFLFNBQVM7TUFDVCxrQkFBa0I7SUFDcEI7O0lBRUE7TUFDRSxrQkFBa0I7TUFDbEIsY0FBYztJQUNoQjs7SUFFQTtNQUNFLFNBQVM7TUFDVCxrQkFBa0I7SUFDcEI7O0lBRUE7TUFDRSxrQkFBa0I7TUFDbEIsY0FBYztJQUNoQjs7SUFFQTtNQUNFLG1CQUFtQjtNQUNuQix5QkFBeUI7SUFDM0I7O0lBRUE7TUFDRSxnQkFBZ0I7SUFDbEIiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuZGlzYWJsZS0yZmEtZGlhbG9nIHtcbiAgICAgIG1heC13aWR0aDogNTAwcHg7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG5cbiAgICAuZGlhbG9nLXRpdGxlIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxMnB4O1xuICAgICAgY29sb3I6ICNmNTdjMDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cblxuICAgIC53YXJuaW5nLWljb24ge1xuICAgICAgY29sb3I6ICNmZjk4MDA7XG4gICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICB3aWR0aDogMjRweDtcbiAgICAgIGhlaWdodDogMjRweDtcbiAgICB9XG5cbiAgICAuZGlhbG9nLWNvbnRlbnQge1xuICAgICAgcGFkZGluZzogMjBweCAwO1xuICAgIH1cblxuICAgIC5zZWN1cml0eS13YXJuaW5nIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgIGdhcDogMTZweDtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmYzZTA7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZjYzAyO1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gICAgfVxuXG4gICAgLnNlY3VyaXR5LWljb24ge1xuICAgICAgY29sb3I6ICNmZjk4MDA7XG4gICAgICBmb250LXNpemU6IDMycHg7XG4gICAgICB3aWR0aDogMzJweDtcbiAgICAgIGhlaWdodDogMzJweDtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgIH1cblxuICAgIC53YXJuaW5nLXRleHQgaDMge1xuICAgICAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gICAgICBjb2xvcjogI2Y1N2MwMDtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICB9XG5cbiAgICAud2FybmluZy10ZXh0IHAge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgY29sb3I6ICNlNjUxMDA7XG4gICAgfVxuXG4gICAgLnN0YXR1cy1pbmZvIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxMnB4O1xuICAgICAgYmFja2dyb3VuZDogI2UzZjJmZDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMTk2ZjM7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICB9XG5cbiAgICAuaW5mby1pY29uIHtcbiAgICAgIGNvbG9yOiAjMjE5NmYzO1xuICAgIH1cblxuICAgIC5mdWxsLXdpZHRoIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICB9XG5cbiAgICAuYWx0ZXJuYXRpdmVzLXNlY3Rpb24sIC5wcm9jZXNzLWluZm8ge1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICBtYXJnaW46IDE2cHggMDtcbiAgICB9XG5cbiAgICAuYWx0ZXJuYXRpdmVzLXNlY3Rpb24gaDQsIC5wcm9jZXNzLWluZm8gaDQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDhweDtcbiAgICAgIG1hcmdpbjogMCAwIDEycHggMDtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgIH1cblxuICAgIC5hbHRlcm5hdGl2ZXMtc2VjdGlvbiB1bCB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7XG4gICAgfVxuXG4gICAgLmFsdGVybmF0aXZlcy1zZWN0aW9uIGxpIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgIGNvbG9yOiAjNDI0MjQyO1xuICAgIH1cblxuICAgIC5wcm9jZXNzLWluZm8gb2wge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgcGFkZGluZy1sZWZ0OiAyMHB4O1xuICAgIH1cblxuICAgIC5wcm9jZXNzLWluZm8gbGkge1xuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgICAgY29sb3I6ICM0MjQyNDI7XG4gICAgfVxuXG4gICAgLmRpYWxvZy1hY3Rpb25zIHtcbiAgICAgIHBhZGRpbmc6IDE2cHggMCAwIDA7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xuICAgIH1cblxuICAgIC5kaWFsb2ctYWN0aW9ucyBidXR0b24ge1xuICAgICAgbWFyZ2luLWxlZnQ6IDhweDtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "MAT_DIALOG_DATA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "Disable2FADialogComponent", "constructor", "formBuilder", "twoFactorService", "snackBar", "dialogRef", "data", "loading", "disableForm", "group", "email", "value", "disabled", "required", "reason", "allCodesUsed", "onSubmit", "invalid", "formValue", "getRawValue", "requestDisable2FA", "subscribe", "next", "response", "open", "duration", "panelClass", "close", "success", "error", "console", "message", "onCancel", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "TwoFactorService", "i3", "MatSnackBar", "i4", "MatDialogRef", "_2", "selectors", "decls", "vars", "consts", "template", "Disable2FADialogComponent_Template", "rf", "ctx", "ɵɵtemplate", "Disable2FADialogComponent_div_14_Template", "ɵɵlistener", "Disable2FADialogComponent_Template_form_ngSubmit_15_listener", "ɵɵelement", "Disable2FADialogComponent_Template_button_click_59_listener", "Disable2FADialogComponent_Template_button_click_61_listener", "Disable2FADialogComponent_mat_icon_62_Template", "Disable2FADialogComponent_mat_icon_63_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\disable-2fa-dialog\\disable-2fa-dialog.component.ts"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { TwoFactorService } from '../../../services/two-factor.service';\r\n\r\nexport interface DisableDialogData {\r\n  email: string;\r\n  allCodesUsed?: boolean;\r\n  source: 'recovery' | 'login' | 'profile';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-disable-2fa-dialog',\r\n  template: `\r\n    <div class=\"disable-2fa-dialog\">\r\n      <h2 mat-dialog-title class=\"dialog-title\">\r\n        <mat-icon class=\"warning-icon\">warning</mat-icon>\r\n        Disable Two-Factor Authentication\r\n      </h2>\r\n\r\n      <mat-dialog-content class=\"dialog-content\">\r\n        <!-- Security Warning -->\r\n        <div class=\"security-warning\">\r\n          <mat-icon class=\"security-icon\">security</mat-icon>\r\n          <div class=\"warning-text\">\r\n            <h3>Security Warning</h3>\r\n            <p>Disabling 2FA will make your account less secure. Are you sure you want to proceed?</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Current Status -->\r\n        <div class=\"status-info\" *ngIf=\"data.allCodesUsed\">\r\n          <mat-icon class=\"info-icon\">info</mat-icon>\r\n          <p>All your recovery codes have been used. You can request to disable 2FA via email verification.</p>\r\n        </div>\r\n\r\n        <!-- Form -->\r\n        <form [formGroup]=\"disableForm\" (ngSubmit)=\"onSubmit()\">\r\n          <!-- Email (readonly) -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Email Address</mat-label>\r\n            <input matInput formControlName=\"email\" readonly>\r\n            <mat-icon matSuffix>email</mat-icon>\r\n          </mat-form-field>\r\n\r\n          <!-- Reason Selection -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Reason for Disabling</mat-label>\r\n            <mat-select formControlName=\"reason\" required>\r\n              <mat-option value=\"recovery_codes_exhausted\">All recovery codes used</mat-option>\r\n              <mat-option value=\"lost_device\">Lost access to authenticator device</mat-option>\r\n              <mat-option value=\"other\">Other reason</mat-option>\r\n            </mat-select>\r\n            <mat-icon matSuffix>help_outline</mat-icon>\r\n          </mat-form-field>\r\n\r\n          <!-- Alternative Options -->\r\n          <div class=\"alternatives-section\">\r\n            <h4><mat-icon>lightbulb</mat-icon> Consider These Alternatives:</h4>\r\n            <ul>\r\n              <li>Generate new recovery codes if you still have access to your authenticator</li>\r\n              <li>Set up a new authenticator app on a different device</li>\r\n              <li>Use email-based 2FA verification instead</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- Process Info -->\r\n          <div class=\"process-info\">\r\n            <h4><mat-icon>email</mat-icon> Email Verification Process:</h4>\r\n            <ol>\r\n              <li>We'll send a secure link to your email address</li>\r\n              <li>Click the link to confirm disabling 2FA</li>\r\n              <li>The link expires in 1 hour for security</li>\r\n            </ol>\r\n          </div>\r\n        </form>\r\n      </mat-dialog-content>\r\n\r\n      <mat-dialog-actions class=\"dialog-actions\">\r\n        <button mat-button (click)=\"onCancel()\" [disabled]=\"loading\">\r\n          Cancel\r\n        </button>\r\n        <button \r\n          mat-raised-button \r\n          color=\"warn\" \r\n          (click)=\"onSubmit()\" \r\n          [disabled]=\"disableForm.invalid || loading\">\r\n          <mat-icon *ngIf=\"loading\">hourglass_empty</mat-icon>\r\n          <mat-icon *ngIf=\"!loading\">email</mat-icon>\r\n          {{ loading ? 'Sending...' : 'Send Disable Email' }}\r\n        </button>\r\n      </mat-dialog-actions>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .disable-2fa-dialog {\r\n      max-width: 500px;\r\n      width: 100%;\r\n    }\r\n\r\n    .dialog-title {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n      color: #f57c00;\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    .warning-icon {\r\n      color: #ff9800;\r\n      font-size: 24px;\r\n      width: 24px;\r\n      height: 24px;\r\n    }\r\n\r\n    .dialog-content {\r\n      padding: 20px 0;\r\n    }\r\n\r\n    .security-warning {\r\n      display: flex;\r\n      align-items: flex-start;\r\n      gap: 16px;\r\n      background: #fff3e0;\r\n      border: 1px solid #ffcc02;\r\n      border-radius: 8px;\r\n      padding: 16px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .security-icon {\r\n      color: #ff9800;\r\n      font-size: 32px;\r\n      width: 32px;\r\n      height: 32px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .warning-text h3 {\r\n      margin: 0 0 8px 0;\r\n      color: #f57c00;\r\n      font-size: 16px;\r\n    }\r\n\r\n    .warning-text p {\r\n      margin: 0;\r\n      color: #e65100;\r\n    }\r\n\r\n    .status-info {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n      background: #e3f2fd;\r\n      border: 1px solid #2196f3;\r\n      border-radius: 8px;\r\n      padding: 12px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .info-icon {\r\n      color: #2196f3;\r\n    }\r\n\r\n    .full-width {\r\n      width: 100%;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .alternatives-section, .process-info {\r\n      background: #f8f9fa;\r\n      border-radius: 8px;\r\n      padding: 16px;\r\n      margin: 16px 0;\r\n    }\r\n\r\n    .alternatives-section h4, .process-info h4 {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin: 0 0 12px 0;\r\n      color: #1976d2;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .alternatives-section ul {\r\n      margin: 0;\r\n      padding-left: 20px;\r\n    }\r\n\r\n    .alternatives-section li {\r\n      margin-bottom: 8px;\r\n      color: #424242;\r\n    }\r\n\r\n    .process-info ol {\r\n      margin: 0;\r\n      padding-left: 20px;\r\n    }\r\n\r\n    .process-info li {\r\n      margin-bottom: 8px;\r\n      color: #424242;\r\n    }\r\n\r\n    .dialog-actions {\r\n      padding: 16px 0 0 0;\r\n      justify-content: flex-end;\r\n    }\r\n\r\n    .dialog-actions button {\r\n      margin-left: 8px;\r\n    }\r\n  `]\r\n})\r\nexport class Disable2FADialogComponent {\r\n  disableForm: FormGroup;\r\n  loading = false;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private twoFactorService: TwoFactorService,\r\n    private snackBar: MatSnackBar,\r\n    public dialogRef: MatDialogRef<Disable2FADialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DisableDialogData\r\n  ) {\r\n    this.disableForm = this.formBuilder.group({\r\n      email: [{ value: data.email, disabled: true }, [Validators.required, Validators.email]],\r\n      reason: [\r\n        data.allCodesUsed ? 'recovery_codes_exhausted' : 'lost_device', \r\n        [Validators.required]\r\n      ]\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.disableForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const formValue = this.disableForm.getRawValue();\r\n\r\n    this.twoFactorService.requestDisable2FA(formValue.email, formValue.reason).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open(\r\n          'Disable confirmation email sent! Check your inbox and click the link to confirm.',\r\n          'Close',\r\n          { duration: 8000, panelClass: ['success-snackbar'] }\r\n        );\r\n        this.dialogRef.close({ success: true, response });\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to request 2FA disable:', error);\r\n        this.snackBar.open(\r\n          error.error?.message || 'Failed to send disable email. Please try again.',\r\n          'Close',\r\n          { duration: 5000, panelClass: ['error-snackbar'] }\r\n        );\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.dialogRef.close({ success: false });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAAuBC,eAAe,QAAQ,0BAA0B;;;;;;;;IA+B9DC,EADF,CAAAC,cAAA,cAAmD,mBACrB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qGAA8F;IACnGF,EADmG,CAAAG,YAAA,EAAI,EACjG;;;;;IAqDJH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACpDH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;AA+HrD,OAAM,MAAOC,yBAAyB;EAIpCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,QAAqB,EACtBC,SAAkD,EACzBC,IAAuB;IAJ/C,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAPtC,KAAAC,OAAO,GAAG,KAAK;IASb,IAAI,CAACC,WAAW,GAAG,IAAI,CAACN,WAAW,CAACO,KAAK,CAAC;MACxCC,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAEL,IAAI,CAACI,KAAK;QAAEE,QAAQ,EAAE;MAAI,CAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACgB,KAAK,CAAC,CAAC;MACvFI,MAAM,EAAE,CACNR,IAAI,CAACS,YAAY,GAAG,0BAA0B,GAAG,aAAa,EAC9D,CAACrB,UAAU,CAACmB,QAAQ,CAAC;KAExB,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACR,WAAW,CAACS,OAAO,EAAE;MAC5B;IACF;IAEA,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,MAAMW,SAAS,GAAG,IAAI,CAACV,WAAW,CAACW,WAAW,EAAE;IAEhD,IAAI,CAAChB,gBAAgB,CAACiB,iBAAiB,CAACF,SAAS,CAACR,KAAK,EAAEQ,SAAS,CAACJ,MAAM,CAAC,CAACO,SAAS,CAAC;MACnFC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAChB,kFAAkF,EAClF,OAAO,EACP;UAAEC,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,kBAAkB;QAAC,CAAE,CACrD;QACD,IAAI,CAACrB,SAAS,CAACsB,KAAK,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAEL;QAAQ,CAAE,CAAC;QACjD,IAAI,CAAChB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACzB,QAAQ,CAACoB,IAAI,CAChBK,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAI,iDAAiD,EACzE,OAAO,EACP;UAAEN,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,gBAAgB;QAAC,CAAE,CACnD;QACD,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAyB,QAAQA,CAAA;IACN,IAAI,CAAC3B,SAAS,CAACsB,KAAK,CAAC;MAAEC,OAAO,EAAE;IAAK,CAAE,CAAC;EAC1C;EAAC,QAAAK,CAAA,G;qCApDUjC,yBAAyB,EAAAJ,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1C,EAAA,CAAAsC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAsC,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA9C,EAAA,CAAAsC,iBAAA,CAS1BvC,eAAe;EAAA;EAAA,QAAAgD,EAAA,G;UATd3C,yBAAyB;IAAA4C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAvM9BtD,EAFJ,CAAAC,cAAA,aAAgC,YACY,kBACT;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACjDH,EAAA,CAAAE,MAAA,0CACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAKDH,EAHJ,CAAAC,cAAA,4BAA2C,aAEX,kBACI;QAAAD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEjDH,EADF,CAAAC,cAAA,aAA0B,UACpB;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACzBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,2FAAmF;QAE1FF,EAF0F,CAAAG,YAAA,EAAI,EACtF,EACF;QAGNH,EAAA,CAAAwD,UAAA,KAAAC,yCAAA,iBAAmD;QAMnDzD,EAAA,CAAAC,cAAA,eAAwD;QAAxBD,EAAA,CAAA0D,UAAA,sBAAAC,6DAAA;UAAA,OAAYJ,GAAA,CAAAnC,QAAA,EAAU;QAAA,EAAC;QAGnDpB,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAA4D,SAAA,iBAAiD;QACjD5D,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAC3BF,EAD2B,CAAAG,YAAA,EAAW,EACrB;QAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;QAAAD,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAEzCH,EADF,CAAAC,cAAA,sBAA8C,sBACC;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACjFH,EAAA,CAAAC,cAAA,sBAAgC;QAAAD,EAAA,CAAAE,MAAA,2CAAmC;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChFH,EAAA,CAAAC,cAAA,sBAA0B;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QACxCF,EADwC,CAAAG,YAAA,EAAa,EACxC;QACbH,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAClCF,EADkC,CAAAG,YAAA,EAAW,EAC5B;QAIXH,EADN,CAAAC,cAAA,eAAkC,UAC5B,gBAAU;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,qCAA4B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAElEH,EADF,CAAAC,cAAA,UAAI,UACE;QAAAD,EAAA,CAAAE,MAAA,kFAA0E;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnFH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,4DAAoD;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7DH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,gDAAwC;QAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACD;QAIAH,EADN,CAAAC,cAAA,eAA0B,UACpB,gBAAU;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,oCAA2B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE7DH,EADF,CAAAC,cAAA,UAAI,UACE;QAAAD,EAAA,CAAAE,MAAA,sDAA8C;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvDH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,+CAAuC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChDH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,+CAAuC;QAInDF,EAJmD,CAAAG,YAAA,EAAK,EAC7C,EACD,EACD,EACY;QAGnBH,EADF,CAAAC,cAAA,8BAA2C,kBACoB;QAA1CD,EAAA,CAAA0D,UAAA,mBAAAG,4DAAA;UAAA,OAASN,GAAA,CAAAnB,QAAA,EAAU;QAAA,EAAC;QACrCpC,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAI8C;QAD5CD,EAAA,CAAA0D,UAAA,mBAAAI,4DAAA;UAAA,OAASP,GAAA,CAAAnC,QAAA,EAAU;QAAA,EAAC;QAGpBpB,EADA,CAAAwD,UAAA,KAAAO,8CAAA,uBAA0B,KAAAC,8CAAA,uBACC;QAC3BhE,EAAA,CAAAE,MAAA,IACF;QAEJF,EAFI,CAAAG,YAAA,EAAS,EACU,EACjB;;;QA7DwBH,EAAA,CAAAiE,SAAA,IAAuB;QAAvBjE,EAAA,CAAAkE,UAAA,SAAAX,GAAA,CAAA7C,IAAA,CAAAS,YAAA,CAAuB;QAM3CnB,EAAA,CAAAiE,SAAA,EAAyB;QAAzBjE,EAAA,CAAAkE,UAAA,cAAAX,GAAA,CAAA3C,WAAA,CAAyB;QA0CSZ,EAAA,CAAAiE,SAAA,IAAoB;QAApBjE,EAAA,CAAAkE,UAAA,aAAAX,GAAA,CAAA5C,OAAA,CAAoB;QAO1DX,EAAA,CAAAiE,SAAA,GAA2C;QAA3CjE,EAAA,CAAAkE,UAAA,aAAAX,GAAA,CAAA3C,WAAA,CAAAS,OAAA,IAAAkC,GAAA,CAAA5C,OAAA,CAA2C;QAChCX,EAAA,CAAAiE,SAAA,EAAa;QAAbjE,EAAA,CAAAkE,UAAA,SAAAX,GAAA,CAAA5C,OAAA,CAAa;QACbX,EAAA,CAAAiE,SAAA,EAAc;QAAdjE,EAAA,CAAAkE,UAAA,UAAAX,GAAA,CAAA5C,OAAA,CAAc;QACzBX,EAAA,CAAAiE,SAAA,EACF;QADEjE,EAAA,CAAAmE,kBAAA,MAAAZ,GAAA,CAAA5C,OAAA,4CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}