=== Page 467 ===

467Some headers used to have to be prefixed as HTTP_ to be used correctly. This is no
longer required. Code where you have done this:
Can be changed to:
Status codes used to be set on the request class but is now set on the response class.
The method and uses are the same:
Should be changed to:from masonite.response import Response
class CustomMiddleware(Middleware):
  def __init__(self, response: Response)
      self.response = response
  # ...
  def after(self):
  self.response.header('IS_INERTIA', 'true')
request.header('HTTP_IS_INERTIA', 'true')
response.header('IS_INERTIA', 'true')
request.status(200)
response.status(200)HTTP Header Prefix.
Status Codes
New Providers6/12/25, 3:02 AM Masonite Documentation