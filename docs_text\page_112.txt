=== Page 113 ===

113A handler is a simple class with a handle() method that Masonite will call when a
specific exception is thrown by the application.
As an example, you could handle specifically ZeroDivisionError exceptions that could
be thrown by your application. It will look like this:
The name of the class can be whatever you like. In the handle method you should
manually return a response by using the response class.
You will then need to register the class to the container using a specific key binding. The
key binding will be {exception_name}Handler. You can do this in your Kernel file.
To register a custom exception handler for our ZeroDivisionError we would create a
binding that looks like this:
You can add this binding in your AppProvider or in Kernel.py.class DivideException:
    def __init__(self, application)
        self.application = application
    def handle(self, exception):
        self.application.make('response').view({
            "error": str(exception),
            "message": "You cannot divide by zero"
        }, status=500)
from app.exceptions.DivideException import DivideException
self.application.bind(
    "ZeroDivisionErrorHandler",
    DivideException(self.application)
)
Adding New Handlers6/12/25, 3:02 AM Masonite Documentation