=== Page 254 ===

254Y<PERSON> can then fill the list with rules:
You can then use the rule enclosure like this:from masonite.validation import RuleEnclosure
...
class AcceptedTerms(RuleEnclosure):
    def rules(self):
        """ ... """
        return [
            # Rules go here
        ]
from masonite.validation import required, accepted
class LoginForm(RuleEnclosure):
    def rules(self):
        """ ... """
        return [
            required(['email', 'terms']),
            accepted('terms')
        ]Creating the Rule Enclosure6/12/25, 3:02 AM Masonite Documentation