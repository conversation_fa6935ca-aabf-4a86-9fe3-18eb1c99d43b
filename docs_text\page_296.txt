=== Page 297 ===

297This then calls the same attribute but anytime the Request object itself is made from
the container. Notice everything is the same except line 6 where we are using an object
instead of a string.
We can do the same thing with the other options:from masonite.request import Request
# ...
# sets the hook
container.on_make(Request, attribute_on_make)
container.bind('request', Request)
# runs the attribute_on_make function
request = container.make('request')
request.attribute # 'some value'
container.on_bind(Request, attribute_on_make)
container.on_make(Request, attribute_on_make)
container.on_resolve(Request, attribute_on_make)6/12/25, 3:02 AM Masonite Documentation