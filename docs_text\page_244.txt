=== Page 245 ===

245If you want to handle errors in views specifically you will need to add the 
ShareErrorsInSessionMiddleware middleware into your route middlewares. errors
will be injected to views as a MessageBag instance allowing to handle errors easily:@if session().has('errors'):
<div class="bg-yellow-400">
  <div class="bg-yellow-200 text-yellow-800 px-4 py-2">
    <ul>
      @for key, error_list in session().get('errors').items():
        @for error in error_list
          <li>{{ error }}</li>
        @endfor
      @endfor
    </ul>
  </div>
</div>
@endif6/12/25, 3:02 AM Masonite Documentation