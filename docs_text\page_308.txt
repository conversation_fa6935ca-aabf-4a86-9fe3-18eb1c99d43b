=== Page 309 ===

309When you run a test class each test method of this test class will be ran following a
specific life cycle.
Running the above test class will create this output:class TestFeatures(TestCase):
    @classmethod
    def setUpClass(cls):
        """Called once before all tests of this class are executed."""
        print("Setting up test class")
    @classmethod
    def tearDownClass(cls):
        """Called once after all tests of this class are executed."""
        print("Cleaning up test class")
    def setUp(self):
        """Called once before each test are executed."""
        super().setUp()
        print("Setting up individual unit test")
    def tearDown(self):
        """Called once after each test are executed."""
        super().tearDown()
        print("Cleaning up individual unit test")
    def test_1(self):
        print("Running test 1")
    def test_2(self):
        print("Running test 2")
Setting up test class
Setting up individual unit test
Running test 2
Cleaning up individual unit test
Setting up individual unit test
Running test 1
Cleaning up individual unit test
Cleaning up test classTest Life Cycle6/12/25, 3:02 AM Masonite Documentation