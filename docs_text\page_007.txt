=== Page 8 ===

8In this tutorial we will walk through how to create a blog. We will touch on all the major
systems of Masonite and it should give you the confidence to try the more advanced
tutorials or build an application yourself.
Typically your first starting point for your Masonite development flow will be to create a
route. All routes are located in routes/web.py and are extremely simple to understand.
They consist of a request method and a route method. Routing is simply stating what
incoming URI's should direct to which controllers.
For example, to create a GET request route it will look like:
We'll talk more about the controller in a little bit.
You can read more about routes in the Routing documentation
We will start off by creating a view and controller to create a blog post.
A controller is a simply a class that inherits from Masonite's Controller class and contains
controller methods. These controller methods will be what our routes will call so they will
contain most of our application's business logic.
Think of a controller method as a function in the views.py file if you are coming from theroutes/web.py
from masonite.routes import Route
ROUTES = [
    Route.get('/url', 'Controller@method')
]
Routing
Creating our Route:6/12/25, 3:02 AM Masonite Documentation