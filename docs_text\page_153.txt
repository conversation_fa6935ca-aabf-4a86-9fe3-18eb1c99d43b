=== Page 154 ===

154Laravel is using Laravel mix so you can just follow guidelines to setup TailwindCSS for
Laravel Mix on TailwindCSS Official Documentation.
Please follow the guidelines directly on Laravel Mix Vue Support Documentation
Now that we have our compiled assets configured we can now actually compile them.
You can do so by running:
This will compile the assets and put them in the directories you put in the configuration
file.
You can also have NPM wait for changes and recompile when changes are detected in
frontend files. This is similiar to an auto reloading server. To do this just run:
Laravel Mix can take care of file hashing when releasing assets to production, by adding a
hash suffix to your assets to automatically bust cache when loading assets. To enable
this you can add the following in your webpack.mix.js file:
More information on this feature in Laravel Mix Versioning.
$ npm run dev
$ npm run watch
if (mix.inProduction()) {
  mix.version();
}
Installing Vue
Compiling
Versioning6/12/25, 3:02 AM Masonite Documentation