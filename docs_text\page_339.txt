=== Page 340 ===

340Mocking
When it comes to unit testing, you always want to test a unit of your piece of code. Your
code might depends on third party services such as an API and you don't want to call it
during your local tests or in your CI environment. That's when you should use mocking to
mock the external parts or the part you don't want to test.
Masonite comes with some mocking abilities for some of the features relying on third
party services. For other parts or you own code you can use Python mocking abilities
provided by unittest.mock standard module.
Masonite tests case have two helpers method fake() and restore().
You can mock a Masonite feature by doing self.fake(feature) and then restore it to
the real feature behaviour by calling self.restore(feature). When a feature is
mocked the real behaviour won't be called, instead a quick and simple implementation is
ran, often offering the ability to inspect and test what happens.
Available features that can be mocked (for now) are:
•Mail
•Notification
When mocking emails it will prevent emails from being really sent. Typically, sending mail
is unrelated to the code you are actually testing. Most likely, it is sufficient to simply
assert that <PERSON><PERSON> was instructed to send a given mailable.
Here is an example of how to mock emails sending in your tests:Masonite Features Mocks
Mocking Mail6/12/25, 3:02 AM Masonite Documentation