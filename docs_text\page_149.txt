=== Page 150 ===

150If age key exists in the cache AND it is not expired, then "21" will be added to the cache
and returned. If the age key does not exist or is not expired then it will return whatever
data is in the cache for that key.
The put method will put data into the cache regardless of if it exists already. This is a
good way to overwrite data in the cache:
You can specify the number of seconds that the cache should be valid for. Do not specify
any time or specify None to keep the data forever.
You may also cache lists and dictionaries which will preserve the data types:
You can get cache data from the cache. If the data is expired then this will either return 
None or the default you specify:
This will either fetch the correct age data from the cache or return the default value of 
40.from masonite.cache import Cache
def store(self, cache: Cache):
    data = cache.add('age', '21')
cache.put('age', '21')
cache.put('age', '21', seconds=300) # stored for 5 minutes
cache.put('user', {"name": "<PERSON>"}, seconds=300) # stored for 5 minutes
cache.get("user")['name'] #== "Joe"
cache.get('age', '40')put
Getting Data6/12/25, 3:02 AM Masonite Documentation