=== Page 395 ===

395resolved. These objects do not have to be in the container in the first place.
You can now use a env function to automatically type cast your environment variables
turning a numeric into an int:
You can now resolve from a container with a parameter list in addition to custom
parameters.
In addition to all the awesome things that craft auth generates, we now generate
password reset views and controllers as well for you
Fixed an issue where custom route compilers was not working well with request
parametersfrom masonite import env
env('DB_PORT', '5432') #== 5432 (int)Added a new env function
Added ability to resolve with paramaters at the
same time
Added password reset to auth command
Route Compiler
Added Database Seeders6/12/25, 3:02 AM Masonite Documentation