=== Page 209 ===

209The default package layout come with one basic test. You should see this test passing.
You can then start building your package and adding more unit tests.
The default package layout comes with a test project located in tests/integrations.
This project is really useful to directly test your package behaviour. It is scaffolded as a
default Masonite project with your package already installed (see Installing a Package
section). You can run the project with the usual command:
You can then visit http://localhost:8000 to see the welcome page.
When you make changes to your package as your package is installed locally and registered
into your project, your changes will be directly available in the project. You will just need to
refresh your pages to see changes.
If your package has migrations and you want to migrate your test project you should first 
register your migrations, publish them and then run the usual migrate command:
Once you're satisfied with your package it's time to release it on PyPi so that everyone
can install it. We made it really easy to do this with the make commands.
Registering on PyPi$ python -m pytest
$ python craft serve
$ masonite-orm migrate -d tests/integrations/databases/migrations
Running the test project
Migrating the test project
Releasing the package6/12/25, 3:02 AM Masonite Documentation