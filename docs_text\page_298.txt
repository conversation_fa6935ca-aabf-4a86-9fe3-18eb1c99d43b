=== Page 299 ===

299CORS
CORS is sometimes important to activate in your application. CORS allows you to have a
stronger layer of security over your application by specifying specific CORS headers in
your responses. This is done through "preflight" requests.
These "preflight" requests are OPTIONS requests that are sent at the same time as other
non safe requests (POST, PUT, DELETE) and specifically designed to verify the CORS
headers before allowing the other request to go through.
To learn more about CORS please read MDN documentation.
You can enable CORS protection by simply adding the CorsMiddleware into you
middleware stack.
To enable CORS in Masonite, you just need to add the CorsMiddleware in your
middleware stack to your http_middleware key in your Kernel.py class.
It is best to put the CORS middleware as the first middleware to prevent possible errors.
Your application will now handle CORS requests to successfully go through.
from masonite.middleware import CorsMiddleware
#..
class Kernel:
    http_middleware = [
        CorsMiddleware,
        EncryptCookies
    ]
    #..Getting Started6/12/25, 3:02 AM Masonite Documentation