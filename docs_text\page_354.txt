=== Page 355 ===

355Handling AJAX requests
with expired authentication
When an ajax request is made from a page that requires a valid user (in the session), the
page does not redirect (to a Login page) when the auth session has expired. This makes
the page look unresponsive and provides a less than ideal user experience.
The solution is quite simple though not that obvious. It has 2 parts:
•The middleware will recognise the expired auth and that the request is ajax, then send
a custom Response the page will recognise.
•The Javascript on the page will check the ajax response before it gets processes by
other event handlres. If the response matches the criteria the browser redirects to the
login page.
In your AuthenticationMiddleware
do something similar to the followingThe Problem:
The Solution:
The Code:
Auth Middleware6/12/25, 3:02 AM Masonite Documentation