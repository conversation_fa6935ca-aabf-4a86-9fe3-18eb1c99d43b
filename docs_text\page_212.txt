=== Page 213 ===

213Your package is likely to have a configuration file. You will want to make your package
configuration available through the handy config() Masonite helper. For this you will
need to call config(path, publish=False) inside configure() method:
This will load the package configuration file located at 
super_awesome_package/config/super_awesome.py into Masonite config. The
configuration will then be available with config("super_awesome.key").
If you want to allow users to publish the configuration file into their own project you
should add publish=True argument.
The package publish command will publish the configuration into the defined project
configuration folder. With the default project settings it would be in 
config/super_awesome.py.
Configuration values located in packages and in local project will be merged. Values defined
locally in the project takes precedance over the default values of the package.def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .config("config/super_awesome.py")
    )
def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .config("config/super_awesome.py", publish=True)
    )
Configuration
Migrations6/12/25, 3:02 AM Masonite Documentation