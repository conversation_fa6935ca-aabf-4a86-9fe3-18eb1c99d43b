=== Page 16 ===

16This should be fairly straight forward but if you want to learn more, be sure to read the 
Database Migrations documentation.
Now we can migrate this migration to create the posts table
Now that we have our tables and migrations all done and we have a posts table, let's
create a model for it.
Models in Masonite are a bit different than other Python frameworks. Masonite uses an
Active Record ORM. Models and migrations are separate in Masonite. Our models will
take shape of our tables regardless of what the table looks like.
Again, we can use a craft command to create our model:def up(self):
    """
    Run the migrations.
    """
    with self.schema.create('posts') as table:
        table.increments('id')
        table.string('title')
        table.integer('author_id').unsigned()
        table.foreign('author_id').references('id').on('users')
        table.string('body')
        table.timestamps()
terminal
$ python craft migrate
Models
Creating our Model6/12/25, 3:02 AM Masonite Documentation