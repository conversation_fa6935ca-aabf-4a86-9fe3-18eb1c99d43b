import { PaymentRepository } from '../repositories';
import { Payment } from '../models';
export declare class PaymentService {
    paymentRepository: PaymentRepository;
    private razorpay;
    constructor(paymentRepository: PaymentRepository);
    createOrder(amount: number, currency: string, userId: string, description?: string): Promise<{
        orderId: string;
        amount: number;
        currency: string;
    }>;
    verifyPayment(orderId: string, paymentId: string, signature: string): Promise<boolean>;
    getPaymentStatus(orderId: string): Promise<Payment | null>;
    getUserPayments(userId: string): Promise<Payment[]>;
    refundPayment(paymentId: string, amount?: number): Promise<boolean>;
    handleWebhook(payload: any, signature: string): Promise<void>;
    private handlePaymentCaptured;
    private handlePaymentFailed;
}
