=== Page 172 ===

172The local driver is used for local filesystems like server directories. All files are stored and
managed "locally" on the server.
The S3 driver is used for connecting to Amazon's S3 cloud service.
Uploading files is simple. You will have to use the Masonite Storage class.
The first and most simplest method is taking a file and putting text into it. For this we can
use the put methodOption Description
driver The driver to use for this disk
path The base path to use for this disk
Option Description
driver The driver to use for this disk
client The Amazon S3 client key
secret The Amazon S3 secret key
bucket The Amazon S3 bucket name
path A path to be used for displaying resourcesLocal Driver
S3 Driver
Uploading Files6/12/25, 3:02 AM Masonite Documentation