=== Page 426 ===

426As well as add the SessionProvider inside your PROVIDERS list just below the 
AppProvider:
That's it! You have officially upgrades to Masonite 1.5''' Session Settings '''
'''
|----------------------------------------------------------------------
----
| Session Driver
|----------------------------------------------------------------------
----
|
| Sessions are able to be linked to an individual user and carry data 
from
| request to request. The memory driver will store all the session data
| inside memory which will delete when the server stops running.
|
| Supported: 'memory', 'cookie'
| 
'''
DRIVER = 'memory'
PROVIDERS = [
    # Framework Providers
    'masonite.providers.AppProvider.AppProvider',
    # New Provider
    'masonite.providers.SessionProvider.SessionProvider',
    'masonite.providers.RouteProvider.RouteProvider',
    ....
]
Finished6/12/25, 3:02 AM Masonite Documentation