=== Page 392 ===

392We now have View Routes on all instances of the normal HTTP classes:
We previously used this method on the Cache class like so:
Now we removed the cache_ prefix and it is just:
We can now use the .without() method on the request class which returns all inputs
except the ones specified:Get().view('/url', 'some/template', {'key': 'value'})
def show(self, Cache):
    Cache.cache_exists('key')
from masonite import C<PERSON>
def show(self, cache: Cache):
    cache.exists('key')
def show(self, request: Request):
    request.without('key1', 'key2')Added View Routes
Renamed cache_exists to exists
Added without method to request class
Added port to database6/12/25, 3:02 AM Masonite Documentation