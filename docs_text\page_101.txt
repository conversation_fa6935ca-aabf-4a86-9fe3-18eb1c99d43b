=== Page 102 ===

102Y<PERSON> can use Python standard os.getenv() method to get an environment variable
value. It looks like:
Notice that this method does not cast types, so here we got a string instead of a boolean
value.
You can also use Masonite helper env to read an environment variable value. It looks
like:
Note that you can provide a default value if the environment variable is not defined.
Default value is "". For convenience this helper is casting types. Here are different
examples of variables type casting:import os
is_debug = os.getenv("APP_DEBUG") #== "True" (str)
from masonite.environment import env
is_debug = env("APP_DEBUG", False) #== True (bool)
Env Var Value Casts to (type)
5432 5432 (int)
true True (bool)
None (None)
"" "" (str)
True True (bool)
false False (bool)os.getenv()
env()6/12/25, 3:02 AM Masonite Documentation