=== Page 253 ===

253Just put the dictionary as the first argument and then each rule being its own argument.
Rule enclosures are self contained classes with rules. You can use these to help reuse
your validation logic. For example if you see you are using the same rules often you can
use an enclosure to always keep them together and reuse them throughout your code
base.
You can create a rule enclosure by running:
You will then see a file generated like this inside app/rules:from masonite.validation import Validator
def show(self, validator: Validator):
    """
    Incoming Input: {
        'user': 'username123',
        'company': 'Masonite'
    }
    """
    valid = validator.validate({
        'user': 'username123',
        'company': 'Masonite'
    },
        validate.required(['user', 'company']),
        validate.equals_masonite('company')
    )
$ python craft rule:enclosure AcceptedTermsRule Enclosures
Rule Enclosure Command6/12/25, 3:02 AM Masonite Documentation