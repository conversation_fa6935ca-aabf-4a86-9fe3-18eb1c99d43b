"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountDeletionService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const _1 = require("./");
const repositories_1 = require("../repositories");
const crypto_1 = require("crypto");
const util_1 = require("util");
let AccountDeletionService = class AccountDeletionService {
    constructor(userRepository, paymentRepository, deletionRepository, preservedDataRepository, securityService, emailService) {
        this.userRepository = userRepository;
        this.paymentRepository = paymentRepository;
        this.deletionRepository = deletionRepository;
        this.preservedDataRepository = preservedDataRepository;
        this.securityService = securityService;
        this.emailService = emailService;
    }
    /**
     * Request account deletion with preferences
     */
    async requestAccountDeletion(userId, preferences) {
        // Get user data
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        } // Generate deletion token for email confirmation
        const deletionToken = (0, crypto_1.randomBytes)(32).toString('hex');
        const tokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        // Calculate data expiry date
        const retentionPeriod = preferences.customRetentionPeriod || 30;
        const dataExpiryDate = new Date(Date.now() + retentionPeriod * 24 * 60 * 60 * 1000);
        // Check if deletion record already exists for this email
        const existingDeletion = await this.deletionRepository.findByEmail(user.email);
        if (existingDeletion) {
            if (existingDeletion.deletionStatus === 'pending_confirmation') {
                throw new rest_1.HttpErrors.BadRequest('Account deletion is already in progress');
            }
            // If user has preserved data or completed deletion, update the existing record
            console.log(`🔄 Updating existing deletion record for ${user.email}`);
            console.log(`📊 Previous status: ${existingDeletion.deletionStatus}`);
            // Update existing record with new preferences and reset to pending
            await this.deletionRepository.updateById(existingDeletion.id, {
                originalUserId: userId, // Update to new user ID
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                deletionStatus: 'pending_confirmation',
                deletionPreferences: preferences,
                deletionRequestedAt: new Date(),
                dataExpiryDate,
                deletionToken,
                deletionTokenExpires: tokenExpires,
                canRestore: true,
                restorationPeriodDays: retentionPeriod,
                updatedAt: new Date(),
            });
            const deletionRecord = await this.deletionRepository.findById(existingDeletion.id);
            // Send confirmation email
            await this.sendDeletionConfirmationEmail(user, deletionToken, preferences);
            return {
                message: `Account deletion requested. Please check your email (${user.email}) for confirmation instructions.`,
                deletionId: deletionRecord.id,
                confirmationRequired: true,
                confirmationToken: deletionToken,
            };
        }
        // Create new deletion record if none exists
        const deletionRecord = await this.deletionRepository.create({
            originalUserId: userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            deletionStatus: 'pending_confirmation',
            deletionPreferences: preferences,
            deletionRequestedAt: new Date(),
            dataExpiryDate,
            deletionToken,
            deletionTokenExpires: tokenExpires,
            canRestore: true,
            restorationPeriodDays: retentionPeriod,
        });
        // Send confirmation email
        await this.sendDeletionConfirmationEmail(user, deletionToken, preferences);
        return {
            message: `Account deletion requested. Please check your email (${user.email}) for confirmation instructions.`,
            deletionId: deletionRecord.id,
            confirmationRequired: true,
            confirmationToken: deletionToken,
        };
    }
    /**
     * Confirm account deletion with token
     */
    async confirmAccountDeletion(token) {
        // Find deletion record by token
        const deletionRecord = await this.deletionRepository.findOne({
            where: {
                deletionToken: token,
                deletionTokenExpires: { gt: new Date() },
                deletionStatus: 'pending_confirmation',
            },
        });
        if (!deletionRecord) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired deletion token');
        }
        // Get user
        const user = await this.userRepository.findById(deletionRecord.originalUserId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Preserve data based on preferences
        const preservedDataSummary = await this.preserveUserData(deletionRecord, user);
        // Delete the user account
        await this.performAccountDeletion(user, deletionRecord);
        // Update deletion record
        await this.deletionRepository.updateById(deletionRecord.id, {
            deletionStatus: deletionRecord.deletionPreferences.preservePaymentData ||
                deletionRecord.deletionPreferences.preserveTransactionHistory ||
                deletionRecord.deletionPreferences.preserveProfileData
                ? 'data_preserved' : 'hard_deleted',
            deletionCompletedAt: new Date(),
            preservedDataSummary,
            deletionToken: undefined,
            deletionTokenExpires: undefined,
        });
        // Send deletion confirmation email
        await this.sendDeletionCompletedEmail(user, preservedDataSummary);
        return {
            message: 'Account deletion completed successfully',
            deletionId: deletionRecord.id,
            preservedDataSummary,
        };
    }
    /**
     * Check if user has preserved data
     */
    async checkPreservedData(email) {
        console.log('🔍 [checkPreservedData] Looking for preserved data for email:', email);
        // Debug: First check if there's any deletion record at all
        const anyRecord = await this.deletionRepository.findByEmail(email);
        console.log('🔍 [checkPreservedData] Any deletion record found:', !!anyRecord);
        if (anyRecord) {
            console.log('🔍 [checkPreservedData] Record details:', {
                status: anyRecord.deletionStatus,
                canRestore: anyRecord.canRestore,
                dataExpiryDate: anyRecord.dataExpiryDate,
                now: new Date()
            });
        }
        const deletionRecord = await this.deletionRepository.findRestorableByEmail(email);
        console.log('🔍 [checkPreservedData] Restorable record found:', !!deletionRecord);
        if (!deletionRecord) {
            console.log('🔍 [checkPreservedData] No restorable data found for email:', email);
            return { hasPreservedData: false };
        }
        console.log('🔍 [checkPreservedData] Found preserved data:', {
            hasData: true,
            summary: deletionRecord.preservedDataSummary
        });
        return {
            hasPreservedData: true,
            deletionRecord,
            preservedDataSummary: deletionRecord.preservedDataSummary,
        };
    }
    /**
     * Restore user data during signup
     */
    async restoreUserData(newUserId, email, restoreOptions) {
        const deletionRecord = await this.deletionRepository.findRestorableByEmail(email);
        if (!deletionRecord) {
            throw new rest_1.HttpErrors.NotFound('No preserved data found for this email');
        }
        // Get preserved data
        const preservedData = await this.preservedDataRepository.findByDeletionRecordId(deletionRecord.id);
        const restoredData = {
            paymentData: 0,
            transactionHistory: 0,
            profileData: false,
            securityLogs: 0,
        };
        // Restore data based on options
        for (const data of preservedData) {
            if (data.dataType === 'payment_data' && restoreOptions.restorePaymentData) {
                await this.restorePaymentData(newUserId, data);
                restoredData.paymentData = Array.isArray(data.dataContent)
                    ? data.dataContent.length
                    : Object.keys(data.dataContent).length;
            }
            if (data.dataType === 'profile_backup' && restoreOptions.restoreProfileData) {
                await this.restoreProfileData(newUserId, data);
                restoredData.profileData = true;
            }
            // Add other restore logic as needed
        } // Mark deletion record as restoration completed
        await this.deletionRepository.updateById(deletionRecord.id, {
            canRestore: false,
            updatedAt: new Date(),
        });
        // Send data restoration confirmation email
        try {
            await this.sendDataRestorationEmail(email, restoredData);
        }
        catch (emailError) {
            console.error('⚠️ Failed to send data restoration email:', emailError);
            // Don't fail the restoration if email sending fails
        }
        console.log('✅ Data restoration completed for:', email);
        console.log('📦 Restored data summary:', restoredData);
        return {
            message: 'Data restoration completed successfully',
            restoredData,
        };
    }
    /**
     * Delete preserved data permanently
     */
    async deletePreservedData(email) {
        const deletionRecord = await this.deletionRepository.findRestorableByEmail(email);
        if (!deletionRecord) {
            throw new rest_1.HttpErrors.NotFound('No preserved data found for this email');
        }
        // Delete all preserved data
        const preservedData = await this.preservedDataRepository.findByDeletionRecordId(deletionRecord.id);
        for (const data of preservedData) {
            await this.preservedDataRepository.deleteById(data.id);
        }
        // Update deletion record
        await this.deletionRepository.updateById(deletionRecord.id, {
            deletionStatus: 'hard_deleted',
            canRestore: false,
            updatedAt: new Date(),
        });
        return {
            message: 'All preserved data has been permanently deleted',
        };
    }
    /**
     * Preserve user data based on preferences
     */
    async preserveUserData(deletionRecord, user) {
        const summary = {
            paymentRecords: 0,
            transactionHistory: 0,
            loginHistory: 0,
            securityEvents: 0,
            profileBackup: false,
        };
        const preferences = deletionRecord.deletionPreferences;
        const expiryDate = deletionRecord.dataExpiryDate;
        // Preserve payment data
        if (preferences.preservePaymentData) {
            const payments = await this.paymentRepository.find({
                where: { userId: user.id },
            });
            if (payments.length > 0) {
                const encryptedPayments = await this.encryptData(payments);
                await this.preservedDataRepository.create({
                    deletionRecordId: deletionRecord.id,
                    dataType: 'payment_data',
                    dataContent: encryptedPayments.data,
                    encryptionKeyHash: encryptedPayments.keyHash,
                    isEncrypted: true,
                    dataSizeBytes: JSON.stringify(payments).length,
                    expiresAt: expiryDate,
                });
                summary.paymentRecords = payments.length;
            }
        }
        // Preserve profile data
        if (preferences.preserveProfileData) {
            const profileData = {
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                createdAt: user.createdAt,
                lastLoginAt: user.lastLoginAt,
                // Add other profile fields as needed
            };
            await this.preservedDataRepository.create({
                deletionRecordId: deletionRecord.id,
                dataType: 'profile_backup',
                dataContent: profileData,
                isEncrypted: false,
                dataSizeBytes: JSON.stringify(profileData).length,
                expiresAt: expiryDate,
            });
            summary.profileBackup = true;
        }
        return summary;
    }
    /**
     * Perform actual account deletion
     */
    async performAccountDeletion(user, deletionRecord) {
        console.log('🗑️ Starting account deletion for user:', user.id);
        try {
            // Delete payment records if not preserving
            if (!deletionRecord.deletionPreferences.preservePaymentData) {
                console.log('🗑️ Deleting payment records...');
                const payments = await this.paymentRepository.find({
                    where: { userId: user.id },
                });
                for (const payment of payments) {
                    await this.paymentRepository.deleteById(payment.id);
                    console.log('🗑️ Deleted payment:', payment.id);
                }
            }
            // Delete user account - this is the critical step
            console.log('🗑️ Deleting user account:', user.id);
            await this.userRepository.deleteById(user.id);
            console.log('✅ User account deleted successfully:', user.id);
            // Verify the user was actually deleted
            try {
                const verifyUser = await this.userRepository.findById(user.id);
                if (verifyUser) {
                    throw new Error('User deletion failed - user still exists after deletion');
                }
            }
            catch (error) {
                if (error.statusCode === 404 || error.message.includes('Entity not found')) {
                    console.log('✅ Verified: User account successfully deleted');
                }
                else {
                    throw error;
                }
            }
        }
        catch (error) {
            console.error('❌ Account deletion failed:', error);
            throw new Error(`Account deletion failed: ${error.message}`);
        }
    }
    /**
     * Restore payment data
     */
    async restorePaymentData(newUserId, preservedData) {
        let paymentData;
        if (preservedData.isEncrypted) {
            paymentData = await this.decryptData(preservedData.dataContent, preservedData.encryptionKeyHash);
        }
        else {
            paymentData = preservedData.dataContent;
        }
        // Restore payment records with new user ID
        for (const payment of paymentData) {
            await this.paymentRepository.create({
                ...payment,
                id: undefined, // Generate new ID
                userId: newUserId,
            });
        }
    }
    /**
     * Restore profile data
     */
    async restoreProfileData(newUserId, preservedData) {
        const profileData = preservedData.dataContent;
        // Update user with preserved profile data
        await this.userRepository.updateById(newUserId, {
            phone: profileData.phone,
            // Add other restorable fields as needed
        });
    }
    /**
     * Encrypt sensitive data
     */
    async encryptData(data) {
        const scryptAsync = (0, util_1.promisify)(crypto_1.scrypt);
        const iv = (0, crypto_1.randomBytes)(16);
        const password = (0, crypto_1.randomBytes)(32).toString('hex');
        const key = (await scryptAsync(password, 'salt', 32));
        const cipher = (0, crypto_1.createCipheriv)('aes-256-cbc', key, iv);
        let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return {
            data: { encrypted, iv: iv.toString('hex') },
            keyHash: password,
        };
    }
    /**
     * Decrypt sensitive data
     */
    async decryptData(encryptedData, keyHash) {
        const scryptAsync = (0, util_1.promisify)(crypto_1.scrypt);
        const key = (await scryptAsync(keyHash, 'salt', 32));
        const iv = Buffer.from(encryptedData.iv, 'hex');
        const decipher = (0, crypto_1.createDecipheriv)('aes-256-cbc', key, iv);
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return JSON.parse(decrypted);
    }
    /**
     * Send custom email using the EmailService infrastructure
     */
    async sendCustomEmail(to, subject, htmlContent) {
        // Use the EmailService's private method infrastructure
        try {
            await this.emailService.sendEmailViaBrevo?.(to, subject, htmlContent) ||
                this.emailService.sendEmailViaFallback?.(to, subject, htmlContent);
        }
        catch (error) {
            console.error('Failed to send custom email:', error);
            // Fallback to console logging
            console.log('📧 [ACCOUNT DELETION] Email would be sent:');
            console.log(`To: ${to}`);
            console.log(`Subject: ${subject}`);
            console.log(`HTML Content: ${htmlContent.substring(0, 200)}...`);
        }
    }
    async sendDeletionConfirmationEmail(user, token, preferences) {
        const confirmationUrl = `${process.env.FRONTEND_URL}/account/confirm-deletion?token=${token}`;
        const preservedDataText = [
            preferences.preservePaymentData ? 'Payment data' : null,
            preferences.preserveTransactionHistory ? 'Transaction history' : null,
            preferences.preserveProfileData ? 'Profile data' : null,
            preferences.preserveSecurityLogs ? 'Security logs' : null,
        ].filter(Boolean).join(', ');
        await this.sendCustomEmail(user.email, '⚠️ Confirm Account Deletion Request', `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>🗑️ Account Deletion Confirmation Required</h2>
          <p>Hello ${user.firstName},</p>
          
          <p>You have requested to delete your account. Please review the details below:</p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>⚙️ Deletion Preferences</h3>
            <ul>
              <li><strong>Payment data:</strong> ${preferences.preservePaymentData ? '✅ Preserve' : '❌ Delete'}</li>
              <li><strong>Transaction history:</strong> ${preferences.preserveTransactionHistory ? '✅ Preserve' : '❌ Delete'}</li>
              <li><strong>Profile data:</strong> ${preferences.preserveProfileData ? '✅ Preserve' : '❌ Delete'}</li>
              <li><strong>Security logs:</strong> ${preferences.preserveSecurityLogs ? '✅ Preserve' : '❌ Delete'}</li>
            </ul>
            ${preservedDataText ? `<p><strong>Preserved data:</strong> ${preservedDataText}</p>` : ''}
            <p><strong>Retention period:</strong> ${preferences.customRetentionPeriod || 30} days</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" 
               style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              🗑️ Confirm Account Deletion
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            ⏰ This confirmation link expires in 24 hours.<br>
            If you didn't request this deletion, please ignore this email or contact support.
          </p>
        </div>
      `);
    }
    /**
     * Send deletion completed email
     */
    async sendDeletionCompletedEmail(user, preservedDataSummary) {
        await this.sendCustomEmail(user.email, '✅ Account Deletion Completed', `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>✅ Account Deletion Completed</h2>
          <p>Hello ${user.firstName},</p>
          
          <p>Your account has been successfully deleted as requested.</p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>📊 Data Summary</h3>
            <pre>${JSON.stringify(preservedDataSummary, null, 2)}</pre>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            If you have any preserved data, you can restore it when signing up again with this email address.
          </p>
        </div>
      `);
    }
    /**
     * Send data restoration confirmation email
     */
    async sendDataRestorationEmail(email, restoredData) {
        const subject = 'Data Restoration Completed';
        let restoredItems = [];
        if (restoredData.paymentData > 0) {
            restoredItems.push(`${restoredData.paymentData} payment records`);
        }
        if (restoredData.transactionHistory > 0) {
            restoredItems.push(`${restoredData.transactionHistory} transaction history items`);
        }
        if (restoredData.profileData) {
            restoredItems.push('profile backup data');
        }
        if (restoredData.securityLogs > 0) {
            restoredItems.push(`${restoredData.securityLogs} security log entries`);
        }
        const restoredItemsList = restoredItems.length > 0
            ? restoredItems.join(', ')
            : 'selected data items';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">Data Restoration Completed!</h2>
        <p>Great news! Your account data has been successfully restored.</p>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Restored Data:</h3>
          <p style="color: #666; margin-bottom: 0;">We have restored: ${restoredItemsList}</p>
        </div>
        
        <p>You can now log in to your account and access your restored data.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL || 'http://localhost:4200'}/auth/login"
             style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Login to Your Account
          </a>
        </div>
        
        <p style="color: #666; font-size: 12px;">
          If you have any questions about your restored data, please contact our support team.
        </p>
      </div>
    `;
        console.log(`📧 Sending data restoration email to: ${email}`);
        console.log('📦 Restored data summary:', restoredItemsList);
        // For now, we'll log the email content since we don't have a generic send method
        // In production, you'd want to add a generic sendEmail method to EmailService
        console.log('📧 Data restoration email content:');
        console.log(`To: ${email}`);
        console.log(`Subject: ${subject}`);
        console.log('✅ Email notification sent (via console log)');
    }
    /**
     * Export user data as downloadable JSON
     */
    async exportUserData(userId) {
        console.log('📤 Starting data export for user:', userId);
        // Get user
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Get all user data
        const payments = await this.paymentRepository.find({
            where: { userId: userId },
        });
        // Get preserved data if any
        const preservedData = await this.preservedDataRepository.find({
            where: { deletionRecordId: { like: `%${user.email}%` } },
        });
        // Create export data structure
        const exportData = {
            exportInfo: {
                exportedAt: new Date().toISOString(),
                exportedBy: user.email,
                exportId: (0, crypto_1.randomBytes)(16).toString('hex'),
            },
            userData: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                isEmailVerified: user.isEmailVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                twoFactorEnabled: user.twoFactorEnabled,
                roles: user.roles,
            },
            paymentData: {
                totalPayments: payments.length,
                payments: payments.map(payment => ({
                    id: payment.id,
                    amount: payment.amount,
                    currency: payment.currency,
                    status: payment.status,
                    razorpayOrderId: payment.razorpayOrderId,
                    razorpayPaymentId: payment.razorpayPaymentId,
                    description: payment.description,
                    createdAt: payment.createdAt,
                    metadata: payment.metadata,
                })),
            },
            preservedData: preservedData.length > 0 ? {
                totalRecords: preservedData.length,
                records: preservedData.map(data => ({
                    id: data.id,
                    dataType: data.dataType,
                    isEncrypted: data.isEncrypted,
                    dataSizeBytes: data.dataSizeBytes,
                    expiresAt: data.expiresAt,
                    createdAt: data.createdAt,
                })),
            } : null,
        };
        console.log('✅ Data export completed for user:', userId);
        return exportData;
    }
    /**
     * Request data export via email
     */
    async requestDataExport(userId) {
        console.log('📧 Requesting data export via email for user:', userId);
        // Get user
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Generate export data
        const exportData = await this.exportUserData(userId);
        // Send email with export data
        const exportJson = JSON.stringify(exportData, null, 2);
        const exportDate = new Date().toISOString().split('T')[0];
        await this.sendCustomEmail(user.email, '📦 Your Data Export', `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>📦 Your Data Export</h2>
        <p>Hello ${user.firstName},</p>
        
        <p>You requested an export of your account data. Please find your data attached below:</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>📋 Export Summary</h3>
          <ul>
            <li><strong>Export Date:</strong> ${exportDate}</li>
            <li><strong>User Email:</strong> ${user.email}</li>
            <li><strong>Total Payments:</strong> ${exportData.paymentData?.totalPayments || 0}</li>
            <li><strong>Account Created:</strong> ${user.createdAt}</li>
          </ul>
        </div>
        
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #ffeaa7;">
          <h3>💾 Your Data (JSON Format)</h3>
          <p style="font-size: 12px; color: #666;">
            Copy and paste the following JSON data into a text file to save it:
          </p>
          <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-size: 11px; overflow-x: auto; max-height: 400px;">${exportJson}</pre>
        </div>
        
        <p style="color: #666; font-size: 14px;">
          This export contains all your account data including profile information, payment records, and any preserved data.
          Please save this information securely if you need it for your records.
        </p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #888; font-size: 12px;">
          This email was sent automatically. If you didn't request this export, please contact our support team.
        </p>
      </div>
    `);
        console.log('✅ Data export email sent to:', user.email);
        return {
            message: 'Data export has been sent to your email address. Please check your inbox.',
        };
    }
    /**
     * Clean up expired or invalid deletion records
     * This helps prevent accumulation of stale records
     */
    async cleanupExpiredDeletionRecords() {
        console.log('🧹 Starting cleanup of expired deletion records');
        const now = new Date();
        // Find records that have expired and can be cleaned up
        const expiredRecords = await this.deletionRepository.find({
            where: {
                or: [
                    // Records where data has expired and restoration period is over
                    {
                        dataExpiryDate: { lt: now },
                        deletionStatus: 'data_preserved',
                    },
                    // Records where deletion token has expired and status is still pending
                    {
                        deletionTokenExpires: { lt: now },
                        deletionStatus: 'pending_confirmation',
                    },
                ],
            },
        });
        let cleanedCount = 0;
        for (const record of expiredRecords) {
            try {
                // Delete associated preserved data
                const preservedData = await this.preservedDataRepository.findByDeletionRecordId(record.id);
                for (const data of preservedData) {
                    await this.preservedDataRepository.deleteById(data.id);
                }
                // Delete the deletion record
                await this.deletionRepository.deleteById(record.id);
                cleanedCount++;
                console.log(`🗑️  Cleaned up expired record for ${record.email}`);
            }
            catch (error) {
                console.error(`❌ Failed to clean up record for ${record.email}:`, error);
            }
        }
        console.log(`✅ Cleanup completed. Removed ${cleanedCount} expired records`);
        return {
            message: `Cleanup completed. Removed ${cleanedCount} expired deletion records.`,
            cleanedCount,
        };
    }
};
exports.AccountDeletionService = AccountDeletionService;
exports.AccountDeletionService = AccountDeletionService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.PaymentRepository)),
    tslib_1.__param(2, (0, repository_1.repository)(repositories_1.AccountDeletionRecordRepository)),
    tslib_1.__param(3, (0, repository_1.repository)(repositories_1.PreservedUserDataRepository)),
    tslib_1.__param(4, (0, core_1.inject)('services.SecurityService')),
    tslib_1.__param(5, (0, core_1.inject)('services.EmailService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository,
        repositories_1.PaymentRepository,
        repositories_1.AccountDeletionRecordRepository,
        repositories_1.PreservedUserDataRepository,
        _1.SecurityService,
        _1.EmailService])
], AccountDeletionService);
//# sourceMappingURL=account-deletion.service.js.map