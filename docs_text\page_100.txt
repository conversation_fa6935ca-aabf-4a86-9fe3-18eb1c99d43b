=== Page 101 ===

101If you have installed Masonite but do not see this .env file then you can create it
manually and copy and paste the contents of .env-example file.
Environment files are loaded in this order:
1.Masonite will load the .env file located at your project root into the Python
environment.
2.Masonite will look for an APP_ENV variable inside the already loaded .env. If it is
defined it will try to load the .env.{APP_ENV} file corresponding to this environment
name.
For example, if APP_ENV is set to local, Masonite will additionally load the 
.env.local environment file.
When the server is ready all those variables will be loaded into the current environment
ready to be accessed in the different Masonite configuration files or directly with env()
helper.
If some variables contain spaces you should put variable content into double quotes:APP_ENV=local
APP_NAME="Masonite test project"Loading Order
Defining Variables
Reading Variables.env6/12/25, 3:02 AM Masonite Documentation