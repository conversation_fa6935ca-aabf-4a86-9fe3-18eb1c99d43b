=== Page 196 ===

196Then you can import most of the blocks available in Slack documentation and start
building your notification. You need to use the block() option. Once again you can
chain as many blocks as you want.
You can find all blocks name and options in slackblocks documentation and more
information in Slack blocks list.
Some blocks or elements might not be yet available in slackblocks, but most of them
should be there.
You should define the related route_notification_for_slack method on your
notifiable to return either
•a webhook URL or a list of webhook URLs (if you're using Incoming Webhooks)
•a channel name/ID or a list of channels names/IDs (if you're using Slack Web API)$ pip install slackblocks
from masonite.notification.components import SlackComponent
from slackblocks import Header<PERSON>lock, ImageBlock, DividerBlock
class Welcome(Notification):
    def to_slack(self, notifiable):
        return SlackComponent() \
            .text('Notification text') \
            .channel('#bot') \
            .block(HeaderBlock("Header title")) \
            .block(DividerBlock()) \
            .block(ImageBlock("https://path/to/image", "Alt image 
text", "Image title"))
Routing to notifiable6/12/25, 3:02 AM Masonite Documentation