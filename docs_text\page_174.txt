=== Page 175 ===

175Then in your templates you can use the asset helper:
The signature is:
You may also specify multiple paths as a dictionary:
Then inside your asset helper you can use dot notation to specify the path you want to
use:<img src="{{ asset('s3', user.avatar_url) }}">
asset('disk', file_name)
DISKS = {
    "default": "local",
    # "..",
    "s3": {
        "driver": "s3",
        # "..",
          "path": {
          "logos": "https://bucket.s3.us-east-2.amazonaws.com/logos",
          "invoices": "https://bucket.s3.us-east-
2.amazonaws.com/invoices"
        }
    },
}
<img src="{{ asset('s3.logos', user.avatar_url) }}">
<a href="{{ asset('s3.invoices', invoice_url) }}"Multiple Paths6/12/25, 3:02 AM Masonite Documentation