=== Page 167 ===

167Y<PERSON> can then register the event inside the provider:
When you emit the UserAdded event inside the controller, or somewhere else in the
project, it will now send this email out.
You can register as many listeners to the events as you like by simply adding more
listeners to the list.from app.mailables.WelcomeMailable import WelcomeMailable
class WelcomeEmail:
  def handle(self, event):
    from wsgi import application
    application.make("mail").send(
      WelcomeMailable().to('<EMAIL>')
    )
class EventsProvider(Provider):
    def register(self):
        self.application.make('event').listen(UserAddedEvent, 
[WelcomeListener])6/12/25, 3:02 AM Masonite Documentation