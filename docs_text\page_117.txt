=== Page 118 ===

118This will create a new api.py config file in your configuration directory that looks like
this:
This will attempt to import your user model but if you have a different model or if its in a
different location you can change it in that model.
This command will also generate a secret key, you should store that secret key in an
environment variable called JWT_SECRET. This will be used as a salt for encoding and
decoding the JWT token.
The authenticates key is used as a check to check against the database on every
request to see if the token is set on the user. By default, the database is not called to
check if the token is assigned to a user. One of the benefits of JWT is the need to not
have to make a database call to validate the user but if you want that behavior, you can
set this option to True
You should add some routes to your web.py file which can be used to authenticate
users to give them JWT tokens:python craft api:install
"""API Config"""
from app.models.User import User
from masonite.environment import env
DRIVERS = {
    "jwt": {
        "algorithm": "HS512",
        "secret": env("JWT_SECRET"),
        "model": User,
        "expires": None,
        "authenticates": False,
        "version": None,
    }
}
Routes6/12/25, 3:02 AM Masonite Documentation