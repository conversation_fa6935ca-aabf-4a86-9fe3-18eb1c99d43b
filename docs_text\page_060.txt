=== Page 61 ===

61Y<PERSON> can override the registered controllers location in Kernel.py file by editing the
default binding controllers.location.
You can multiple additional controller locations with add_controller_locations:
The best place to do this is in your Kernel.py file in the register_routes() method.
You should do it before registering routes, else registering routes will fail as Masonite will
fail to resolve controller classes.from masonite.routes import Route
Route.add_controller_locations("app/http/controllers", 
"other_module/controllers")
Adding locations6/12/25, 3:02 AM Masonite Documentation