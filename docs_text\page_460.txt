=== Page 461 ===

461Masonite no longer supports SASS and LESS compiling. Masonite now uses webpack
and NPM to compile assets. You will need to now reference the Compiling Assets
documentation.
You will need to remove the SassProvider completely from the providers list in 
config/providers.py. As well as remove the SassProvider import from on top of
the file.
You can also completely remove the configuration settings in your config/storage.py
file:
Be sure to reference the Compiling Assets documentation to know how to use the new
NPM features.
The container can no longer hold modules. Modules now have to be imported in the class
you require them. For example you can't bind a module like this:
and then make it somewhere else:SASSFILES = {
    'importFrom': [
        'storage/static'
    ],
    'includePaths': [
        'storage/static/sass'
    ],
    'compileTo': 'storage/compiled'
}
from config import auth
app.bind('AuthConfig', auth)Removed The Sass Provider
Removed The Ability For The Container To Hold Modules6/12/25, 3:02 AM Masonite Documentation