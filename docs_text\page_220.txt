=== Page 221 ===

221The async driver will simply run the jobs in memory using processes or threading. This is
the simplest driver as it does not need any special software or setup.
The available options include:password The password of your AMQP connection
port The port of your AMQP connection
vhostThe name of your virtual host. Can get this
through your AMQP connection dashboard
hostThe IP address or host name of your
connection.
channel The channel to push the queue jobs onto
Thedefaultnameofthequeuetopushthe
Option Description
blockingA boolean value on whether jobs should run
synchronously.
Useful for debugging purposes.
callbackThe name of the method on the job that should
run.
modeWhether the queue should spawn processes or
threads. Options are threading or 
multiprocess
workersThe numbers of processes or threads that
should spawn to run the jobs.Async Driver
Creating Jobs6/12/25, 3:02 AM Masonite Documentation