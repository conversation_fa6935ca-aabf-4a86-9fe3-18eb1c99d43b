=== Page 430 ===

430String providers will still work but it is not recommended and will not be supported in current
and future releases of Masonite.
There are a few changes in the wsgi.py file and the bootstrap/start.py file.
In the wsgi.py file we should add a new import at the top:
Then change the code logic of bootstrapping service providers from:from masonite.providers import (
    AppProvider,
    SessionProvider,
    RouteProvider
)
...
PROVIDERS = [
    # Framework Providers
    AppProvider,
    SessionProvider,
    RouteProvider,
    ....
]
...
# from pydoc import locate - Remove This
...
from config import application, providers
...
container.bind('WSGI', app)
container.bind('Application', application)
# New Additions Here
container.bind('ProvidersConfig', providers)
container.bind('Providers', providers)
container.bind('WSGIProviders', providers)WSGI changes6/12/25, 3:02 AM Masonite Documentation