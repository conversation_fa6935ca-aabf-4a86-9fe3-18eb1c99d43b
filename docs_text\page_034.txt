=== Page 35 ===

35This repo is simple and will be able to be installed following the installation instruction in
the README.
•Fork the MasoniteFramework/cookie-cutter repo.
•Clone that repo into your computer:
◦git clone http://github.com/your-username/cookie-cutter.git
•Checkout the current release branch (example: develop)
◦git checkout -b develop
•You should now be on a develop local branch.
•Run git pull origin develop to get the current release version.
•From there simply create your feature branches (<feature|fix>-<issue-number>)
and make your desired changes.
•Push to your origin repository:
◦git push origin change-default-orm
•Open a pull request and follow the PR process below
The trick to this is that we need it to be pip installed and then quickly editable until we like
it, and then pushed back to the repo for a PR. Do this only if you want to make changes to
the core Masonite package
To do this just:
•Fork the MasoniteFramework/masonite repo,
•Clone that repo into your computer:
◦git clone http://github.com/your-username/masonite.git
•Activate your masonite virtual environment (optional)
◦Go to where you installed masonite and activate the environment
•While inside the virtual environment, cd into the directory you installed core.
•Run pip install -e . from inside the masonite directory. This will install
masonite as a pip package in editable mode. Any changes you make to the codebaseEditing the Masonite repository6/12/25, 3:02 AM Masonite Documentation