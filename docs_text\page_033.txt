=== Page 34 ===

34Contributing Guide
When contributing to Masonite, please first discuss the change you wish to make via a
GitHub issue. Starting with a GitHub issue allows all contributors and maintainers to have
a chance to give input on the issue. It creates a public forum to discuss the best way to
implement the issue, fix, or improvement. It also creates an open discussion on if the
issue will even be permitted to be in the project.
Please note we have a code of conduct, please follow it in all your interactions with the
project. You can find it in this in this documentation as well as all of Masonite's
repositories.
The framework has 2 main parts: The "masonite" repo and the "cookie-cutter" repo.
The MasoniteFramework/cookie-cutter repository is the main repository that will
install when creating new projects using the project start command. This is actually
a full Masonite project. When you run project start it simply reaches out to this
GitHub repo, fetches the zip and unzips it on your computer. Not much development will
be done in this repository and won't be changed unless something requires changes in
the default installation project structure.
The MasoniteFramework/core repository is deprecated and development has been
moved into MasoniteFramework/masonite. This repository contains all the
development of Masonite and contains all the releases for Masonite. If you need to fix a
bug or add a new feature then this is the repository to fork and make your changes from.
The MasoniteFramework/craft is deprecated. This was where the craft CLI tool lived
that has since been moved into the masonite repository.
You can read about how the framework flows, works and architectural concepts here
Getting Started
Getting the Masonite cookie-cutter repository up and
running to be edited6/12/25, 3:02 AM Masonite Documentation