import { Getter } from '@loopback/core';
import { DefaultCrudRepository, BelongsToAccessor } from '@loopback/repository';
import { DbDataSource } from '../datasources';
import { CrawledContent, CrawledContentRelations, CrawlJob } from '../models';
import { CrawlJobRepository } from './crawl-job.repository';
export declare class CrawledContentRepository extends DefaultCrudRepository<CrawledContent, typeof CrawledContent.prototype.id, CrawledContentRelations> {
    protected crawlJobRepositoryGetter: Getter<CrawlJobRepository>;
    readonly crawlJob: BelongsToAccessor<CrawlJob, typeof CrawledContent.prototype.id>;
    constructor(dataSource: DbDataSource, crawlJobRepositoryGetter: Getter<CrawlJobRepository>);
    /**
     * Find content by crawl job ID
     */
    findByCrawlJobId(crawlJobId: string): Promise<CrawledContent[]>;
    /**
     * Find content by URL
     */
    findByUrl(url: string): Promise<CrawledContent[]>;
    /**
     * Find content by status
     */
    findByStatus(status: string, crawlJobId?: string): Promise<CrawledContent[]>;
    /**
     * Find selected content for document generation
     */
    findSelectedContent(crawlJobId: string, selectionGroup?: string): Promise<CrawledContent[]>;
    /**
     * Update content selection
     */
    updateSelection(contentIds: string[], isSelected: boolean, selectionGroup?: string): Promise<void>;
    /**
     * Get content statistics for a crawl job
     */
    getCrawlJobContentStatistics(crawlJobId: string): Promise<object>;
    /**
     * Group content by content type
     */
    private groupContentByType;
    /**
     * Group content by depth
     */
    private groupContentByDepth;
    /**
     * Search content by text
     */
    searchContent(crawlJobId: string, searchTerm: string, limit?: number): Promise<CrawledContent[]>;
    /**
     * Get content by depth range
     */
    findByDepthRange(crawlJobId: string, minDepth: number, maxDepth: number): Promise<CrawledContent[]>;
    /**
     * Clean up content files for deleted crawl jobs
     */
    cleanupOrphanedContent(): Promise<number>;
}
