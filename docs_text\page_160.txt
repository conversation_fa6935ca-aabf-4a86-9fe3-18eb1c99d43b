=== Page 161 ===

161CSRF Protection
CSRF protection typically entails setting a unique token to the user for that page request
that matches the same token on the server. This prevents any person from submitting a
form without the correct token. There are many online resources that teach what CSRF
does and how it works but Masonite makes it really simple to use.
The CSRF features for Masonite are located in the CsrfProvider Service Provider and
the CsrfMiddleware. If you do not wish to have CSRF protection then you can safely
remove both of these.
The CsrfProvider simply loads the CSRF features into the container and the 
CsrfMiddleware is what actually generates the keys and checks if they are valid.
By default, all POST requests require a CSRF token. We can simply add a CSRF token in
our forms by adding the {{ csrf_field }} tag to our form like so:
This will add a hidden field that looks like:<form action="/dashboard" method="POST">
    {{ csrf_field }}
    <input type="text" name="first_name">
</form>
<input type="hidden" name="__token" value="8389hdnajs8...">Introduction
Getting Started
Templates6/12/25, 3:02 AM Masonite Documentation