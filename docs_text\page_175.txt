=== Page 176 ===

176Hash ID's
The Masonite hashing feature is a very useful feature to prevent exposing ID's in your
application.
Many times you need to expose your database primary keys to the frontend. For example,
when updating a record, you might need to pass in a primary key value to a URL like 
/users/10/settings.
Typically you want to hide these key values from a hacker trying to change these values
easily.
With the Masonite hashing feature you can change a value like 10 to l9avmeG and
prevent exposing those sensitive integer values.
The Masonite hashing feature automatically decodes values before they get to the
controller. To do this it you need to specify both a middleware to help decode the values
as well as the provider to register the helper in the templates.
For the middleare you can add it easily:
You should put the Hash ID middleware towards the top of the middleware stack so it will
decode the request properly before getting to the other middleware in the stack.from masonite.essentials.middleware import HashIDMiddleware
# ..
route_middleware = {
  "web": [
    HashIDMiddleware,
    SessionMiddleware, 
    EncryptCookies,
    VerifyCsrfToken,
  ],
}Setup6/12/25, 3:02 AM Masonite Documentation