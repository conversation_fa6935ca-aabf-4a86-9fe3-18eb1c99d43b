=== Page 391 ===

391We no longer need to do:
We can now simply do:
Previously we had to specify the status code as a string:
in order for these to be used properly. Now we can just specify the status code:
There is quite a bit of things to remember when binding various things into the container.
For example when binding commands, the key needs to be postfixed with Command like 
ModelCommand. Now we can do things like:
Along with this there are several other methods to help you bind things into the container
without having to remember all the special rules involved, if any.{{ csrf_field|safe }}
{{ csrf_field }}
def show(self, request: Request):
    request.status('500 Internal Server Error')
def show(self, request: Request):
    request.status(500)
def register(self):
    self.commands(Command1(), Command2())Improved setting status codes
Added several new methods to service
providers6/12/25, 3:02 AM Masonite Documentation