import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {CrawlJobRepository, CrawledContentRepository} from '../repositories';
import {CrawlJob, CrawledContent} from '../models';
import {spawn, ChildProcess} from 'child_process';
import * as path from 'path';
import * as fs from 'fs/promises';

export interface CrawlOptions {
  maxDepth?: number;
  maxPages?: number;
  allowedContentTypes?: string[];
  excludePatterns?: string[];
  includePatterns?: string[];
  followExternalLinks?: boolean;
  respectRobotsTxt?: boolean;
  delayBetweenRequests?: number;
  userAgent?: string;
  timeout?: number;
}

export interface CrawlProgress {
  jobId: string;
  status: string;
  processedPages: number;
  totalPages: number;
  currentUrl?: string;
  errorMessage?: string;
}

@injectable({scope: BindingScope.SINGLETON})
export class CrawlerService {
  private activeCrawlers: Map<string, ChildProcess> = new Map();
  private crawlerScriptPath: string;

  constructor(
    @repository(CrawlJobRepository)
    public crawlJobRepository: CrawlJobRepository,
    @repository(CrawledContentRepository)
    public crawledContentRepository: CrawledContentRepository,
  ) {
    // Path to the Python crawler script
    this.crawlerScriptPath = path.join(__dirname, '../../scripts/crawler.py');
  }

  /**
   * Start a new crawl job
   */
  async startCrawl(crawlJob: CrawlJob): Promise<void> {
    try {
      // Update job status to running
      await this.crawlJobRepository.updateById(crawlJob.id, {
        status: 'running',
        startedAt: new Date(),
        updatedAt: new Date(),
      });

      // Prepare crawl options
      const options: CrawlOptions = {
        maxDepth: crawlJob.maxDepth,
        maxPages: crawlJob.maxPages,
        allowedContentTypes: crawlJob.allowedContentTypes,
        excludePatterns: crawlJob.excludePatterns,
        includePatterns: crawlJob.includePatterns,
        followExternalLinks: crawlJob.followExternalLinks,
        respectRobotsTxt: crawlJob.respectRobotsTxt,
        delayBetweenRequests: crawlJob.delayBetweenRequests,
        ...crawlJob.crawlOptions,
      };

      // Start Python crawler process
      await this.spawnCrawlerProcess(crawlJob.id, crawlJob.url, options);

    } catch (error) {
      await this.handleCrawlError(crawlJob.id, error);
    }
  }

  /**
   * Stop a running crawl job
   */
  async stopCrawl(jobId: string): Promise<void> {
    const crawler = this.activeCrawlers.get(jobId);
    if (crawler) {
      crawler.kill('SIGTERM');
      this.activeCrawlers.delete(jobId);
      
      await this.crawlJobRepository.updateById(jobId, {
        status: 'cancelled',
        completedAt: new Date(),
        updatedAt: new Date(),
      });
    }
  }

  /**
   * Pause a running crawl job
   */
  async pauseCrawl(jobId: string): Promise<void> {
    const crawler = this.activeCrawlers.get(jobId);
    if (crawler) {
      crawler.kill('SIGUSR1'); // Send pause signal
      
      await this.crawlJobRepository.updateById(jobId, {
        status: 'paused',
        updatedAt: new Date(),
      });
    }
  }

  /**
   * Resume a paused crawl job
   */
  async resumeCrawl(jobId: string): Promise<void> {
    const crawlJob = await this.crawlJobRepository.findById(jobId);
    if (crawlJob.status === 'paused') {
      const crawler = this.activeCrawlers.get(jobId);
      if (crawler) {
        crawler.kill('SIGUSR2'); // Send resume signal
        
        await this.crawlJobRepository.updateById(jobId, {
          status: 'running',
          updatedAt: new Date(),
        });
      }
    }
  }

  /**
   * Get crawl job progress
   */
  async getCrawlProgress(jobId: string): Promise<CrawlProgress> {
    const crawlJob = await this.crawlJobRepository.findById(jobId);
    const contentCount = await this.crawledContentRepository.count({crawlJobId: jobId});
    
    return {
      jobId,
      status: crawlJob.status,
      processedPages: crawlJob.processedPages,
      totalPages: crawlJob.totalPages,
      errorMessage: crawlJob.errorMessage,
    };
  }

  /**
   * Spawn Python crawler process
   */
  private async spawnCrawlerProcess(
    jobId: string,
    url: string,
    options: CrawlOptions,
  ): Promise<void> {
    const args = [
      this.crawlerScriptPath,
      '--job-id', jobId,
      '--url', url,
      '--options', JSON.stringify(options),
      '--callback-url', `http://localhost:3002/api/crawler/callback`,
    ];

    const crawler = spawn('python', args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        PYTHONPATH: path.join(__dirname, '../../scripts'),
      },
    });

    this.activeCrawlers.set(jobId, crawler);

    // Handle crawler output
    crawler.stdout.on('data', (data) => {
      console.log(`Crawler ${jobId} stdout:`, data.toString());
      this.handleCrawlerOutput(jobId, data.toString());
    });

    crawler.stderr.on('data', (data) => {
      console.error(`Crawler ${jobId} stderr:`, data.toString());
    });

    crawler.on('close', (code) => {
      console.log(`Crawler ${jobId} exited with code ${code}`);
      this.activeCrawlers.delete(jobId);
      this.handleCrawlerExit(jobId, code || 0);
    });

    crawler.on('error', (error) => {
      console.error(`Crawler ${jobId} error:`, error);
      this.activeCrawlers.delete(jobId);
      this.handleCrawlError(jobId, error);
    });
  }

  /**
   * Handle crawler output for progress updates
   */
  private async handleCrawlerOutput(jobId: string, output: string): Promise<void> {
    try {
      const lines = output.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (line.startsWith('PROGRESS:')) {
          const progressData = JSON.parse(line.substring(9));
          await this.updateCrawlProgress(jobId, progressData);
        } else if (line.startsWith('CONTENT:')) {
          const contentData = JSON.parse(line.substring(8));
          await this.saveCrawledContent(jobId, contentData);
        }
      }
    } catch (error) {
      console.error('Error parsing crawler output:', error);
    }
  }

  /**
   * Handle crawler process exit
   */
  private async handleCrawlerExit(jobId: string, code: number): Promise<void> {
    const status = code === 0 ? 'completed' : 'failed';
    const errorMessage = code !== 0 ? `Crawler exited with code ${code}` : undefined;
    
    await this.crawlJobRepository.updateById(jobId, {
      status,
      completedAt: new Date(),
      updatedAt: new Date(),
      errorMessage,
    });
  }

  /**
   * Handle crawl errors
   */
  private async handleCrawlError(jobId: string, error: any): Promise<void> {
    await this.crawlJobRepository.updateById(jobId, {
      status: 'failed',
      completedAt: new Date(),
      updatedAt: new Date(),
      errorMessage: error.message || 'Unknown error occurred',
    });
  }

  /**
   * Update crawl progress
   */
  private async updateCrawlProgress(jobId: string, progressData: any): Promise<void> {
    await this.crawlJobRepository.updateProgress(
      jobId,
      progressData.processedPages,
      progressData.totalPages,
      progressData.status,
    );
  }

  /**
   * Save crawled content
   */
  private async saveCrawledContent(jobId: string, contentData: any): Promise<void> {
    const content = new CrawledContent({
      crawlJobId: jobId,
      url: contentData.url,
      title: contentData.title,
      content: contentData.content,
      htmlContent: contentData.htmlContent,
      markdownContent: contentData.markdownContent,
      contentType: contentData.contentType,
      depth: contentData.depth,
      contentLength: contentData.contentLength,
      statusCode: contentData.statusCode,
      status: 'completed',
      extractedLinks: contentData.extractedLinks || [],
      extractedImages: contentData.extractedImages || [],
      metadata: contentData.metadata || {},
      headers: contentData.headers || {},
      parentUrl: contentData.parentUrl,
      processingTimeMs: contentData.processingTimeMs || 0,
      crawledAt: new Date(),
    });

    await this.crawledContentRepository.create(content);
  }

  /**
   * Get active crawlers count
   */
  getActiveCrawlersCount(): number {
    return this.activeCrawlers.size;
  }

  /**
   * Get all active crawler job IDs
   */
  getActiveCrawlerJobIds(): string[] {
    return Array.from(this.activeCrawlers.keys());
  }
}
