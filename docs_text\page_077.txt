=== Page 78 ===

78Then you can use the ip() helper:
IP address is retrieved from various HTTP request headers in the following order:
•HTTP_CLIENT_IP
•HTTP_X_FORWARDED_FOR
•HTTP_X_FORWARDED
•HTTP_X_CLUSTER_CLIENT_IP
•HTTP_FORWARDED_FOR
•HTTP_FORWARDED
•REMOTE_ADDR
The first non-private and non-reserved IP address found by resolving the different
headers will be returned by the helper.
The headers used and their order can be customized by overriding the IpMiddleware
and changing the headers attribute:# Kernel.py
from masonite.middleware import IpMiddleware
class Kernel:
    http_middleware = [
      # ...
      IpMiddleware
    ]
def show(self, request: Request):
    request.ip()
class CustomIpMiddleware(IpMiddleware):
    headers = [
      "HTTP_CLIENT_IP",
      "REMOTE_ADDR"
    ]6/12/25, 3:02 AM Masonite Documentation