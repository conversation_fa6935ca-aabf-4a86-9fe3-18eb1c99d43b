import { UserRepository, OtpRepository } from '../repositories';
import { RecoveryCodeService } from './recovery-code.service';
export declare class SecurityService {
    userRepository: UserRepository;
    otpRepository: OtpRepository;
    recoveryCodeService?: RecoveryCodeService | undefined;
    constructor(userRepository: UserRepository, otpRepository: OtpRepository, recoveryCodeService?: RecoveryCodeService | undefined);
    generateTwoFactorSecret(userId: string): Promise<{
        secret: string;
        qrCode: string;
    }>;
    verifyTwoFactorToken(userId: string, token: string): Promise<boolean>;
    enableTwoFactor(userId: string, token: string): Promise<void>;
    disableTwoFactor(userId: string, token: string): Promise<void>;
    generateOTP(identifier: string, type: string): Promise<string>;
    verifyOTP(identifier: string, code: string, type: string): Promise<boolean>;
    generatePasswordResetToken(): Promise<string>;
    generateEmailVerificationToken(): Promise<string>;
    hashPassword(password: string): Promise<string>;
    isDisposableEmail(email: string): Promise<boolean>;
    validatePasswordStrength(password: string): Promise<{
        isValid: boolean;
        errors: string[];
    }>;
    generateBackupCodes(userId: string): Promise<string[]>;
    verifyBackupCode(userId: string, code: string): Promise<boolean>;
}
