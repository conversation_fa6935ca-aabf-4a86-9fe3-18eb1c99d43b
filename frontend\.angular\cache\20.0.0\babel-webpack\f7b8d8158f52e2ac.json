{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nfunction OAuthSuccessComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-card\", 4)(2, \"mat-card-content\")(3, \"div\", 5);\n    i0.ɵɵelement(4, \"mat-spinner\", 6);\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.statusMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.subMessage);\n  }\n}\nfunction OAuthSuccessComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-card\", 8)(2, \"mat-card-content\")(3, \"div\", 9)(4, \"mat-icon\", 10);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\");\n    i0.ɵɵtext(7, \"Authentication Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function OAuthSuccessComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToLogin());\n    });\n    i0.ɵɵtext(11, \" Try Again \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nexport class OAuthSuccessComponent {\n  constructor(route, router, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.isProcessing = true;\n    this.hasError = false;\n    this.statusMessage = 'Completing authentication...';\n    this.subMessage = 'Please wait while we finalize your login.';\n    this.errorMessage = '';\n  }\n  ngOnInit() {\n    this.handleOAuthCallback();\n  }\n  handleOAuthCallback() {\n    this.route.queryParams.subscribe(params => {\n      console.log('🔍 OAuth Success - Query params received:', params);\n      const token = params['token'];\n      const isNewUser = params['isNewUser'] === 'true';\n      const provider = params['provider'];\n      const error = params['error'];\n      console.log('🔍 OAuth Success - Parsed values:', {\n        token: token ? 'Present' : 'Missing',\n        isNewUser,\n        provider,\n        error\n      });\n      if (error) {\n        console.log('❌ OAuth Success - Error found:', error);\n        this.showError(`Authentication failed: ${error}`);\n        return;\n      }\n      if (!token) {\n        console.log('❌ OAuth Success - No token received');\n        this.showError('No authentication token received. Please try again.');\n        return;\n      }\n      console.log('✅ OAuth Success - Processing authentication...');\n      this.completeAuthentication(token, isNewUser, provider);\n    });\n  }\n  completeAuthentication(token, isNewUser, provider) {\n    this.statusMessage = isNewUser ? 'Creating your account...' : 'Signing you in...';\n    this.subMessage = `Authenticated with ${this.formatProviderName(provider)}`;\n    try {\n      // Set the token in auth service\n      this.authService.setToken(token);\n      // Refresh user data from server\n      this.authService.refreshUserData().subscribe({\n        next: user => {\n          this.statusMessage = 'Success!';\n          this.subMessage = isNewUser ? `Welcome! Your account has been created and linked to ${this.formatProviderName(provider)}.` : `Welcome back! You've been signed in with ${this.formatProviderName(provider)}.`;\n          // Show success message\n          this.snackBar.open(isNewUser ? `Account created and signed in with ${this.formatProviderName(provider)}!` : `Signed in with ${this.formatProviderName(provider)}!`, 'Close', {\n            duration: 4000\n          });\n          // Wait a moment for user to see success message, then redirect\n          setTimeout(() => {\n            this.redirectToDashboard();\n          }, 2000);\n        },\n        error: error => {\n          console.error('Failed to refresh user data:', error);\n          // Even if refresh fails, we have the token, so redirect to dashboard\n          // The dashboard will handle any additional authentication checks\n          this.redirectToDashboard();\n        }\n      });\n    } catch (error) {\n      console.error('Token processing error:', error);\n      this.showError('Failed to process authentication. Please try again.');\n    }\n  }\n  redirectToDashboard() {\n    this.router.navigate(['/dashboard']).then(() => {\n      // Clear URL parameters after successful navigation\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {},\n        replaceUrl: true\n      });\n    });\n  }\n  showError(message) {\n    this.isProcessing = false;\n    this.hasError = true;\n    this.errorMessage = message;\n    this.snackBar.open(message, 'Close', {\n      duration: 6000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  formatProviderName(provider) {\n    switch (provider?.toLowerCase()) {\n      case 'google':\n        return 'Google';\n      case 'github':\n        return 'GitHub';\n      case 'microsoft':\n        return 'Microsoft';\n      default:\n        return provider || 'OAuth Provider';\n    }\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static #_ = this.ɵfac = function OAuthSuccessComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OAuthSuccessComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: OAuthSuccessComponent,\n    selectors: [[\"app-oauth-success\"]],\n    standalone: false,\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"oauth-success-container\"], [\"class\", \"loading-card\", 4, \"ngIf\"], [\"class\", \"error-card\", 4, \"ngIf\"], [1, \"loading-card\"], [1, \"success-card\"], [1, \"loading-content\"], [\"diameter\", \"60\"], [1, \"error-card\"], [1, \"error-card-content\"], [1, \"error-content\"], [\"color\", \"warn\", 1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"]],\n    template: function OAuthSuccessComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, OAuthSuccessComponent_div_1_Template, 9, 2, \"div\", 1)(2, OAuthSuccessComponent_div_2_Template, 12, 1, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasError);\n      }\n    },\n    dependencies: [i4.NgIf, i5.MatCard, i5.MatCardContent, i6.MatButton, i7.MatIcon, i8.MatProgressSpinner],\n    styles: [\".oauth-success-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n      padding: 20px;\\n    }\\n\\n    .success-card[_ngcontent-%COMP%], .error-card-content[_ngcontent-%COMP%] {\\n      max-width: 500px;\\n      width: 100%;\\n      margin: 0 auto;\\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n      border-radius: 15px;\\n    }\\n\\n    .loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%] {\\n      text-align: center;\\n      padding: 40px 20px;\\n    }\\n\\n    .loading-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n      margin: 20px 0 10px 0;\\n      color: #333;\\n      font-weight: 500;\\n    }\\n\\n    .loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      color: #666;\\n      margin-bottom: 20px;\\n      line-height: 1.5;\\n    }\\n\\n    .error-icon[_ngcontent-%COMP%] {\\n      font-size: 48px;\\n      height: 48px;\\n      width: 48px;\\n      margin-bottom: 20px;\\n    }\\n\\n    .retry-button[_ngcontent-%COMP%] {\\n      margin-top: 20px;\\n      padding: 12px 30px;\\n      border-radius: 25px;\\n    }\\n\\n    mat-spinner[_ngcontent-%COMP%] {\\n      margin: 0 auto 20px auto;\\n    }\\n\\n    @media (max-width: 600px) {\\n      .oauth-success-container[_ngcontent-%COMP%] {\\n        padding: 10px;\\n      }\\n      \\n      .loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%] {\\n        padding: 30px 15px;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "statusMessage", "subMessage", "ɵɵlistener", "OAuthSuccessComponent_div_2_Template_button_click_10_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "goToLogin", "errorMessage", "OAuthSuccessComponent", "constructor", "route", "router", "authService", "snackBar", "isProcessing", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "handleOAuthCallback", "queryParams", "subscribe", "params", "console", "log", "token", "isNewUser", "provider", "error", "showError", "completeAuthentication", "formatProviderName", "setToken", "refreshUserData", "next", "user", "open", "duration", "setTimeout", "redirectToDashboard", "navigate", "then", "relativeTo", "replaceUrl", "message", "panelClass", "toLowerCase", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthService", "i3", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "OAuthSuccessComponent_Template", "rf", "ctx", "ɵɵtemplate", "OAuthSuccessComponent_div_1_Template", "OAuthSuccessComponent_div_2_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\oauth-success\\oauth-success.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-oauth-success',\r\n  template: `\r\n    <div class=\"oauth-success-container\">\r\n      <div class=\"loading-card\" *ngIf=\"isProcessing\">\r\n        <mat-card class=\"success-card\">\r\n          <mat-card-content>\r\n            <div class=\"loading-content\">\r\n              <mat-spinner diameter=\"60\"></mat-spinner>\r\n              <h2>{{ statusMessage }}</h2>\r\n              <p>{{ subMessage }}</p>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <div class=\"error-card\" *ngIf=\"hasError\">\r\n        <mat-card class=\"error-card-content\">\r\n          <mat-card-content>\r\n            <div class=\"error-content\">\r\n              <mat-icon color=\"warn\" class=\"error-icon\">error</mat-icon>\r\n              <h2>Authentication Failed</h2>\r\n              <p>{{ errorMessage }}</p>\r\n              <button mat-raised-button color=\"primary\" (click)=\"goToLogin()\" class=\"retry-button\">\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .oauth-success-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      padding: 20px;\r\n    }\r\n\r\n    .success-card, .error-card-content {\r\n      max-width: 500px;\r\n      width: 100%;\r\n      margin: 0 auto;\r\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\r\n      border-radius: 15px;\r\n    }\r\n\r\n    .loading-content, .error-content {\r\n      text-align: center;\r\n      padding: 40px 20px;\r\n    }\r\n\r\n    .loading-content h2, .error-content h2 {\r\n      margin: 20px 0 10px 0;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .loading-content p, .error-content p {\r\n      color: #666;\r\n      margin-bottom: 20px;\r\n      line-height: 1.5;\r\n    }\r\n\r\n    .error-icon {\r\n      font-size: 48px;\r\n      height: 48px;\r\n      width: 48px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .retry-button {\r\n      margin-top: 20px;\r\n      padding: 12px 30px;\r\n      border-radius: 25px;\r\n    }\r\n\r\n    mat-spinner {\r\n      margin: 0 auto 20px auto;\r\n    }\r\n\r\n    @media (max-width: 600px) {\r\n      .oauth-success-container {\r\n        padding: 10px;\r\n      }\r\n      \r\n      .loading-content, .error-content {\r\n        padding: 30px 15px;\r\n      }\r\n    }\r\n  `],\r\n  standalone: false\r\n})\r\nexport class OAuthSuccessComponent implements OnInit {\r\n  isProcessing = true;\r\n  hasError = false;\r\n  statusMessage = 'Completing authentication...';\r\n  subMessage = 'Please wait while we finalize your login.';\r\n  errorMessage = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.handleOAuthCallback();\r\n  }\r\n  private handleOAuthCallback(): void {\r\n    this.route.queryParams.subscribe(params => {\r\n      console.log('🔍 OAuth Success - Query params received:', params);\r\n      \r\n      const token = params['token'];\r\n      const isNewUser = params['isNewUser'] === 'true';\r\n      const provider = params['provider'];\r\n      const error = params['error'];\r\n\r\n      console.log('🔍 OAuth Success - Parsed values:', { token: token ? 'Present' : 'Missing', isNewUser, provider, error });\r\n\r\n      if (error) {\r\n        console.log('❌ OAuth Success - Error found:', error);\r\n        this.showError(`Authentication failed: ${error}`);\r\n        return;\r\n      }\r\n\r\n      if (!token) {\r\n        console.log('❌ OAuth Success - No token received');\r\n        this.showError('No authentication token received. Please try again.');\r\n        return;\r\n      }\r\n\r\n      console.log('✅ OAuth Success - Processing authentication...');\r\n      this.completeAuthentication(token, isNewUser, provider);\r\n    });\r\n  }\r\n\r\n  private completeAuthentication(token: string, isNewUser: boolean, provider: string): void {\r\n    this.statusMessage = isNewUser ? 'Creating your account...' : 'Signing you in...';\r\n    this.subMessage = `Authenticated with ${this.formatProviderName(provider)}`;\r\n\r\n    try {\r\n      // Set the token in auth service\r\n      this.authService.setToken(token);\r\n\r\n      // Refresh user data from server\r\n      this.authService.refreshUserData().subscribe({\r\n        next: (user) => {\r\n          this.statusMessage = 'Success!';\r\n          this.subMessage = isNewUser ? \r\n            `Welcome! Your account has been created and linked to ${this.formatProviderName(provider)}.` :\r\n            `Welcome back! You've been signed in with ${this.formatProviderName(provider)}.`;\r\n\r\n          // Show success message\r\n          this.snackBar.open(\r\n            isNewUser ? \r\n              `Account created and signed in with ${this.formatProviderName(provider)}!` : \r\n              `Signed in with ${this.formatProviderName(provider)}!`, \r\n            'Close', \r\n            { duration: 4000 }\r\n          );\r\n\r\n          // Wait a moment for user to see success message, then redirect\r\n          setTimeout(() => {\r\n            this.redirectToDashboard();\r\n          }, 2000);\r\n        },\r\n        error: (error) => {\r\n          console.error('Failed to refresh user data:', error);\r\n          // Even if refresh fails, we have the token, so redirect to dashboard\r\n          // The dashboard will handle any additional authentication checks\r\n          this.redirectToDashboard();\r\n        }\r\n      });\r\n    } catch (error) {\r\n      console.error('Token processing error:', error);\r\n      this.showError('Failed to process authentication. Please try again.');\r\n    }\r\n  }\r\n\r\n  private redirectToDashboard(): void {\r\n    this.router.navigate(['/dashboard']).then(() => {\r\n      // Clear URL parameters after successful navigation\r\n      this.router.navigate([], {\r\n        relativeTo: this.route,\r\n        queryParams: {},\r\n        replaceUrl: true\r\n      });\r\n    });\r\n  }\r\n\r\n  private showError(message: string): void {\r\n    this.isProcessing = false;\r\n    this.hasError = true;\r\n    this.errorMessage = message;\r\n    \r\n    this.snackBar.open(message, 'Close', { \r\n      duration: 6000,\r\n      panelClass: ['error-snackbar']\r\n    });\r\n  }\r\n\r\n  private formatProviderName(provider: string): string {\r\n    switch (provider?.toLowerCase()) {\r\n      case 'google': return 'Google';\r\n      case 'github': return 'GitHub';\r\n      case 'microsoft': return 'Microsoft';\r\n      default: return provider || 'OAuth Provider';\r\n    }\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;IAYYA,EAHN,CAAAC,cAAA,aAA+C,kBACd,uBACX,aACa;IAC3BD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAI3BH,EAJ2B,CAAAI,YAAA,EAAI,EACnB,EACW,EACV,EACP;;;;IALMJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAmB;IACpBR,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAE,UAAA,CAAgB;;;;;;IAUnBT,EAJR,CAAAC,cAAA,aAAyC,kBACF,uBACjB,aACW,mBACiB;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1DJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzBJ,EAAA,CAAAC,cAAA,kBAAqF;IAA3CD,EAAA,CAAAU,UAAA,mBAAAC,8DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASR,MAAA,CAAAS,SAAA,EAAW;IAAA,EAAC;IAC7DhB,EAAA,CAAAG,MAAA,mBACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;IAPKJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAU,YAAA,CAAkB;;;AA0EnC,OAAM,MAAOC,qBAAqB;EAOhCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAVlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAjB,aAAa,GAAG,8BAA8B;IAC9C,KAAAC,UAAU,GAAG,2CAA2C;IACxD,KAAAQ,YAAY,GAAG,EAAE;EAOd;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EACQA,mBAAmBA,CAAA;IACzB,IAAI,CAACP,KAAK,CAACQ,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEF,MAAM,CAAC;MAEhE,MAAMG,KAAK,GAAGH,MAAM,CAAC,OAAO,CAAC;MAC7B,MAAMI,SAAS,GAAGJ,MAAM,CAAC,WAAW,CAAC,KAAK,MAAM;MAChD,MAAMK,QAAQ,GAAGL,MAAM,CAAC,UAAU,CAAC;MACnC,MAAMM,KAAK,GAAGN,MAAM,CAAC,OAAO,CAAC;MAE7BC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAAEC,KAAK,EAAEA,KAAK,GAAG,SAAS,GAAG,SAAS;QAAEC,SAAS;QAAEC,QAAQ;QAAEC;MAAK,CAAE,CAAC;MAEtH,IAAIA,KAAK,EAAE;QACTL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,KAAK,CAAC;QACpD,IAAI,CAACC,SAAS,CAAC,0BAA0BD,KAAK,EAAE,CAAC;QACjD;MACF;MAEA,IAAI,CAACH,KAAK,EAAE;QACVF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD,IAAI,CAACK,SAAS,CAAC,qDAAqD,CAAC;QACrE;MACF;MAEAN,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D,IAAI,CAACM,sBAAsB,CAACL,KAAK,EAAEC,SAAS,EAAEC,QAAQ,CAAC;IACzD,CAAC,CAAC;EACJ;EAEQG,sBAAsBA,CAACL,KAAa,EAAEC,SAAkB,EAAEC,QAAgB;IAChF,IAAI,CAAC3B,aAAa,GAAG0B,SAAS,GAAG,0BAA0B,GAAG,mBAAmB;IACjF,IAAI,CAACzB,UAAU,GAAG,sBAAsB,IAAI,CAAC8B,kBAAkB,CAACJ,QAAQ,CAAC,EAAE;IAE3E,IAAI;MACF;MACA,IAAI,CAACb,WAAW,CAACkB,QAAQ,CAACP,KAAK,CAAC;MAEhC;MACA,IAAI,CAACX,WAAW,CAACmB,eAAe,EAAE,CAACZ,SAAS,CAAC;QAC3Ca,IAAI,EAAGC,IAAI,IAAI;UACb,IAAI,CAACnC,aAAa,GAAG,UAAU;UAC/B,IAAI,CAACC,UAAU,GAAGyB,SAAS,GACzB,wDAAwD,IAAI,CAACK,kBAAkB,CAACJ,QAAQ,CAAC,GAAG,GAC5F,4CAA4C,IAAI,CAACI,kBAAkB,CAACJ,QAAQ,CAAC,GAAG;UAElF;UACA,IAAI,CAACZ,QAAQ,CAACqB,IAAI,CAChBV,SAAS,GACP,sCAAsC,IAAI,CAACK,kBAAkB,CAACJ,QAAQ,CAAC,GAAG,GAC1E,kBAAkB,IAAI,CAACI,kBAAkB,CAACJ,QAAQ,CAAC,GAAG,EACxD,OAAO,EACP;YAAEU,QAAQ,EAAE;UAAI,CAAE,CACnB;UAED;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,mBAAmB,EAAE;UAC5B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDX,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD;UACA;UACA,IAAI,CAACW,mBAAmB,EAAE;QAC5B;OACD,CAAC;IACJ,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAACC,SAAS,CAAC,qDAAqD,CAAC;IACvE;EACF;EAEQU,mBAAmBA,CAAA;IACzB,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAK;MAC7C;MACA,IAAI,CAAC5B,MAAM,CAAC2B,QAAQ,CAAC,EAAE,EAAE;QACvBE,UAAU,EAAE,IAAI,CAAC9B,KAAK;QACtBQ,WAAW,EAAE,EAAE;QACfuB,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQd,SAASA,CAACe,OAAe;IAC/B,IAAI,CAAC5B,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACR,YAAY,GAAGmC,OAAO;IAE3B,IAAI,CAAC7B,QAAQ,CAACqB,IAAI,CAACQ,OAAO,EAAE,OAAO,EAAE;MACnCP,QAAQ,EAAE,IAAI;MACdQ,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEQd,kBAAkBA,CAACJ,QAAgB;IACzC,QAAQA,QAAQ,EAAEmB,WAAW,EAAE;MAC7B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC;QAAS,OAAOnB,QAAQ,IAAI,gBAAgB;IAC9C;EACF;EAEAnB,SAASA,CAAA;IACP,IAAI,CAACK,MAAM,CAAC2B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAAC,QAAAO,CAAA,G;qCAzHUrC,qBAAqB,EAAAlB,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1D,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA3D,EAAA,CAAAwD,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAAwD,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArB9C,qBAAqB;IAAA+C,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7F9BxE,EAAA,CAAAC,cAAA,aAAqC;QAanCD,EAZA,CAAA0E,UAAA,IAAAC,oCAAA,iBAA+C,IAAAC,oCAAA,kBAYN;QAc3C5E,EAAA,CAAAI,YAAA,EAAM;;;QA1BuBJ,EAAA,CAAAK,SAAA,EAAkB;QAAlBL,EAAA,CAAA6E,UAAA,SAAAJ,GAAA,CAAAjD,YAAA,CAAkB;QAYpBxB,EAAA,CAAAK,SAAA,EAAc;QAAdL,EAAA,CAAA6E,UAAA,SAAAJ,GAAA,CAAAhD,QAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}