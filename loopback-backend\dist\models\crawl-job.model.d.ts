import { Entity } from '@loopback/repository';
import { User } from './user.model';
import { CrawledContent } from './crawled-content.model';
import { GeneratedDocument } from './generated-document.model';
export declare class CrawlJob extends Entity {
    id: string;
    url: string;
    status: string;
    maxDepth: number;
    maxPages: number;
    allowedContentTypes: string[];
    excludePatterns: string[];
    includePatterns: string[];
    followExternalLinks: boolean;
    respectRobotsTxt: boolean;
    delayBetweenRequests: number;
    crawlOptions: object;
    totalPages: number;
    processedPages: number;
    failedPages: number;
    progressPercentage: number;
    errorMessage?: string;
    crawlStatistics?: object;
    startedAt?: Date;
    completedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    crawledContents: CrawledContent[];
    generatedDocuments: GeneratedDocument[];
    constructor(data?: Partial<CrawlJob>);
}
export interface CrawlJobRelations {
    user?: User;
    crawledContents?: CrawledContent[];
    generatedDocuments?: GeneratedDocument[];
}
export type CrawlJobWithRelations = CrawlJob & CrawlJobRelations;
