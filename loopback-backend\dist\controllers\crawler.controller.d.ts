import { Filter, FilterExcludingWhere } from '@loopback/repository';
import { UserProfile } from '@loopback/security';
import { CrawlJob, CrawledContent } from '../models';
import { CrawlJobRepository, CrawledContentRepository } from '../repositories';
import { CrawlerService } from '../services';
export declare class CrawlerController {
    crawlJobRepository: CrawlJobRepository;
    crawledContentRepository: CrawledContentRepository;
    crawlerService: CrawlerService;
    constructor(crawlJobRepository: CrawlJobRepository, crawledContentRepository: CrawledContentRepository, crawlerService: CrawlerService);
    createCrawlJob(crawlJob: Omit<CrawlJob, 'id' | 'createdAt' | 'updatedAt' | 'userId'>, currentUser: UserProfile): Promise<CrawlJob>;
    findCrawlJobs(filter?: Filter<CrawlJob>, currentUser: UserProfile): Promise<CrawlJob[]>;
    findCrawlJobById(id: string, filter?: FilterExcludingWhere<CrawlJob>, currentUser: UserProfile): Promise<CrawlJob>;
    updateCrawlJob(id: string, crawlJob: Partial<CrawlJob>, currentUser: UserProfile): Promise<void>;
    deleteCrawlJob(id: string, currentUser: UserProfile): Promise<void>;
    startCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    stopCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    pauseCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    resumeCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    getCrawlJobProgress(id: string, currentUser: UserProfile): Promise<object>;
    getCrawledContent(id: string, filter?: Filter<CrawledContent>, currentUser: UserProfile): Promise<CrawledContent[]>;
    getCrawlJobStatistics(id: string, currentUser: UserProfile): Promise<object>;
}
