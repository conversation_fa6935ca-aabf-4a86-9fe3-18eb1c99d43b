import { Filter, FilterExcludingWhere } from '@loopback/repository';
import { UserProfile } from '@loopback/security';
import { CrawlJob, CrawledContent } from '../models';
import { CrawlJobRepository, CrawledContentRepository } from '../repositories';
import { CrawlerService } from '../services';
export declare class CrawlerController {
    crawlJobRepository: CrawlJobRepository;
    crawledContentRepository: CrawledContentRepository;
    crawlerService: CrawlerService;
    constructor(crawlJobRepository: CrawlJobRepository, crawledContentRepository: CrawledContentRepository, crawlerService: CrawlerService);
    createCrawlJob(crawlJob: Omit<CrawlJob, 'id' | 'createdAt' | 'updatedAt' | 'userId'>, currentUser: UserProfile): Promise<CrawlJob>;
    findCrawlJobs(currentUser: UserProfile, filter?: Filter<CrawlJob>): Promise<CrawlJob[]>;
    findCrawlJobById(id: string, currentUser: UserProfile, filter?: FilterExcludingWhere<CrawlJob>): Promise<CrawlJob>;
    updateCrawlJob(id: string, currentUser: UserProfile, crawlJob: Partial<CrawlJob>): Promise<void>;
    deleteCrawlJob(id: string, currentUser: UserProfile): Promise<void>;
    startCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    stopCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    pauseCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    resumeCrawlJob(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    getCrawlJobProgress(id: string, currentUser: UserProfile): Promise<object>;
    getCrawledContent(id: string, currentUser: UserProfile, filter?: Filter<CrawledContent>): Promise<CrawledContent[]>;
    getCrawlJobStatistics(id: string, currentUser: UserProfile): Promise<object>;
}
