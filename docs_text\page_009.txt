=== Page 10 ===

10This will create a controller in app/controllers directory that looks like this:
Simple enough, right? You'll notice we have a show method we were looking for. These
are called "controller methods" and are similiar to what Django calls a "view."
But also notice we now have our show method that we specified in our route earlier.
We can return a lot of different things in our controller but for now we can return a view
from our controller. A view in Masonite are html files or "templates". They are not Python
objects themselves like other Python frameworks. Views are what the users will see (or
view).
This is important as this is our first introduction to Masonite's IOC container. We specify
in our parameter list that we need a view class and Masonite will inject it for us.
For now on we won't focus on the whole controller but just the sections we are worried
about. A ... means there is code in between that we are not worried about:terminal
$ python craft controller Blog
app/http/controller/BlogController.py
from masonite.controllers import Controller
from masonite.views import View
class BlogController(Controller):
    def show(self, view: View):
        return view.render("")
app/controllers/BlogController.pyReturning a View6/12/25, 3:02 AM Masonite Documentation