=== Page 423 ===

423project under the app/http/middleware/CsrfMiddleware.py file.
Lastly, put that middleware into the HTTP_MIDDLEWARE list inside 
config/middleware.py like so:
There has been a slight change in the constants used in the config/database.py file.
Mainly just for consistency and coding standards. Your file may have some slight
changes but this change is optional. If you do make this change, be sure to change any
places in your code where you have used the Orator Query Builder. For example any place
you may have:
should now be:
with this changeHTTP_MIDDLEWARE = [
    'app.http.middleware.LoadUserMiddleware.LoadUserMiddleware',
    'app.http.middleware.CsrfMiddleware.CsrfMiddleware',
]
from config import database
database.db.table(...)
from config import database
database.DB.table(...)Changes to Database Configuration6/12/25, 3:02 AM Masonite Documentation