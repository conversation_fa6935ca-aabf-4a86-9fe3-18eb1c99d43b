=== Page 80 ===

80dot notation.
Take this for example:
this will render:
You can also make the config location a dictionary and use dot notation:
and use the dot notation like so:....
's3': {
  's3_client': 'sIS8shn...'
  ...
  'path': 'https://s3.us-east-2.amazonaws.com/bucket'
  },
....
...
<img src="{{ asset('s3', 'profile.jpg') }}" alt="profile" />
...
<img
  src="https://s3.us-east-2.amazonaws.com/bucket/profile.jpg"
  alt="profile"
/>
....
's3': {
  's3_client': 'sIS8shn...'
  ...
  'path': {
    'east': 'https://s3.us-east-2.amazonaws.com/east-bucket',
    'west': 'https://s3.us-west-16.amazonaws.com/west-bucket'
  },
....config/filesystem.py
config/filesystem.py6/12/25, 3:02 AM Masonite Documentation