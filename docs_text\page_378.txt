=== Page 379 ===

379Removed the need for the redirection provider completely. You need to remove this from
your PROVIDERS list.
Renamed Request.redirectTo to Request.redirect_to
Also removed the .send() method and moved the dictionary into a parameter:
Read more in the Requests documentation.
Added a new Request.only method to fetch only specific inputs needed.
Read more in Requests documentation.
Added a new Request.get_request_method() method to the Request class.
Read more in Requests documentation.def show(self):
    return request().redirect('/dashboard/@id', {'id': '5'})
Redirection Provider
Redirection
Request Only
Get Request Method6/12/25, 3:02 AM Masonite Documentation