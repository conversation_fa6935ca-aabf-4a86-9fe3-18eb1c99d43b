=== Page 206 ===

206Package Development
Creating packages is very simple for Masonite. You can create a package and publish it
on PyPi in less than 5 minutes. With Masonite packages you will scaffold and centralize
some features to reuse it all your Masonite projects with ease. Masonite comes with
several helper functions in order to create packages which can add configuration files,
routes, controllers, views, commands, migrations and more.
As a developer, you will be responsible for both making packages and consuming
packages. In this documentation we'll talk about both. We'll start by talking about how to
make a package and then talk about how to use that package or other third party
packages.
You can browse Masonite packages (official and community) on: 
packages.masoniteproject.com (alpha version).
Masonite, being a Python framework, you can obviously use all Python packages that
aren’t designed for a specific framework. For example, you can obviously use a library like
requests but you can’t use specific Django Rest Framework.
Package providers are the connection between your package and Masonite. A service
provider is responsible for binding things into Masonite container and specifying from
where to load package resources such as views, configuration, routes and assets.
Your Masonite project will discover packages through the PROVIDERS list defined in your
providers.py configuration file. When a package provider is added to this list, this will
allow additional bindings, commands, views, routes, migrations and assets to be
registered in your project.
Introduction
Package Discovery6/12/25, 3:02 AM Masonite Documentation