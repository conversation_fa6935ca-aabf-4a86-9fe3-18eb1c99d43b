=== Page 43 ===

43How To Contribute
There are plenty of ways to contribute to open source. Many of which don't even rely on
writing code. A great open source project should have excellent documentation, a thriving
community and have as little bugs as possible. Below I will explain how to contribute to
this project in different ways both including and exluding code contributions.
This is not an exhaustive list and not the only ways to contribute but they are the most
common. If you know of other ways to contribute then please let us know.
Of course the project requires contributions to the main development aspects but it's not
the only way. But if you would like to contribute to development then a great way to get
started is to simply read through this documentation. Get acquainted with how the
framework works, how Controllers and Routing work and read the Architectural Concepts
documentation starting with the Request Lifecycle, then the Service Providers and finally
the Service Container.
It would also be good to read about the Release Cycle to get familiar with how Masonite
does releases (SemVer and RomVer).
Feature Maintainers are people who are in charge of specific features (such as Caching
or Creating Packages). These developers will be in charge of reviewing PR's and merging
them into the development branch and also have direct contact with the repository owner
to discuss.Introduction
Contributing to Development
Become a Feature Maintainer6/12/25, 3:02 AM Masonite Documentation