=== Page 186 ===

186Sending attachments is really simply with Masonite. Simply attach the file to the mailable
before sending it:
You will then see your attachment in the email.
When you are building your emails it might be nice to see how they look before sending
them. This can save a lot of time when you're trying to get those styles just right.
You can simply return your mailable in your controller and it will return like a normal view
file.reply_to('<EMAIL>')Specifies the address that will be set if a user
clicks reply to this email
text('Welcome to Masonite') Specifies the text version of the email.
html('Welcome to Masonite') Specifies the HTML version of the email.
view('mailables.view', {})Specifies a view file with data to render the
HTML version of the email
priority(1)Specifies the priority of the email, values
should be 1 through 5.
low_priority() Sets the priortiy of the email to 5.
highpriority() Setsthepriortiyoftheemailto1
user = user.find(1)
welcome_mailable = WelcomeMailable().to(f"{user.name} <{user.email}>')
welcome_mailable.attach("MAY-2021-invoice.pdf", 
"storage/pdf/invoice.pdf")
mail.mailable(welcome_mailable).send()Sending Attachments
Mailable Responses6/12/25, 3:02 AM Masonite Documentation