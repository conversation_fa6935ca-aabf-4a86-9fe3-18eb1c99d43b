=== Page 204 ===

204Then you could scaffold this code into a new Masonite package so that community can
use it 😉  !
You can enable dry notifications to avoid notifications to be sent. It can be useful in some
cases (background task, production commands, development, testing...)
When fail_silently parameter is enabled, notifications sending will not raise
exceptions if an error occurs.
Channels defined in via() method of the notification can be overriden at send:user.notify(WelcomeNotification(), dry=True)
# or
notification.send(WelcomeNotification(), dry=True)
user.notify(WelcomeNotification(), fail_silently=True)
# or
notification.send(WelcomeNotification(), fail_silently=True)Advanced Usage
Dry run
Ignoring errors
Overriding channels6/12/25, 3:02 AM Masonite Documentation