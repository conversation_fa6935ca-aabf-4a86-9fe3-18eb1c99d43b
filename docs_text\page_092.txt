=== Page 93 ===

93Note that if you are using an @ symbol that should not be rendered with Masonite
then this will throw an error. An example being when you are using @media tags in
CSS. In this case you will need to wrap this statement inside `` and `
` blocks.
You can show variable or string text by using {{ }} characters:
If statements are similar to python but require an endif!
Line Statements:
Using alternative Jinja2 syntax:<p>
    {{ variable }}
</p>
<p>
    {{ 'hello world' }}
</p>
@if expression
    <p>do something</p>
@elif expression
    <p>do something else</p>
@else
    <p>above all are false</p>
@endifVariables
If statement6/12/25, 3:02 AM Masonite Documentation