import { Filter, FilterExcludingWhere } from '@loopback/repository';
import { UserProfile } from '@loopback/security';
import { GeneratedDocument } from '../models';
import { GeneratedDocumentRepository, CrawlJobRepository, CrawledContentRepository } from '../repositories';
import { DocumentGeneratorService, DocumentGenerationOptions } from '../services';
export declare class DocumentGeneratorController {
    generatedDocumentRepository: GeneratedDocumentRepository;
    crawlJobRepository: CrawlJobRepository;
    crawledContentRepository: CrawledContentRepository;
    documentGeneratorService: DocumentGeneratorService;
    constructor(generatedDocumentRepository: GeneratedDocumentRepository, crawlJobRepository: CrawlJobRepository, crawledContentRepository: CrawledContentRepository, documentGeneratorService: DocumentGeneratorService);
    generateDocument(options: DocumentGenerationOptions & {
        crawlJobId: string;
    }, currentUser: UserProfile): Promise<GeneratedDocument>;
    findGeneratedDocuments(currentUser: UserProfile, filter?: Filter<GeneratedDocument>): Promise<GeneratedDocument[]>;
    findGeneratedDocumentById(id: string, currentUser: UserProfile, filter?: FilterExcludingWhere<GeneratedDocument>): Promise<GeneratedDocument>;
    getGenerationProgress(id: string, currentUser: UserProfile): Promise<object>;
    cancelGeneration(id: string, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    downloadDocument(id: string, currentUser: UserProfile): Promise<object>;
    deleteGeneratedDocument(id: string, currentUser: UserProfile): Promise<void>;
    getUserStatistics(currentUser: UserProfile): Promise<object>;
    updateContentSelection(selectionData: {
        crawlJobId: string;
        contentIds: string[];
        isSelected: boolean;
        selectionGroup?: string;
    }, currentUser: UserProfile): Promise<{
        message: string;
    }>;
    getSelectedContent(crawlJobId: string, currentUser: UserProfile, selectionGroup?: string): Promise<object[]>;
}
