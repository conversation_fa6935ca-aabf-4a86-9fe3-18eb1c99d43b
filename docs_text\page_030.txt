=== Page 31 ===

31Great! You now have a blog that you can use to create, view, update and delete posts! Go
on to create amazing things!<form action="/post/{{ post.id }}/update" method="POST">
    {{ csrf_field }}
    <label for="">Title</label>
    <input type="text" name="title" value="{{ post.title }}"><br>
    <label>Body</label>
    <textarea name="body">{{ post.body }}</textarea><br>
    <input type="submit" value="Update">
    <a href="/post/{{ post.id }}/delete"> Delete </a>
</form>6/12/25, 3:02 AM Masonite Documentation