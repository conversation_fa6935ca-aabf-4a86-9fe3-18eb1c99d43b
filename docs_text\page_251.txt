=== Page 252 ===

252notice we called the method as if it was apart of the validator class this whole time.
Registering rules is especially useful when creating packages for Masonite that contain new
rules.
In addition to validating the request class we can also use the validator class directly.
This is useful if you need to validate your own dictionary:from masonite.validation import <PERSON>ida<PERSON>
def show(self, request: Request, validate: Validator):
    """
    Incoming Input: {
        'user': 'username123',
        'company': 'Masonite'
    }
    """
    valid = request.validate(
        validate.required(['user', 'company']),
        validate.equals_masonite('company')
    )
Using The Validator Class6/12/25, 3:02 AM Masonite Documentation