"""API Routes for Authentication, 2FA, and OAuth Endpoints with Rate Limiting"""

from masonite.routes import Route

ROUTES = [
    # CORS preflight OPTIONS routes for ALL endpoints    # Authentication endpoints    Route.options('/auth/signup', 'Cors<PERSON>ontroller@preflight').name('api.cors.auth.signup'),
    Route.options('/auth/login', 'CorsController@preflight').name('api.cors.auth.login'),
    Route.options('/auth/verify-otp', 'Cors<PERSON>ontroller@preflight').name('api.cors.auth.verify_otp'),
    Route.options('/auth/verify-email', 'CorsController@preflight').name('api.cors.auth.verify_email'),
    Route.options('/auth/forgot-password', 'Cors<PERSON>ontroller@preflight').name('api.cors.auth.forgot_password'),
    Route.options('/auth/reset-password', 'Cors<PERSON>ontroller@preflight').name('api.cors.auth.reset_password'),
    Route.options('/auth/resend-verification', 'CorsController@preflight').name('api.cors.auth.resend_verification'),
    Route.options('/auth/profile', 'CorsController@preflight').name('api.cors.auth.profile'),
    Route.options('/auth/me', 'CorsController@preflight').name('api.cors.auth.me'),
    Route.options('/auth/change-password', 'CorsController@preflight').name('api.cors.auth.change_password'),    Route.options('/auth/logout', 'CorsController@preflight').name('api.cors.auth.logout'),
    Route.options('/auth/refresh', 'CorsController@preflight').name('api.cors.auth.refresh'),
    Route.options('/auth/verify-token', 'CorsController@preflight').name('api.cors.auth.verify_token'),
    
    # Two-Factor Authentication endpoints
    Route.options('/2fa/setup', 'CorsController@preflight').name('api.cors.2fa.setup'),
    Route.options('/2fa/verify', 'CorsController@preflight').name('api.cors.2fa.verify'),
    Route.options('/2fa/disable', 'CorsController@preflight').name('api.cors.2fa.disable'),
    Route.options('/2fa/status', 'CorsController@preflight').name('api.cors.2fa.status'),
    Route.options('/2fa/recovery-codes', 'CorsController@preflight').name('api.cors.2fa.recovery_codes'),
    Route.options('/2fa/regenerate-codes', 'CorsController@preflight').name('api.cors.2fa.regenerate_codes'),
    Route.options('/2fa/send-sms', 'CorsController@preflight').name('api.cors.2fa.send_sms'),
    Route.options('/2fa/verify-sms', 'CorsController@preflight').name('api.cors.2fa.verify_sms'),
    Route.options('/2fa/send-email', 'CorsController@preflight').name('api.cors.2fa.send_email'),
    Route.options('/2fa/verify-email', 'CorsController@preflight').name('api.cors.2fa.verify_email'),
    
    # 2FA Disable Flow endpoints
    Route.options('/auth/request-disable-2fa', 'CorsController@preflight').name('api.cors.auth.request_disable_2fa'),
    Route.options('/auth/confirm-disable-2fa', 'CorsController@preflight').name('api.cors.auth.confirm_disable_2fa'),
    Route.options('/auth/disable-2fa-status/@email', 'CorsController@preflight').name('api.cors.auth.disable_2fa_status'),
    
    # OAuth Authentication endpoints
    Route.options('/auth/oauth/@provider/url', 'CorsController@preflight').name('api.cors.oauth.get_url'),
    Route.options('/auth/oauth/@provider/callback', 'CorsController@preflight').name('api.cors.oauth.callback'),
    Route.options('/auth/oauth/callback', 'CorsController@preflight').name('api.cors.oauth.redirect'),
    Route.options('/auth/oauth/exchange-token', 'CorsController@preflight').name('api.cors.oauth.exchange'),
    Route.options('/auth/oauth/providers', 'CorsController@preflight').name('api.cors.oauth.providers'),
      # Payment endpoints
    Route.options('/payments/create-order', 'CorsController@preflight').name('api.cors.payments.create_order'),
    Route.options('/payments/verify', 'CorsController@preflight').name('api.cors.payments.verify'),
    Route.options('/payments/status/@order_id', 'CorsController@preflight').name('api.cors.payments.status'),
    Route.options('/payments/my-payments', 'CorsController@preflight').name('api.cors.payments.my_payments'),
    Route.options('/payments/refund', 'CorsController@preflight').name('api.cors.payments.refund'),
    Route.options('/payments/analytics', 'CorsController@preflight').name('api.cors.payments.analytics'),
    Route.options('/payments/refunds', 'CorsController@preflight').name('api.cors.payments.refunds'),
    Route.options('/payments/cancel', 'CorsController@preflight').name('api.cors.payments.cancel'),
    Route.options('/payments/test', 'CorsController@preflight').name('api.cors.payments.test'),
    Route.options('/payments/my-payments', 'CorsController@preflight').name('api.cors.payments.my_payments'),
    Route.options('/payments/refund', 'CorsController@preflight').name('api.cors.payments.refund'),
    Route.options('/payments/analytics', 'CorsController@preflight').name('api.cors.payments.analytics'),
    Route.options('/payments/refunds', 'CorsController@preflight').name('api.cors.payments.refunds'),
    Route.options('/payments/cancel', 'CorsController@preflight').name('api.cors.payments.cancel'),
    Route.options('/payments/webhook', 'CorsController@preflight').name('api.cors.payments.webhook'),
    
    # Account Management endpoints
    Route.options('/account/request-deletion', 'CorsController@preflight').name('api.cors.account.request_deletion'),
    Route.options('/account/deletion-status', 'CorsController@preflight').name('api.cors.account.deletion_status'),
    Route.options('/account/cancel-deletion', 'CorsController@preflight').name('api.cors.account.cancel_deletion'),
    Route.options('/account/export-data', 'CorsController@preflight').name('api.cors.account.export_data'),
    Route.options('/account/request-export', 'CorsController@preflight').name('api.cors.account.request_export'),
    Route.options('/account/cleanup-expired', 'CorsController@preflight').name('api.cors.account.cleanup_expired'),
    Route.options('/account/confirm-deletion', 'CorsController@preflight').name('api.cors.account.confirm_deletion'),
    Route.options('/account/check-preserved-data', 'CorsController@preflight').name('api.cors.account.check_preserved_data'),
    Route.options('/account/check-preserved-data/@email', 'CorsController@preflight').name('api.cors.account.check_preserved_data_by_email'),
    Route.options('/account/restore-data', 'CorsController@preflight').name('api.cors.account.restore_data'),
    Route.options('/account/delete-preserved-data', 'CorsController@preflight').name('api.cors.account.delete_preserved_data'),
    
    # OTP endpoints
    Route.options('/otp/send', 'CorsController@preflight').name('api.cors.otp.send'),
    Route.options('/otp/send-email', 'CorsController@preflight').name('api.cors.otp.send_email'),
    Route.options('/otp/send-sms', 'CorsController@preflight').name('api.cors.otp.send_sms'),
    Route.options('/otp/verify', 'CorsController@preflight').name('api.cors.otp.verify'),
    Route.options('/otp/login', 'CorsController@preflight').name('api.cors.otp.login'),
    Route.options('/otp/status', 'CorsController@preflight').name('api.cors.otp.status'),
    Route.options('/otp/cleanup', 'CorsController@preflight').name('api.cors.otp.cleanup'),
    Route.options('/payments/test', 'CorsController@preflight').name('api.cors.payments.test'),
    
    # Account Management endpoints
    Route.options('/account/request-deletion', 'CorsController@preflight').name('api.cors.account.request_deletion'),
    Route.options('/account/deletion-status', 'CorsController@preflight').name('api.cors.account.deletion_status'),
    Route.options('/account/cancel-deletion', 'CorsController@preflight').name('api.cors.account.cancel_deletion'),
    Route.options('/account/export-data', 'CorsController@preflight').name('api.cors.account.export_data'),
    Route.options('/account/request-export', 'CorsController@preflight').name('api.cors.account.request_export'),
    Route.options('/account/cleanup-expired', 'CorsController@preflight').name('api.cors.account.cleanup_expired'),
    Route.options('/account/confirm-deletion', 'CorsController@preflight').name('api.cors.account.confirm_deletion'),
    Route.options('/account/check-preserved-data', 'CorsController@preflight').name('api.cors.account.check_preserved_data'),
    Route.options('/account/check-preserved-data/@email', 'CorsController@preflight').name('api.cors.account.check_preserved_data_by_email'),
    Route.options('/account/restore-data', 'CorsController@preflight').name('api.cors.account.restore_data'),
    
    # OTP endpoints
    Route.options('/otp/send', 'CorsController@preflight').name('api.cors.otp.send'),
    Route.options('/otp/send-email', 'CorsController@preflight').name('api.cors.otp.send_email'),
    Route.options('/otp/send-sms', 'CorsController@preflight').name('api.cors.otp.send_sms'),
    Route.options('/otp/verify', 'CorsController@preflight').name('api.cors.otp.verify'),
    Route.options('/otp/login', 'CorsController@preflight').name('api.cors.otp.login'),
    Route.options('/otp/status', 'CorsController@preflight').name('api.cors.otp.status'),
    Route.options('/otp/cleanup', 'CorsController@preflight').name('api.cors.otp.cleanup'),
    
    # Security endpoints
    Route.options('/security/dashboard', 'CorsController@preflight').name('api.cors.security.dashboard'),
    Route.options('/security/events', 'CorsController@preflight').name('api.cors.security.events'),
    Route.options('/security/events/user', 'CorsController@preflight').name('api.cors.security.user_events'),
    Route.options('/security/suspicious-activity', 'CorsController@preflight').name('api.cors.security.suspicious_activity'),
    Route.options('/security/analysis', 'CorsController@preflight').name('api.cors.security.analysis'),
    Route.options('/security/statistics', 'CorsController@preflight').name('api.cors.security.statistics'),
    Route.options('/security/account-status', 'CorsController@preflight').name('api.cors.security.account_status'),
    Route.options('/security/unlock-account', 'CorsController@preflight').name('api.cors.security.unlock_account'),
    Route.options('/security/events/@event_id/resolve', 'CorsController@preflight').name('api.cors.security.resolve_event'),
    Route.options('/security/cleanup', 'CorsController@preflight').name('api.cors.security.cleanup'),
    
    # Notification endpoints
    Route.options('/notifications', 'CorsController@preflight').name('api.cors.notifications.list'),
    Route.options('/notifications/@notification_id/read', 'CorsController@preflight').name('api.cors.notifications.mark_read'),
    Route.options('/notifications/test', 'CorsController@preflight').name('api.cors.notifications.test'),
    
    # Queue endpoints
    Route.options('/queue/status', 'CorsController@preflight').name('api.cors.queue.status'),
    Route.options('/queue/stats', 'CorsController@preflight').name('api.cors.queue.stats'),
    Route.options('/queue/failed-jobs', 'CorsController@preflight').name('api.cors.queue.failed_jobs'),
    Route.options('/queue/test-email', 'CorsController@preflight').name('api.cors.queue.test_email'),
    Route.options('/queue/test-security-processing', 'CorsController@preflight').name('api.cors.queue.test_security_processing'),
    Route.options('/queue/test-cleanup', 'CorsController@preflight').name('api.cors.queue.test_cleanup'),
    Route.options('/queue/cleanup', 'CorsController@preflight').name('api.cors.queue.cleanup'),
    Route.options('/queue/process-security-events', 'CorsController@preflight').name('api.cors.queue.process_security'),
      # Public authentication endpoints with reasonable rate limiting
    Route.post('/auth/login', 'AuthController@login').name('api.auth.login').middleware('throttle:100/minute'),
    Route.post('/auth/verify-otp', 'AuthController@verify_otp').name('api.auth.verify_otp').middleware('throttle:100/minute'),  # NEW endpoint for OTP verification
    Route.post('/auth/signup', 'AuthController@register').name('api.auth.signup').middleware('throttle:50/minute'),  # RENAMED from /register to /signup
    Route.post('/auth/verify-email', 'AuthController@verify_email').name('api.auth.verify_email').middleware('throttle:100/minute'),
    Route.post('/auth/forgot-password', 'AuthController@forgot_password').name('api.auth.forgot_password').middleware('throttle:50/minute'),
    Route.post('/auth/reset-password', 'AuthController@reset_password').name('api.auth.reset_password').middleware('throttle:100/minute'),
    Route.post('/auth/resend-verification', 'AuthController@resend_verification').name('api.auth.resend_verification').middleware('throttle:50/minute'),  # NEW endpoint

    # Protected authentication endpoints with moderate rate limiting
    Route.get('/auth/profile', 'AuthController@profile').name('api.auth.profile').middleware('auth', 'throttle:600/minute'),
    Route.get('/auth/me', 'AuthController@profile').name('api.auth.me').middleware('auth', 'throttle:600/minute'),  # NEW alias for /profile
    Route.patch('/auth/profile', 'AuthController@update_profile').name('api.auth.update_profile').middleware('auth', 'throttle:300/minute'),  # NEW endpoint
    Route.post('/auth/change-password', 'AuthController@change_password').name('api.auth.change_password').middleware('auth', 'throttle:100/minute'),  # NEW endpoint    Route.post('/auth/logout', 'AuthController@logout').name('api.auth.logout').middleware('auth', 'throttle:200/minute'),
    Route.post('/auth/refresh', 'AuthController@refresh').name('api.auth.refresh').middleware('auth', 'throttle:300/minute'),
    Route.post('/auth/verify-token', 'AuthController@verify_token').name('api.auth.verify_token').middleware('throttle:500/minute'),  # NEW endpoint for token validation
    
    # 2FA Disable Flow endpoints
    Route.post('/auth/request-disable-2fa', 'AuthController@request_disable_2fa').name('api.auth.request_disable_2fa').middleware('throttle:10/minute'),
    Route.post('/auth/confirm-disable-2fa', 'AuthController@confirm_disable_2fa').name('api.auth.confirm_disable_2fa').middleware('throttle:20/minute'),
    Route.get('/auth/disable-2fa-status/@email', 'AuthController@get_disable_2fa_status').name('api.auth.disable_2fa_status').middleware('throttle:30/minute'),
    
    # Two-Factor Authentication endpoints with security-focused rate limiting (RENAMED to match LoopBack /2fa/ pattern)
    Route.post('/2fa/setup', 'TwoFactorController@setup').name('api.2fa.setup').middleware('auth', 'throttle:50/minute'),
    Route.post('/2fa/verify', 'TwoFactorController@verify').name('api.2fa.verify').middleware('auth', 'throttle:100/minute'),
    Route.post('/2fa/disable', 'TwoFactorController@disable').name('api.2fa.disable').middleware('auth', 'throttle:50/minute'),
    Route.get('/2fa/status', 'TwoFactorController@status').name('api.2fa.status').middleware('auth', 'throttle:100/minute'),  # NEW endpoint
    Route.get('/2fa/recovery-codes', 'TwoFactorController@recovery_codes').name('api.2fa.recovery_codes').middleware('auth', 'throttle:100/minute'),
    Route.post('/2fa/regenerate-codes', 'TwoFactorController@regenerate_codes').name('api.2fa.regenerate_codes').middleware('auth', 'throttle:30/minute'),
    Route.post('/2fa/send-sms', 'TwoFactorController@send_sms').name('api.2fa.send_sms').middleware('auth', 'throttle:100/minute'),  # NEW endpoint
    Route.post('/2fa/verify-sms', 'TwoFactorController@verify_sms').name('api.2fa.verify_sms').middleware('auth', 'throttle:100/minute'),  # NEW endpoint
    Route.post('/2fa/send-email', 'TwoFactorController@send_email').name('api.2fa.send_email').middleware('auth', 'throttle:100/minute'),  # NEW endpoint
    Route.post('/2fa/verify-email', 'TwoFactorController@verify_email').name('api.2fa.verify_email').middleware('auth', 'throttle:100/minute'),  # NEW endpoint
    # OAuth Authentication endpoints with moderate rate limiting (RENAMED to match LoopBack /auth/oauth/ pattern)
    Route.get('/auth/oauth/@provider/url', 'OAuthController@get_oauth_url').name('api.auth.oauth.get_url').middleware('throttle:200/minute'),
    Route.post('/auth/oauth/@provider/callback', 'OAuthController@handle_oauth_callback').name('api.auth.oauth.callback').middleware('throttle:200/minute'),
    Route.get('/auth/oauth/callback', 'OAuthController@handle_oauth_redirect').name('api.auth.oauth.redirect'),  # No rate limit for provider redirects
    Route.post('/auth/oauth/exchange-token', 'OAuthController@exchange_authorization_code').name('api.auth.oauth.exchange').middleware('throttle:200/minute'),
    Route.get('/auth/oauth/providers', 'OAuthController@get_available_providers').name('api.auth.oauth.providers').middleware('throttle:30/minute'),
    
    # Payment endpoints with security-focused rate limiting
    Route.post('/payments/create-order', 'PaymentController@create_order').name('api.payments.create_order').middleware('auth', 'throttle:1000/minute'),
    Route.post('/payments/verify', 'PaymentController@verify_payment').name('api.payments.verify').middleware('auth', 'throttle:200/minute'),
    Route.get('/payments/status/@order_id', 'PaymentController@get_payment_status').name('api.payments.status').middleware('auth', 'throttle:300/minute'),
    Route.get('/payments/my-payments', 'PaymentController@get_user_payments').name('api.payments.my_payments').middleware('auth', 'throttle:300/minute'),  # RENAMED from /user to /my-payments
    Route.post('/payments/refund', 'PaymentController@refund_payment').name('api.payments.refund').middleware('auth', 'throttle:50/minute'),
    Route.get('/payments/analytics', 'PaymentController@get_payment_analytics').name('api.payments.analytics').middleware('auth', 'throttle:200/minute'),
    Route.get('/payments/refunds', 'PaymentController@get_refund_history').name('api.payments.refunds').middleware('auth', 'throttle:300/minute'),
    Route.post('/payments/cancel', 'PaymentController@cancel_payment').name('api.payments.cancel').middleware('auth', 'throttle:100/minute'),
    
    # Payment webhook (unauthenticated for Razorpay callbacks)
    Route.post('/payments/webhook', 'PaymentWebhookController@handle_webhook').name('api.payments.webhook').middleware('throttle:1000/minute'),
    
    # Test endpoint to verify PaymentController is working
    Route.get('/payments/test', 'PaymentController@test').name('api.payments.test'),

    # Account Management endpoints (authenticated)
    Route.post('/account/request-deletion', 'AccountController@request_deletion').name('api.account.request_deletion').middleware('auth', 'throttle:300/hour'),
    Route.get('/account/deletion-status', 'AccountController@deletion_status').name('api.account.deletion_status').middleware('auth', 'throttle:200/minute'),
    Route.post('/account/cancel-deletion', 'AccountController@cancel_deletion').name('api.account.cancel_deletion').middleware('auth', 'throttle:500/minute'),
    Route.get('/account/export-data', 'AccountController@export_data').name('api.account.export_data').middleware('auth', 'throttle:500/hour'),
    Route.post('/account/request-export', 'AccountController@request_export').name('api.account.request_export').middleware('auth', 'throttle:300/hour'),
    Route.post('/account/cleanup-expired', 'AccountController@cleanup_expired').name('api.account.cleanup_expired').middleware('throttle:1000/hour'),  # Admin endpoint

    # Account Management endpoints (public - no authentication required)
    Route.post('/account/confirm-deletion', 'AccountPublicController@confirm_deletion').name('api.account.confirm_deletion').middleware('throttle:1000/hour'),
    Route.post('/account/check-preserved-data', 'AccountPublicController@check_preserved_data').name('api.account.check_preserved_data').middleware('throttle:2000/hour'),
    Route.get('/account/check-preserved-data/@email', 'AccountPublicController@check_preserved_data_by_email').name('api.account.check_preserved_data_by_email').middleware('throttle:2000/hour'),
    Route.post('/account/restore-data', 'AccountPublicController@restore_data').name('api.account.restore_data').middleware('throttle:500/hour'),
    Route.delete('/account/delete-preserved-data', 'AccountPublicController@delete_preserved_data').name('api.account.delete_preserved_data').middleware('throttle:300/hour'),

    # OTP endpoints (public - no authentication required)
    Route.post('/otp/send', 'OTPController@send_otp').name('api.otp.send').middleware('throttle:500/minute'),
    Route.post('/otp/send-email', 'OTPController@send_email_otp').name('api.otp.send_email').middleware('throttle:500/minute'),
    Route.post('/otp/send-sms', 'OTPController@send_sms_otp').name('api.otp.send_sms').middleware('throttle:300/minute'),
    Route.post('/otp/verify', 'OTPController@verify_otp').name('api.otp.verify').middleware('throttle:100/minute'),
    Route.post('/otp/login', 'OTPController@login_with_otp').name('api.otp.login').middleware('throttle:500/minute'),
    Route.get('/otp/status', 'OTPController@get_otp_status').name('api.otp.status').middleware('throttle:200/minute'),
    Route.post('/otp/cleanup', 'OTPController@cleanup_otps').name('api.otp.cleanup').middleware('throttle:1000/hour'),  # Admin endpoint

    # Security endpoints (authentication required)
    Route.get('/security/dashboard', 'SecurityController@get_security_dashboard').name('api.security.dashboard').middleware('auth', 'throttle:300/minute'),
    Route.get('/security/events', 'SecurityController@get_user_security_events').name('api.security.events').middleware('auth', 'throttle:200/minute'),
    Route.get('/security/events/user', 'SecurityController@get_user_security_events').name('api.security.user_events').middleware('auth', 'throttle:200/minute'),
    Route.get('/security/suspicious-activity', 'SecurityController@get_suspicious_activity').name('api.security.suspicious_activity').middleware('auth', 'throttle:200/minute'),
    Route.get('/security/analysis', 'SecurityController@get_security_analysis').name('api.security.analysis').middleware('auth', 'throttle:200/minute'),
    Route.get('/security/statistics', 'SecurityController@get_security_statistics').name('api.security.statistics').middleware('auth', 'throttle:200/minute'),
    Route.get('/security/account-status', 'SecurityController@check_account_status').name('api.security.account_status').middleware('auth', 'throttle:100/minute'),

    # Admin security endpoints (authentication required)
    Route.post('/security/unlock-account', 'SecurityController@unlock_account').name('api.security.unlock_account').middleware('auth', 'throttle:500/hour'),
    Route.post('/security/events/@event_id/resolve', 'SecurityController@resolve_security_event').name('api.security.resolve_event').middleware('auth', 'throttle:2000/hour'),
    Route.post('/security/cleanup', 'SecurityController@cleanup_security_data').name('api.security.cleanup').middleware('auth', 'throttle:200/hour'),

    # Notification endpoints (authentication required)
    Route.get('/notifications', 'NotificationController@get_user_notifications').name('api.notifications.list').middleware('auth', 'throttle:300/minute'),
    Route.post('/notifications/@notification_id/read', 'NotificationController@mark_notification_read').name('api.notifications.mark_read').middleware('auth', 'throttle:600/minute'),
    Route.post('/notifications/test', 'NotificationController@send_test_notification').name('api.notifications.test').middleware('auth', 'throttle:500/hour'),

    # Queue endpoints (authentication required)
    Route.get('/queue/status', 'QueueController@get_queue_status').name('api.queue.status').middleware('auth', 'throttle:200/minute'),
    Route.get('/queue/stats', 'QueueController@get_queue_stats').name('api.queue.stats').middleware('auth', 'throttle:200/minute'),
    Route.get('/queue/failed-jobs', 'QueueController@get_failed_jobs').name('api.queue.failed_jobs').middleware('auth', 'throttle:200/minute'),
    Route.post('/queue/test-email', 'QueueController@queue_test_email').name('api.queue.test_email').middleware('auth', 'throttle:500/hour'),
    Route.post('/queue/test-security-processing', 'QueueController@test_security_processing').name('api.queue.test_security_processing').middleware('auth', 'throttle:500/hour'),
    Route.post('/queue/test-cleanup', 'QueueController@test_cleanup').name('api.queue.test_cleanup').middleware('auth', 'throttle:500/hour'),
    Route.post('/queue/cleanup', 'QueueController@trigger_data_cleanup').name('api.queue.cleanup').middleware('auth', 'throttle:300/hour'),
    Route.post('/queue/process-security-events', 'QueueController@process_security_events').name('api.queue.process_security').middleware('auth', 'throttle:1000/hour'),

    # Debug/Test endpoints (remove in production)
    Route.post('/debug/test-email', 'EmailTestController@test_otp_email').name('api.debug.test_email').middleware('throttle:10/minute'),
    Route.options('/debug/test-email', 'CorsController@preflight').name('api.cors.debug.test_email'),

    # Crawl endpoints
    Route.post('/crawl', 'CrawlController@crawl').name('api.crawl'),
    Route.options('/crawl', 'CorsController@preflight').name('api.cors.crawl'),
]
