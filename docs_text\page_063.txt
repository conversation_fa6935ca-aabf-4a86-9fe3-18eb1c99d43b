=== Page 64 ===

64Y<PERSON> can pass parameters from your routes to your middleware in cases where a
middleware should act differently depending on your route.
You can do this with a : symbol next to your route middleware name and then pass in
those parameters to the before and after middleware methods.
For example, we may be creating a middleware for request throttling and in our routes we
have something like this:
notice the throttle:2,100 syntax. The 2 and the 100 will then be passed into the before and
after methods of your middleware:
Similiar to the way we can pass values to a middleware using the : splice we can also
use the @ in the value to pass the value of the parameter.
For example, we may create a route and a middleware like this
If we go to a route like /dashboard/152/settings then the value of 152 will be passed
to the middleware before and after methods.Get('/feeds', 'FeedController').middleware('throttle:2,100')
class ThrottleMiddleware:
    def before(self, request, response, minutes, requests):
        # throttle requests
    def after(self, request, response, 
             minutes, requests):
        # throttle requests
Get('/dashboard/@user_id/settings', 
'FeedController').middleware('permission:@user_id')Request Parameters6/12/25, 3:02 AM Masonite Documentation