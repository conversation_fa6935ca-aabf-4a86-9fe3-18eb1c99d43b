=== Page 82 ===

82Views
Views in Masonite are a great way to return HTML in your controllers. Views have a
hierarchy, lots of helpers and logical controls, and a great way to separate out your
business logic from your presentation logic.
By default, all templates for your views are located in the templates directory. To create
a template, simply create a .html file inside this directory which we will reference to
later.
Then inside your controller file you can reference this template:
The first argument here is the name of your template and the second argument should be
a dictionary of variables you reference inside your template.
If you have a template inside another directory you can use dot notation to reference
those template names<!-- Template in templates/welcome.html -->
<html>
    <body>
        <h1>Hello, {{ name }}</h1>
    </body>
</html>
from masonite.views import View
class WelcomeController(Controller):
  def show(self, view: View):
    view.render('welcome', {
    "name": "<PERSON>"
    })Getting Started6/12/25, 3:02 AM Masonite Documentation