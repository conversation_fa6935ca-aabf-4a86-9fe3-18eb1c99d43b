=== Page 142 ===

142This will emit events on the presence-channel_name channel.
Adding the authentication route is the same as for Private channels.
Authorizing channels is the same as for Private channels.
To get started more easily with event broadcasting in Masonite, two small examples are
available here:
•Sending public app releases notification to every users (using Public channels)
•Sending private alerts to admin users (using Private channels)
To do this we need to create a NewRelease Broadcast event and trigger this event from
the backend.from masonite.broadcasting import CanBroadcast
from masonite.broadcasting import PresenceChannel
class UserAdded(CanBroadcast):
    def broadcast_on(self):
      return PresenceChannel("channel_name")
Routing
Authorizing
Examples
Sending public app releases notification6/12/25, 3:02 AM Masonite Documentation