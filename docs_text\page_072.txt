=== Page 73 ===

73Although both the request and response classes have headers and cookies, in most
instances, when fetching cookies and headers, it should be fetched from the Request
class. When setting headers and cookies it should be done on the response class.
To get a request header you can do so simply:
You can also set a header on the request class:
Note that setting headers on the request will NOT return the header as part of the
response.
You can get the current request URI:from masonite.request import Request
#..
def show(self, request: Request):
  request.header('X-Custom')
from masonite.request import Request
#..
def show(self, request: Request):
  request.header('X-Custom', 'value')
from masonite.request import Request
#..
def show(self, request: Request):
  request.get_path() #== /dashboard/1Headers
Paths6/12/25, 3:02 AM Masonite Documentation