=== Page 275 ===

275This will produce an error because age it is looking to make sure age is not in the list
now.
Used to make sure a given value is actually a JSON object
Used to make sure a string is of a certain length
This is used to make sure a value is less than a specific value"""
{
  'user': 1,
  'payload': '[{"email": "<EMAIL>"}]'
}
"""
validate.json('payload')
"""
{
  'user': 1,
  'description': 'this is a long description'
}
"""
validate.length('description', min=5, max=35)
"""
{
  'age': 25
}
"""
validate.less_than('age', 18)Json
Length
Less_than6/12/25, 3:02 AM Masonite Documentation