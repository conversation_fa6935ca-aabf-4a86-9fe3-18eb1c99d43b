=== Page 333 ===

333Masonite provides several database assertions that can be used during testing.
•assertDatabaseCount
•assertDatabaseHas
•assertDatabaseMissing
•assertDeleted
•assertSoftDeleted
Assert that a table in the database contains the given number of records.# config/database.py
DATABASES = {
    "default": "mysql",
    "mysql": {
        "host": "localhost",
        "driver": "mysql",
        "database": "app",
        "user": "root",
        "password": "",
        "port": 3306
    }
    "testing": {
        "driver": "sqlite",
        "database": "test_database.sqlite3",
    },
}
self.assertDatabaseCount(table, count)
  def test_can_create_user(self):
      User.create({"name": "john", "email": "john6", "password": 
"secret"})
      self.assertDatabaseCount("users", 1)Available Assertions
assertDatabaseCount6/12/25, 3:02 AM Masonite Documentation