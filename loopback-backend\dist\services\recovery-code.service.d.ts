import { UserRepository } from '../repositories';
/**
 * Modular Recovery Code Service
 * Handles secure generation, storage, and validation of 2FA backup codes
 *
 * Features:
 * - Secure code generation with cryptographic randomness
 * - Hashed storage (never store plaintext codes)
 * - Single-use codes with automatic invalidation
 * - Uses individual fields instead of arrays for PostgreSQL compatibility
 */
export declare class RecoveryCodeService {
    private userRepository;
    private readonly SALT_ROUNDS;
    private readonly CODE_LENGTH;
    private readonly TOTAL_CODES;
    private readonly MAX_ATTEMPTS_PER_HOUR;
    constructor(userRepository: UserRepository);
    /**
     * Generate new recovery codes for a user
     * @param userId - User ID to generate codes for
     * @returns Array of plaintext codes (only time they're in plaintext)
     */
    generateRecoveryCodes(userId: string): Promise<string[]>;
    /**
     * Validate and use a recovery code
     * @param userId - User ID
     * @param inputCode - Code provided by user
     * @returns True if code is valid and has been consumed
     */
    validateAndConsumeRecoveryCode(userId: string, inputCode: string): Promise<boolean>;
    /**
     * Clear all recovery codes for a user
     */
    clearRecoveryCodes(userId: string): Promise<void>;
    /**
     * Check if all recovery codes have been used
     * @param userId - User ID to check
     * @returns true if all codes are used (no remaining codes)
     */
    areAllCodesUsed(userId: string): Promise<boolean>;
    /**
     * Generate a cryptographically secure recovery code
     */
    private generateSecureCode;
}
