=== Page 194 ===

194.to()The channel you want to
broadcast to. If the value you
supply starts with a # sign
then Notifications will make a
POST request with your token
to the Slack channel list API
and get the channel ID. You
can specify the channel ID
directly if you don't want this
behavior.to('#general')
.to('CHSUU862')
.send_from()The username you want to
show as the message sender.
You can also specify either
the url or icon that will
be displayed as the sender..send_from('Masonite Bot',
icon="👻")
.as_current_user()This sets a boolean value to
True on whether the message
should show as the currently
authenticated user..as_current_user()
.link_names()This enables finding and
linking channel names and
usernames in message..link_names()
.can_reply()This auhtorizes replying back
to the message..can_reply()
.without_markdown()This will not parse any
markdown in the message.
This is a boolean value and
requires no parameters..without_markdown()
.unfurl_links()This enable showing
message attachments and
links previews. Usually slack
will show an icon of the
website when posting a link.
This enables that feature for
the current message..unfurl_links()
Used to post the current
message as a snippet instead
of as a normal message. This
option has 3 keyword6/12/25, 3:02 AM Masonite Documentation