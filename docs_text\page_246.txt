=== Page 247 ===

247In this case you can create a new rule.
You can easily create a new rule boiler plate by running:
There is no particular reason that rules are lowercase class names. The main reason is that
it improves readability when you end up using it as a method if you choose to register the
rule with the validation class like you will see below.
This will create a boiler plate rule inside app/rules/equals_masonite.py that looks like:terminal
$ python craft rule equals_masonite
Rule Command6/12/25, 3:02 AM Masonite Documentation