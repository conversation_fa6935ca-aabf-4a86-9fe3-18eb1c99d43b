=== Page 241 ===

241Tinker Shell (REPL)
Masonite Tinker is a powerful REPL (Read, Evaluate, Print and Loop) environment for the
Masonite framework. It's a supercharged Python interactive shell with access to the
container, models and helpers.
Tinker allows you to interact with your entire Masonite project on the command line,
including models, jobs, events, and more. To enter the Tinker environment, run the tinker
command:
This will open a Python shell with the application container (under the app variable), the
application models and some helpers imported for you.
Finally you can get an enhanced experience by using the Tinker IPython shell. IPython is
an improved Python shell offering some interesting features:
•Syntax highlighting
•Tab completion of python variables and keywords, filenames and function keywords
•Input history, persistent across sessions
•Integrated access to the pdb debugger and the Python profiler
•and much more...
You just need to use -i option and install IPython if not installed yet (pip install 
IPython):python craft tinker
python craft tinker -i
Configuration
Auto-loading Models6/12/25, 3:02 AM Masonite Documentation