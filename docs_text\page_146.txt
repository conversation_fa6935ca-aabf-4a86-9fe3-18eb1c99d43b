=== Page 147 ===

147Caching
Masonite provides a powerful caching feature to keep any data cached that may be
needless or expensive to fetch on every request. Masonite caching allows you to save
and fetch data, set expiration times and manage different cache stores.
Masonite supports the following cache drivers: Redis, Memcached and a basic File driver.
We'll walk through how to configure and use cache in this documentation.
Cache configuration is located at config/cache.py file. In this file, you can specify
different cache stores with a name via the STORES dictionary and the default to use in
your application with the default key.
Masonite is configured to use the File cache driver by default, named local.
For production applications, it is recommended to use a more efficient driver such as 
Memcached or Redis.STORES = {
    "default": "local",
    "local": {
        "driver": "file",
        "location": "storage/framework/cache"
    },
    "redis": {
        "driver": "redis",
        "host": "127.0.0.1",
        "port": "6379",
        "password": "",
        "name": "masonite4",
    },
    # ...
}
Configuration
config/cache.py6/12/25, 3:02 AM Masonite Documentation