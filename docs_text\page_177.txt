=== Page 178 ===

178When using the template helper, you may also use the hashid feature for request params:
If you have a route like this:
Inside your controller you are able to get the unhashed request parameter:Route.get('/user/@user_id/updates', 'Controller@method')
<!-- user.id == 10 -->
<form action="/user/{{ user.id }}/update" method="POST">
</form>
def store(self, request: Request):
  request.param('user_id') #== 10Route Parameters6/12/25, 3:02 AM Masonite Documentation