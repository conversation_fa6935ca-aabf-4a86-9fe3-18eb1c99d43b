=== Page 46 ===

46If you have a large following on any social media or no following at all, you can contribute
by trying to build up a following around Masonite. Any open source project requires an
amazing community around the framework. You can either build up a community
personally and be the leader of that community or you can simply send them to
Masonite's GitHub repository where we can build up a community around there.
Another idea is to use Masonite to build applications such as a screencast website like 
LaraCasts.com or an official Masonite website or even a social network around
Masonite. Every great framework needs it's "ecosystem" so you may be apart of that by
building these applications with the Masonite branding and logos. Although copying the
branding requires an OK from Joseph <PERSON>, as long as the website was built with
Masonite and looks clean it shouldn't be a problem at all.
Questions will come in eventually either through the GitHub issues or through websites
like StackOverflow. You could make it a priority to be the first to answer these peoples
questions or if you don't know the answer you can redirect one of the core maintainers or
contributors to the question so we can answer it further.
Most pull requests will sit inside GitHub for a few days while it gets quality tested. The
main develop branch pull requests could sit there for as long as 6 months and will only
be merged in on releases. With that being said, you can look at the file changes of these
pull requests and ensure they meet the community guidelines, the API is similar to other
aspects of the project and that they are being respectful and following pull requests rules
in accordance with the Contributing Guide documentation.
Build Community Software Around Masonite
Answer Questions from the Community
Review Code on Pull Requests6/12/25, 3:02 AM Masonite Documentation