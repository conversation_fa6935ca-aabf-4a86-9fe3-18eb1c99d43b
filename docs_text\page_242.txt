=== Page 243 ===

243Validation
There are a lot of times when you need to validate incoming input either from a form or
from an incoming json request. It is wise to have some form of backend validation as it
will allow you to build more secure applications. Masonite provides an extremely flexible
and fluent way to validate this data.
Validations are based on rules where you can pass in a key or a list of keys to the rule.
The validation will then use all the rules and apply them to the dictionary to validate.
You can see a list of available rules here.
Incoming form or JSON data can be validated very simply. All you need to do is import
the Validator class, resolve it, and use the necessary rule methods.
This whole snippet will look like this in your controller method:
Validating The Request6/12/25, 3:02 AM Masonite Documentation