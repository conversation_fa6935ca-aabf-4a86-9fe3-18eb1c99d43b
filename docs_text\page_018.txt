=== Page 19 ===

19Because of how Masonite does models, some models may rely on each other so it is
typically better to perform the import inside the relationship like we did above to
prevent any possibilities of circular imports.
We won't go into much more detail here about different types of relationships but to learn
more, refer to Masonite ORM Relationships documentation.
Let's setup a little HTML so we can learn a bit more about how views work. In this part we
will setup a really basic template in order to not clog up this part with too much HTML but
we will learn the basics enough that you can move forward and create a really awesome
blog template (or collect one from the internet).
Now that we have all the models and migrations setup, we have everything in the
backend that we need to create a layout and start creating and updating blog posts.
We will also check if the user is logged in before creating a template.
The URL for creating will be located at /blog/create and will be a simple form for
creating a blog post"""Post Model."""
from masoniteorm.models import Model
from masoniteorm.relationships import belongs_to
class Post(Model):
    __fillable__ = ['title', 'author_id', 'body']
    @belongs_to('author_id', 'id')
    def author(self):
        from app.models.User import User
        return User
Designing Our Blog
The Template For Creating6/12/25, 3:02 AM Masonite Documentation