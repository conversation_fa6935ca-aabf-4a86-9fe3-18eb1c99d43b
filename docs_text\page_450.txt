=== Page 451 ===

451Y<PERSON>'ll need to move this into the parameter list so it can be resolved:
There should be quite a bit of these in your application if you have used this class or you
have used the built in craft auth scaffold command.
Here is an example application that is being upgraded from 2.1 to 2.2 GitHub Repo
Impact: MEDIUM
The behavior for resolving classes has now been changed. If you bind a class into the
container like this:
It previously would have resolved and gave back the class:from masonite.auth import Auth
...
def show(self, request: Request):
    Auth(request).user()
from masonite.auth import Auth
...
def show(self, auth: Auth):
    auth.user()
from some.place import SomeClass
class SomeProvider:
    def register(self): 
        self.app.bind('SomeClass', SomeClass)
from some.place import SomeClass
def show(self, request: Request, some: SomeClass):
    some #== <class some.place.SomeClass>
    setup_class = some(request)Resolving Classes6/12/25, 3:02 AM Masonite Documentation