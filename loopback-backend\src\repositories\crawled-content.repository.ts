import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DbDataSource} from '../datasources';
import {CrawledContent, CrawledContentRelations, CrawlJob} from '../models';
import {CrawlJobRepository} from './crawl-job.repository';

export class CrawledContentRepository extends DefaultCrudRepository<
  CrawledContent,
  typeof CrawledContent.prototype.id,
  CrawledContentRelations
> {
  public readonly crawlJob: BelongsToAccessor<CrawlJob, typeof CrawledContent.prototype.id>;

  constructor(
    @inject('datasources.db') dataSource: DbDataSource,
    @repository.getter('CrawlJobRepository') protected crawlJobRepositoryGetter: Getter<CrawlJobRepository>,
  ) {
    super(CrawledContent, dataSource);
    
    this.crawlJob = this.createBelongsToAccessorFor('crawlJob', crawlJobRepositoryGetter);
    this.registerInclusionResolver('crawlJob', this.crawlJob.inclusionResolver);
  }

  /**
   * Find content by crawl job ID
   */
  async findByCrawlJobId(crawlJobId: string): Promise<CrawledContent[]> {
    return this.find({
      where: {crawlJobId},
      order: ['createdAt DESC'],
    });
  }

  /**
   * Find content by URL
   */
  async findByUrl(url: string): Promise<CrawledContent[]> {
    return this.find({
      where: {url},
      order: ['createdAt DESC'],
    });
  }

  /**
   * Find content by status
   */
  async findByStatus(status: string, crawlJobId?: string): Promise<CrawledContent[]> {
    const where: any = {status};
    if (crawlJobId) {
      where.crawlJobId = crawlJobId;
    }
    
    return this.find({
      where,
      order: ['createdAt DESC'],
    });
  }

  /**
   * Find selected content for document generation
   */
  async findSelectedContent(crawlJobId: string, selectionGroup?: string): Promise<CrawledContent[]> {
    const where: any = {
      crawlJobId,
      isSelected: true,
    };
    
    if (selectionGroup) {
      where.selectionGroup = selectionGroup;
    }
    
    return this.find({
      where,
      order: ['depth ASC', 'createdAt ASC'],
    });
  }

  /**
   * Update content selection
   */
  async updateSelection(
    contentIds: string[],
    isSelected: boolean,
    selectionGroup?: string,
  ): Promise<void> {
    const updateData: Partial<CrawledContent> = {
      isSelected,
      updatedAt: new Date(),
    };
    
    if (selectionGroup) {
      updateData.selectionGroup = selectionGroup;
    }
    
    for (const id of contentIds) {
      await this.updateById(id, updateData);
    }
  }

  /**
   * Get content statistics for a crawl job
   */
  async getCrawlJobContentStatistics(crawlJobId: string): Promise<object> {
    const contents = await this.find({where: {crawlJobId}});
    
    const stats = {
      totalContent: contents.length,
      completedContent: contents.filter(c => c.status === 'completed').length,
      failedContent: contents.filter(c => c.status === 'failed').length,
      pendingContent: contents.filter(c => c.status === 'pending').length,
      selectedContent: contents.filter(c => c.isSelected).length,
      totalContentLength: contents.reduce((sum, c) => sum + c.contentLength, 0),
      contentByType: this.groupContentByType(contents),
      contentByDepth: this.groupContentByDepth(contents),
    };

    return stats;
  }

  /**
   * Group content by content type
   */
  private groupContentByType(contents: CrawledContent[]): object {
    const grouped: {[key: string]: number} = {};
    
    for (const content of contents) {
      const type = content.contentType || 'unknown';
      grouped[type] = (grouped[type] || 0) + 1;
    }
    
    return grouped;
  }

  /**
   * Group content by depth
   */
  private groupContentByDepth(contents: CrawledContent[]): object {
    const grouped: {[key: number]: number} = {};
    
    for (const content of contents) {
      const depth = content.depth || 0;
      grouped[depth] = (grouped[depth] || 0) + 1;
    }
    
    return grouped;
  }

  /**
   * Search content by text
   */
  async searchContent(
    crawlJobId: string,
    searchTerm: string,
    limit: number = 50,
  ): Promise<CrawledContent[]> {
    // Note: This is a basic text search. For production, consider using full-text search
    return this.find({
      where: {
        crawlJobId,
        or: [
          {title: {like: `%${searchTerm}%`}},
          {content: {like: `%${searchTerm}%`}},
          {url: {like: `%${searchTerm}%`}},
        ],
      },
      limit,
      order: ['createdAt DESC'],
    });
  }

  /**
   * Get content by depth range
   */
  async findByDepthRange(
    crawlJobId: string,
    minDepth: number,
    maxDepth: number,
  ): Promise<CrawledContent[]> {
    return this.find({
      where: {
        crawlJobId,
        depth: {between: [minDepth, maxDepth]},
      },
      order: ['depth ASC', 'createdAt ASC'],
    });
  }

  /**
   * Clean up content files for deleted crawl jobs
   */
  async cleanupOrphanedContent(): Promise<number> {
    // This would typically involve file system cleanup
    // For now, just return count of orphaned records
    const orphanedContent = await this.find({
      where: {
        crawlJobId: {exists: false},
      },
    });

    if (orphanedContent.length > 0) {
      await this.deleteAll({
        crawlJobId: {exists: false},
      });
    }

    return orphanedContent.length;
  }
}
