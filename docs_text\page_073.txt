=== Page 74 ===

74You can get the current request method:
You can check if the current path contains a glob style schema:
You can get the current subdomain from the host:
You can get the current host:from masonite.request import Request
#..
def show(self, request: Request):
  request.get_request_method() #== PUT
from masonite.request import Request
#..
def show(self, request: Request):
  # URI: /dashboard/1/users
  request.contains("/dashboard/*/users") #== True
from masonite.request import Request
#..
def show(self, request: Request):
  # URI: work.example.com
  request.get_subdomain() #== work
from masonite.request import Request
#..
def show(self, request: Request):
  # URI: work.example.com
  request.get_host() #== example.com
Inputs6/12/25, 3:02 AM Masonite Documentation