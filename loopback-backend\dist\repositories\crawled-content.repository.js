"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawledContentRepository = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const datasources_1 = require("../datasources");
const models_1 = require("../models");
let CrawledContentRepository = class CrawledContentRepository extends repository_1.DefaultCrudRepository {
    constructor(dataSource, crawlJobRepositoryGetter) {
        super(models_1.CrawledContent, dataSource);
        this.crawlJobRepositoryGetter = crawlJobRepositoryGetter;
        this.crawlJob = this.createBelongsToAccessorFor('crawlJob', crawlJobRepositoryGetter);
        this.registerInclusionResolver('crawlJob', this.crawlJob.inclusionResolver);
    }
    /**
     * Find content by crawl job ID
     */
    async findByCrawlJobId(crawlJobId) {
        return this.find({
            where: { crawlJobId },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find content by URL
     */
    async findByUrl(url) {
        return this.find({
            where: { url },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find content by status
     */
    async findByStatus(status, crawlJobId) {
        const where = { status };
        if (crawlJobId) {
            where.crawlJobId = crawlJobId;
        }
        return this.find({
            where,
            order: ['createdAt DESC'],
        });
    }
    /**
     * Find selected content for document generation
     */
    async findSelectedContent(crawlJobId, selectionGroup) {
        const where = {
            crawlJobId,
            isSelected: true,
        };
        if (selectionGroup) {
            where.selectionGroup = selectionGroup;
        }
        return this.find({
            where,
            order: ['depth ASC', 'createdAt ASC'],
        });
    }
    /**
     * Update content selection
     */
    async updateSelection(contentIds, isSelected, selectionGroup) {
        const updateData = {
            isSelected,
            updatedAt: new Date(),
        };
        if (selectionGroup) {
            updateData.selectionGroup = selectionGroup;
        }
        for (const id of contentIds) {
            await this.updateById(id, updateData);
        }
    }
    /**
     * Get content statistics for a crawl job
     */
    async getCrawlJobContentStatistics(crawlJobId) {
        const contents = await this.find({ where: { crawlJobId } });
        const stats = {
            totalContent: contents.length,
            completedContent: contents.filter(c => c.status === 'completed').length,
            failedContent: contents.filter(c => c.status === 'failed').length,
            pendingContent: contents.filter(c => c.status === 'pending').length,
            selectedContent: contents.filter(c => c.isSelected).length,
            totalContentLength: contents.reduce((sum, c) => sum + c.contentLength, 0),
            contentByType: this.groupContentByType(contents),
            contentByDepth: this.groupContentByDepth(contents),
        };
        return stats;
    }
    /**
     * Group content by content type
     */
    groupContentByType(contents) {
        const grouped = {};
        for (const content of contents) {
            const type = content.contentType || 'unknown';
            grouped[type] = (grouped[type] || 0) + 1;
        }
        return grouped;
    }
    /**
     * Group content by depth
     */
    groupContentByDepth(contents) {
        const grouped = {};
        for (const content of contents) {
            const depth = content.depth || 0;
            grouped[depth] = (grouped[depth] || 0) + 1;
        }
        return grouped;
    }
    /**
     * Search content by text
     */
    async searchContent(crawlJobId, searchTerm, limit = 50) {
        // Note: This is a basic text search. For production, consider using full-text search
        return this.find({
            where: {
                crawlJobId,
                or: [
                    { title: { like: `%${searchTerm}%` } },
                    { content: { like: `%${searchTerm}%` } },
                    { url: { like: `%${searchTerm}%` } },
                ],
            },
            limit,
            order: ['createdAt DESC'],
        });
    }
    /**
     * Get content by depth range
     */
    async findByDepthRange(crawlJobId, minDepth, maxDepth) {
        return this.find({
            where: {
                crawlJobId,
                depth: { between: [minDepth, maxDepth] },
            },
            order: ['depth ASC', 'createdAt ASC'],
        });
    }
    /**
     * Clean up content files for deleted crawl jobs
     */
    async cleanupOrphanedContent() {
        // This would typically involve file system cleanup
        // For now, just return count of orphaned records
        const orphanedContent = await this.find({
            where: {
                crawlJobId: { exists: false },
            },
        });
        if (orphanedContent.length > 0) {
            await this.deleteAll({
                crawlJobId: { exists: false },
            });
        }
        return orphanedContent.length;
    }
};
exports.CrawledContentRepository = CrawledContentRepository;
exports.CrawledContentRepository = CrawledContentRepository = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('datasources.db')),
    tslib_1.__param(1, repository_1.repository.getter('CrawlJobRepository')),
    tslib_1.__metadata("design:paramtypes", [datasources_1.DbDataSource, Function])
], CrawledContentRepository);
//# sourceMappingURL=crawled-content.repository.js.map