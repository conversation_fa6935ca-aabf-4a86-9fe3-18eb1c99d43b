=== Page 480 ===

480After you upgrade all your middleware, you will need to move them from the
config/middleware.py file to the top of your Kernel file:
get_flashed method has changed to just get. Here is an example in your templates:
The static helper has changed to asset:
Finally after all the above changes attempt to run your server:from some.place import CustomMiddleware
class Kernel:
    # ...
    route_middleware = {"web": [
        # ...
        CustomMiddleware
    ],
- {{ session().get_flashed('success') }}
+ session().get('success')
- {{ static('s3.uploads', 'invoice.pdf') }}
+ {{ asset('s3.uploads', 'invoice.pdf') }}
$ python craft serveSession
Static Helper6/12/25, 3:02 AM Masonite Documentation