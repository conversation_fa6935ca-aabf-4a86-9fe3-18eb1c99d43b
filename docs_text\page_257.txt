=== Page 258 ===

258Y<PERSON> can also merge an existing dictionary into the bag with the errors:errors.amount('email') #== 1
errors.get('email')
"""
['Your email is required']
"""
errors.errors()
"""
['email', 'name']
"""
errors.messages()
"""
['Your email is required', 'Your name is required']
"""
errors.merge({'key': 'value'})Get the Messages:
Get the Errors
Get all the Messages:
Merge a Dictionary
Nested Validations6/12/25, 3:02 AM Masonite Documentation