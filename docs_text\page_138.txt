=== Page 139 ===

139Private channels require authorization from the user to connect to the channel. You can
use this channel to emit only events that a user should listen in on.
Private channels are channels that start with a private- prefix. When using private
channels, the prefix will be prepended for you automatically.
Private channels can only be broadasting on if users are logged in. When the channel is
authorized, it will check if the user is currently authenticated before it broadcasts. If the
user is not authenticated it will not broadcast anything on this channel.
This will emit events on the private-channel_name channel.
On the frontend, when you make a connection to a private channel, a POST request is
triggered by the broadcast client to authenticate the private channel. Masonite ships with
this authentication route for you. All you need to do is add it to your routes:
This will create a route you can authenticate you private channel on the frontend. The
authorization route will be /broadcasting/authorize but you can change this to
anything you like:from masonite.broadcasting import CanBroadcast
from masonite.broadcasting import PrivateChannel
class UserAdded(CanBroadcast):
    def broadcast_on(self):
      return PrivateChannel("channel_name")
from masonite.broadcasting import Broadcast
ROUTES = [
  # Normal route list here
]
ROUTES += Broadcast.routes()Routing6/12/25, 3:02 AM Masonite Documentation