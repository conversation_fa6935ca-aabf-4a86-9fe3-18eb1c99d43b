=== Page 238 ===

238To run all the tasks that are registered, we can find and execute the ones that should run
depending on the time of the computer / server:
To run only a specific registered task we can use --task option:
Finally we can force task(s) to run immediately we can use --force option. This can be
especially useful when developing locally.
Setting up Cron Jobs are for UNIX based machines like Mac and Linux only. Windows
has a similar schedule called Task Scheduler which is similar but will require different
instructions in setting it up.at(17)Specifies the time to run the job at. Can be
used with other options like daily()
run_every('7 minutes')Specifies the amount of time to run. Can be
any combination of time like 7 months, 4 
days, 3 weeks.
daily_at(17)Runs every day at the specified time. Time is in
24-hour time. 8 is "8 am" and 17 is "5pm".
attwice([8,17]) Runsat8amand5pm.
$ python craft schedule:run
$ python craft schedule:run --task MyTask
$ python craft schedule:run --task MyTask --forceRunning The Tasks
Cron Jobs6/12/25, 3:02 AM Masonite Documentation