{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/account-deletion.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/checkbox\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction DataRestorationComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.paymentRecords, \" records\");\n  }\n}\nfunction DataRestorationComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.transactionHistory, \" transactions\");\n  }\n}\nfunction DataRestorationComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Profile Backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7, \"Available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DataRestorationComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.securityEvents, \" events\");\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 24)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore saved payment methods and billing information \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore past transaction records and order history \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 27)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Profile Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore your previous profile information and preferences \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 28)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore login history and security event records \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 17)(1, \"h4\");\n    i0.ɵɵtext(2, \"Restoration Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 18);\n    i0.ɵɵtext(4, \"Select which data you want to restore:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵtemplate(6, DataRestorationComponent_form_24_mat_checkbox_6_Template, 5, 0, \"mat-checkbox\", 20)(7, DataRestorationComponent_form_24_mat_checkbox_7_Template, 5, 0, \"mat-checkbox\", 21)(8, DataRestorationComponent_form_24_mat_checkbox_8_Template, 5, 0, \"mat-checkbox\", 22)(9, DataRestorationComponent_form_24_mat_checkbox_9_Template, 5, 0, \"mat-checkbox\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.restoreForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.paymentRecords > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.transactionHistory > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.profileBackup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.securityEvents > 0);\n  }\n}\nfunction DataRestorationComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"mat-icon\", 30);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No restorable data was found in your preserved data backup.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 31);\n  }\n}\nfunction DataRestorationComponent_mat_icon_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"restore\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class DataRestorationComponent {\n  constructor(fb, accountDeletionService, snackBar, router) {\n    this.fb = fb;\n    this.accountDeletionService = accountDeletionService;\n    this.snackBar = snackBar;\n    this.router = router;\n    this.restorationComplete = new EventEmitter();\n    this.restorationSkipped = new EventEmitter();\n    this.isLoading = false;\n    this.restoreForm = this.fb.group({\n      restorePaymentData: [true],\n      restoreTransactionHistory: [true],\n      restoreProfileData: [false],\n      restoreSecurityLogs: [false]\n    });\n  }\n  ngOnInit() {\n    // Auto-check available data types\n    if (this.preservedData?.preservedDataSummary) {\n      const summary = this.preservedData.preservedDataSummary;\n      this.restoreForm.patchValue({\n        restorePaymentData: summary.paymentRecords > 0,\n        restoreTransactionHistory: summary.transactionHistory > 0,\n        restoreProfileData: summary.profileBackup === true,\n        restoreSecurityLogs: summary.securityEvents > 0\n      });\n    }\n  }\n  restoreData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isLoading = true;\n      try {\n        const restoreOptions = {\n          restorePaymentData: _this.restoreForm.value.restorePaymentData,\n          restoreTransactionHistory: _this.restoreForm.value.restoreTransactionHistory,\n          restoreProfileData: _this.restoreForm.value.restoreProfileData,\n          restoreSecurityLogs: _this.restoreForm.value.restoreSecurityLogs\n        };\n        const result = yield _this.accountDeletionService.restoreData(_this.userId, _this.email, restoreOptions).toPromise();\n        _this.snackBar.open('Data restoration completed successfully!', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-success']\n        });\n        _this.restorationComplete.emit(result);\n      } catch (error) {\n        console.error('Error restoring data:', error);\n        _this.snackBar.open(error.message || 'Failed to restore data. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-error']\n        });\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  skipRestoration() {\n    this.restorationSkipped.emit();\n  }\n  get hasSelectableData() {\n    if (!this.preservedData?.preservedDataSummary) return false;\n    const summary = this.preservedData.preservedDataSummary;\n    return summary.paymentRecords > 0 || summary.transactionHistory > 0 || summary.profileBackup === true || summary.securityEvents > 0;\n  }\n  get preservedDataSummary() {\n    return this.preservedData?.preservedDataSummary || {};\n  }\n  static #_ = this.ɵfac = function DataRestorationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DataRestorationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountDeletionService), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DataRestorationComponent,\n    selectors: [[\"app-data-restoration\"]],\n    inputs: {\n      email: \"email\",\n      userId: \"userId\",\n      preservedData: \"preservedData\"\n    },\n    outputs: {\n      restorationComplete: \"restorationComplete\",\n      restorationSkipped: \"restorationSkipped\"\n    },\n    decls: 35,\n    vars: 9,\n    consts: [[1, \"data-restoration-container\"], [1, \"restoration-card\"], [\"color\", \"primary\"], [1, \"welcome-message\"], [\"color\", \"primary\", 1, \"large-icon\"], [1, \"preserved-data-summary\"], [1, \"data-grid\"], [\"class\", \"data-item\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [\"class\", \"warning-note\", 4, \"ngIf\"], [1, \"actions\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"data-item\"], [1, \"data-count\"], [3, \"formGroup\"], [1, \"section-description\"], [1, \"restore-options\"], [\"formControlName\", \"restorePaymentData\", 4, \"ngIf\"], [\"formControlName\", \"restoreTransactionHistory\", 4, \"ngIf\"], [\"formControlName\", \"restoreProfileData\", 4, \"ngIf\"], [\"formControlName\", \"restoreSecurityLogs\", 4, \"ngIf\"], [\"formControlName\", \"restorePaymentData\"], [1, \"option-description\"], [\"formControlName\", \"restoreTransactionHistory\"], [\"formControlName\", \"restoreProfileData\"], [\"formControlName\", \"restoreSecurityLogs\"], [1, \"warning-note\"], [\"color\", \"warn\"], [\"diameter\", \"20\"]],\n    template: function DataRestorationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n        i0.ɵɵtext(5, \"restore\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Restore Previous Data \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 3)(9, \"mat-icon\", 4);\n        i0.ɵɵtext(10, \"info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\")(12, \"h3\");\n        i0.ɵɵtext(13, \"Welcome back!\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"p\");\n        i0.ɵɵtext(15, \" We found previously preserved data from your deleted account. You can choose to restore this data or permanently delete it. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 5)(17, \"h4\");\n        i0.ɵɵtext(18, \"Available Data\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵtemplate(20, DataRestorationComponent_div_20_Template, 8, 1, \"div\", 7)(21, DataRestorationComponent_div_21_Template, 8, 1, \"div\", 7)(22, DataRestorationComponent_div_22_Template, 8, 0, \"div\", 7)(23, DataRestorationComponent_div_23_Template, 8, 1, \"div\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(24, DataRestorationComponent_form_24_Template, 10, 5, \"form\", 8)(25, DataRestorationComponent_div_25_Template, 5, 0, \"div\", 9);\n        i0.ɵɵelementStart(26, \"div\", 10)(27, \"button\", 11);\n        i0.ɵɵlistener(\"click\", function DataRestorationComponent_Template_button_click_27_listener() {\n          return ctx.skipRestoration();\n        });\n        i0.ɵɵelementStart(28, \"mat-icon\");\n        i0.ɵɵtext(29, \"delete_forever\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(30, \" Delete All Data \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function DataRestorationComponent_Template_button_click_31_listener() {\n          return ctx.restoreData();\n        });\n        i0.ɵɵtemplate(32, DataRestorationComponent_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 13)(33, DataRestorationComponent_mat_icon_33_Template, 2, 0, \"mat-icon\", 14);\n        i0.ɵɵtext(34, \" Restore Selected Data \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(20);\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.paymentRecords > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.transactionHistory > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.profileBackup);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.securityEvents > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSelectableData);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasSelectableData);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.hasSelectableData);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, MatIconModule, i7.MatIcon, MatCheckboxModule, i8.MatCheckbox, MatButtonModule, i9.MatButton, MatProgressSpinnerModule, i10.MatProgressSpinner, MatSnackBarModule],\n    styles: [\".data-restoration-container[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.restoration-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.mat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 15px;\\n  padding: 20px;\\n  background-color: #e3f2fd;\\n  border: 1px solid #bbdefb;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #1565c0;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1976d2;\\n}\\n\\n.large-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n}\\n\\n.preserved-data-summary[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.preserved-data-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #495057;\\n}\\n\\n.data-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 15px;\\n  margin: 15px 0;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  color: #495057;\\n}\\n\\n.data-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 15px;\\n}\\n\\n.restore-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  margin: 20px 0;\\n}\\n\\n.option-description[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #888;\\n  margin-top: 4px;\\n}\\n\\n.warning-note[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px;\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  color: #856404;\\n  margin: 20px 0;\\n}\\n\\n.warning-note[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 20px;\\n  gap: 15px;\\n}\\n\\n.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .data-restoration-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .welcome-message[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ReactiveFormsModule", "MatCardModule", "MatIconModule", "MatCheckboxModule", "MatButtonModule", "MatProgressSpinnerModule", "MatSnackBarModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "preservedDataSummary", "paymentRecords", "transactionHistory", "securityEvents", "ɵɵtemplate", "DataRestorationComponent_form_24_mat_checkbox_6_Template", "DataRestorationComponent_form_24_mat_checkbox_7_Template", "DataRestorationComponent_form_24_mat_checkbox_8_Template", "DataRestorationComponent_form_24_mat_checkbox_9_Template", "ɵɵproperty", "restoreForm", "profileBackup", "ɵɵelement", "DataRestorationComponent", "constructor", "fb", "accountDeletionService", "snackBar", "router", "restorationComplete", "restorationSkipped", "isLoading", "group", "restorePaymentData", "restoreTransactionHistory", "restoreProfileData", "restoreSecurityLogs", "ngOnInit", "preservedData", "summary", "patchValue", "restoreData", "_this", "_asyncToGenerator", "restoreOptions", "value", "result", "userId", "email", "to<PERSON>romise", "open", "duration", "panelClass", "emit", "error", "console", "message", "skipRestoration", "hasSelectableData", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AccountDeletionService", "i3", "MatSnackBar", "i4", "Router", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DataRestorationComponent_Template", "rf", "ctx", "DataRestorationComponent_div_20_Template", "DataRestorationComponent_div_21_Template", "DataRestorationComponent_div_22_Template", "DataRestorationComponent_div_23_Template", "DataRestorationComponent_form_24_Template", "DataRestorationComponent_div_25_Template", "ɵɵlistener", "DataRestorationComponent_Template_button_click_27_listener", "DataRestorationComponent_Template_button_click_31_listener", "DataRestorationComponent_mat_spinner_32_Template", "DataRestorationComponent_mat_icon_33_Template", "i5", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i7", "MatIcon", "i8", "MatCheckbox", "i9", "MatButton", "i10", "MatProgressSpinner", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\data-restoration\\data-restoration.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\data-restoration\\data-restoration.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { Router } from '@angular/router';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { AccountDeletionService } from '../../services/account-deletion.service';\r\nimport { PreservedDataCheck, DataRestoreOptions } from '../../models/account-deletion.model';\r\n\r\n@Component({\r\n  selector: 'app-data-restoration',\r\n  templateUrl: './data-restoration.component.html',\r\n  styleUrls: ['./data-restoration.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatCardModule,\r\n    MatIconModule,\r\n    MatCheckboxModule,\r\n    MatButtonModule,\r\n    MatProgressSpinnerModule,\r\n    MatSnackBarModule\r\n  ]\r\n})\r\nexport class DataRestorationComponent implements OnInit {\r\n  @Input() email!: string;\r\n  @Input() userId!: string;\r\n  @Input() preservedData!: PreservedDataCheck;\r\n  @Output() restorationComplete = new EventEmitter<any>();\r\n  @Output() restorationSkipped = new EventEmitter<void>();\r\n\r\n  restoreForm: FormGroup;\r\n  isLoading = false;\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private accountDeletionService: AccountDeletionService,\r\n    private snackBar: MatSnackBar,\r\n    private router: Router\r\n  ) {\r\n    this.restoreForm = this.fb.group({\r\n      restorePaymentData: [true],\r\n      restoreTransactionHistory: [true],\r\n      restoreProfileData: [false],\r\n      restoreSecurityLogs: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Auto-check available data types\r\n    if (this.preservedData?.preservedDataSummary) {\r\n      const summary = this.preservedData.preservedDataSummary as any;\r\n      this.restoreForm.patchValue({\r\n        restorePaymentData: summary.paymentRecords > 0,\r\n        restoreTransactionHistory: summary.transactionHistory > 0,\r\n        restoreProfileData: summary.profileBackup === true,\r\n        restoreSecurityLogs: summary.securityEvents > 0\r\n      });\r\n    }\r\n  }\r\n\r\n  async restoreData(): Promise<void> {\r\n    this.isLoading = true;\r\n\r\n    try {\r\n      const restoreOptions: DataRestoreOptions = {\r\n        restorePaymentData: this.restoreForm.value.restorePaymentData,\r\n        restoreTransactionHistory: this.restoreForm.value.restoreTransactionHistory,\r\n        restoreProfileData: this.restoreForm.value.restoreProfileData,\r\n        restoreSecurityLogs: this.restoreForm.value.restoreSecurityLogs\r\n      };\r\n\r\n      const result = await this.accountDeletionService.restoreData(\r\n        this.userId,\r\n        this.email,\r\n        restoreOptions\r\n      ).toPromise();\r\n\r\n      this.snackBar.open(\r\n        'Data restoration completed successfully!',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['snack-bar-success'] }\r\n      );\r\n\r\n      this.restorationComplete.emit(result);\r\n\r\n    } catch (error: any) {\r\n      console.error('Error restoring data:', error);\r\n      this.snackBar.open(\r\n        error.message || 'Failed to restore data. Please try again.',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['snack-bar-error'] }\r\n      );\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  skipRestoration(): void {\r\n    this.restorationSkipped.emit();\r\n  }\r\n\r\n  get hasSelectableData(): boolean {\r\n    if (!this.preservedData?.preservedDataSummary) return false;\r\n    \r\n    const summary = this.preservedData.preservedDataSummary as any;\r\n    return summary.paymentRecords > 0 || \r\n           summary.transactionHistory > 0 || \r\n           summary.profileBackup === true || \r\n           summary.securityEvents > 0;\r\n  }\r\n\r\n  get preservedDataSummary(): any {\r\n    return this.preservedData?.preservedDataSummary || {};\r\n  }\r\n}\r\n", "<div class=\"data-restoration-container\">\r\n  <mat-card class=\"restoration-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon color=\"primary\">restore</mat-icon>\r\n        Restore Previous Data\r\n      </mat-card-title>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <div class=\"welcome-message\">\r\n        <mat-icon color=\"primary\" class=\"large-icon\">info</mat-icon>\r\n        <div>\r\n          <h3>Welcome back!</h3>\r\n          <p>\r\n            We found previously preserved data from your deleted account. \r\n            You can choose to restore this data or permanently delete it.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"preserved-data-summary\">\r\n        <h4>Available Data</h4>\r\n        <div class=\"data-grid\">\r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.paymentRecords > 0\">\r\n            <mat-icon>payment</mat-icon>\r\n            <div>\r\n              <strong>Payment Data</strong>\r\n              <div class=\"data-count\">{{ preservedDataSummary.paymentRecords }} records</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.transactionHistory > 0\">\r\n            <mat-icon>history</mat-icon>\r\n            <div>\r\n              <strong>Transaction History</strong>\r\n              <div class=\"data-count\">{{ preservedDataSummary.transactionHistory }} transactions</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.profileBackup\">\r\n            <mat-icon>account_circle</mat-icon>\r\n            <div>\r\n              <strong>Profile Backup</strong>\r\n              <div class=\"data-count\">Available</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.securityEvents > 0\">\r\n            <mat-icon>security</mat-icon>\r\n            <div>\r\n              <strong>Security Logs</strong>\r\n              <div class=\"data-count\">{{ preservedDataSummary.securityEvents }} events</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <form [formGroup]=\"restoreForm\" *ngIf=\"hasSelectableData\">\r\n        <h4>Restoration Options</h4>\r\n        <p class=\"section-description\">Select which data you want to restore:</p>\r\n\r\n        <div class=\"restore-options\">\r\n          <mat-checkbox formControlName=\"restorePaymentData\" \r\n                        *ngIf=\"preservedDataSummary.paymentRecords > 0\">\r\n            <strong>Restore Payment Data</strong>\r\n            <div class=\"option-description\">\r\n              Restore saved payment methods and billing information\r\n            </div>\r\n          </mat-checkbox>\r\n\r\n          <mat-checkbox formControlName=\"restoreTransactionHistory\" \r\n                        *ngIf=\"preservedDataSummary.transactionHistory > 0\">\r\n            <strong>Restore Transaction History</strong>\r\n            <div class=\"option-description\">\r\n              Restore past transaction records and order history\r\n            </div>\r\n          </mat-checkbox>\r\n\r\n          <mat-checkbox formControlName=\"restoreProfileData\" \r\n                        *ngIf=\"preservedDataSummary.profileBackup\">\r\n            <strong>Restore Profile Data</strong>\r\n            <div class=\"option-description\">\r\n              Restore your previous profile information and preferences\r\n            </div>\r\n          </mat-checkbox>\r\n\r\n          <mat-checkbox formControlName=\"restoreSecurityLogs\" \r\n                        *ngIf=\"preservedDataSummary.securityEvents > 0\">\r\n            <strong>Restore Security Logs</strong>\r\n            <div class=\"option-description\">\r\n              Restore login history and security event records\r\n            </div>\r\n          </mat-checkbox>\r\n        </div>\r\n      </form>\r\n\r\n      <div class=\"warning-note\" *ngIf=\"!hasSelectableData\">\r\n        <mat-icon color=\"warn\">info</mat-icon>\r\n        <p>No restorable data was found in your preserved data backup.</p>\r\n      </div>\r\n\r\n      <div class=\"actions\">\r\n        <button mat-button (click)=\"skipRestoration()\">\r\n          <mat-icon>delete_forever</mat-icon>\r\n          Delete All Data\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" \r\n                (click)=\"restoreData()\" \r\n                [disabled]=\"isLoading || !hasSelectableData\">\r\n          <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\r\n          <mat-icon *ngIf=\"!isLoading\">restore</mat-icon>\r\n          Restore Selected Data\r\n        </button>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": ";AAAA,SAA2CA,YAAY,QAAQ,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAgC,gBAAgB;AAC5E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;;;;;;;;;;;;;;ICiBhEC,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE1BH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7BH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiD;IAE7EF,EAF6E,CAAAG,YAAA,EAAM,EAC3E,EACF;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,oBAAA,CAAAC,cAAA,aAAiD;;;;;IAK3ER,EADF,CAAAC,cAAA,cAA2E,eAC/D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE1BH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAEtFF,EAFsF,CAAAG,YAAA,EAAM,EACpF,EACF;;;;IAFsBH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,oBAAA,CAAAE,kBAAA,kBAA0D;;;;;IAKpFT,EADF,CAAAC,cAAA,cAAkE,eACtD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/BH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAErCF,EAFqC,CAAAG,YAAA,EAAM,EACnC,EACF;;;;;IAGJH,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9BH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAE5EF,EAF4E,CAAAG,YAAA,EAAM,EAC1E,EACF;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,oBAAA,CAAAG,cAAA,YAAgD;;;;;IAa1EV,EAFF,CAAAC,cAAA,uBAC8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,8DACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAIbH,EAFF,CAAAC,cAAA,uBACkE,aACxD;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,2DACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAIbH,EAFF,CAAAC,cAAA,uBACyD,aAC/C;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,kEACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAIbH,EAFF,CAAAC,cAAA,uBAC8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,yDACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAlCjBH,EADF,CAAAC,cAAA,eAA0D,SACpD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzEH,EAAA,CAAAC,cAAA,cAA6B;IAyB3BD,EAxBA,CAAAW,UAAA,IAAAC,wDAAA,2BAC8D,IAAAC,wDAAA,2BAQI,IAAAC,wDAAA,2BAQT,IAAAC,wDAAA,2BAQK;IAOlEf,EADE,CAAAG,YAAA,EAAM,EACD;;;;IArCDH,EAAA,CAAAgB,UAAA,cAAAV,MAAA,CAAAW,WAAA,CAAyB;IAMZjB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAC,cAAA,KAA6C;IAQ7CR,EAAA,CAAAI,SAAA,EAAiD;IAAjDJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAE,kBAAA,KAAiD;IAQjDT,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAW,aAAA,CAAwC;IAQxClB,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAG,cAAA,KAA6C;;;;;IAU9DV,EADF,CAAAC,cAAA,cAAqD,mBAC5B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kEAA2D;IAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;IAUFH,EAAA,CAAAmB,SAAA,sBAA2D;;;;;IAC3DnB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;ADjFzD,OAAM,MAAOiB,wBAAwB;EASnCC,YACUC,EAAe,EACfC,sBAA8C,EAC9CC,QAAqB,EACrBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IATN,KAAAC,mBAAmB,GAAG,IAAInC,YAAY,EAAO;IAC7C,KAAAoC,kBAAkB,GAAG,IAAIpC,YAAY,EAAQ;IAGvD,KAAAqC,SAAS,GAAG,KAAK;IAOf,IAAI,CAACX,WAAW,GAAG,IAAI,CAACK,EAAE,CAACO,KAAK,CAAC;MAC/BC,kBAAkB,EAAE,CAAC,IAAI,CAAC;MAC1BC,yBAAyB,EAAE,CAAC,IAAI,CAAC;MACjCC,kBAAkB,EAAE,CAAC,KAAK,CAAC;MAC3BC,mBAAmB,EAAE,CAAC,KAAK;KAC5B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACC,aAAa,EAAE5B,oBAAoB,EAAE;MAC5C,MAAM6B,OAAO,GAAG,IAAI,CAACD,aAAa,CAAC5B,oBAA2B;MAC9D,IAAI,CAACU,WAAW,CAACoB,UAAU,CAAC;QAC1BP,kBAAkB,EAAEM,OAAO,CAAC5B,cAAc,GAAG,CAAC;QAC9CuB,yBAAyB,EAAEK,OAAO,CAAC3B,kBAAkB,GAAG,CAAC;QACzDuB,kBAAkB,EAAEI,OAAO,CAAClB,aAAa,KAAK,IAAI;QAClDe,mBAAmB,EAAEG,OAAO,CAAC1B,cAAc,GAAG;OAC/C,CAAC;IACJ;EACF;EAEM4B,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACfD,KAAI,CAACX,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAMa,cAAc,GAAuB;UACzCX,kBAAkB,EAAES,KAAI,CAACtB,WAAW,CAACyB,KAAK,CAACZ,kBAAkB;UAC7DC,yBAAyB,EAAEQ,KAAI,CAACtB,WAAW,CAACyB,KAAK,CAACX,yBAAyB;UAC3EC,kBAAkB,EAAEO,KAAI,CAACtB,WAAW,CAACyB,KAAK,CAACV,kBAAkB;UAC7DC,mBAAmB,EAAEM,KAAI,CAACtB,WAAW,CAACyB,KAAK,CAACT;SAC7C;QAED,MAAMU,MAAM,SAASJ,KAAI,CAAChB,sBAAsB,CAACe,WAAW,CAC1DC,KAAI,CAACK,MAAM,EACXL,KAAI,CAACM,KAAK,EACVJ,cAAc,CACf,CAACK,SAAS,EAAE;QAEbP,KAAI,CAACf,QAAQ,CAACuB,IAAI,CAChB,0CAA0C,EAC1C,OAAO,EACP;UAAEC,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,mBAAmB;QAAC,CAAE,CACtD;QAEDV,KAAI,CAACb,mBAAmB,CAACwB,IAAI,CAACP,MAAM,CAAC;MAEvC,CAAC,CAAC,OAAOQ,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CZ,KAAI,CAACf,QAAQ,CAACuB,IAAI,CAChBI,KAAK,CAACE,OAAO,IAAI,2CAA2C,EAC5D,OAAO,EACP;UAAEL,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,iBAAiB;QAAC,CAAE,CACpD;MACH,CAAC,SAAS;QACRV,KAAI,CAACX,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEA0B,eAAeA,CAAA;IACb,IAAI,CAAC3B,kBAAkB,CAACuB,IAAI,EAAE;EAChC;EAEA,IAAIK,iBAAiBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAE5B,oBAAoB,EAAE,OAAO,KAAK;IAE3D,MAAM6B,OAAO,GAAG,IAAI,CAACD,aAAa,CAAC5B,oBAA2B;IAC9D,OAAO6B,OAAO,CAAC5B,cAAc,GAAG,CAAC,IAC1B4B,OAAO,CAAC3B,kBAAkB,GAAG,CAAC,IAC9B2B,OAAO,CAAClB,aAAa,KAAK,IAAI,IAC9BkB,OAAO,CAAC1B,cAAc,GAAG,CAAC;EACnC;EAEA,IAAIH,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAAC4B,aAAa,EAAE5B,oBAAoB,IAAI,EAAE;EACvD;EAAC,QAAAiD,CAAA,G;qCAzFUpC,wBAAwB,EAAApB,EAAA,CAAAyD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3D,EAAA,CAAAyD,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAA7D,EAAA,CAAAyD,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAAyD,iBAAA,CAAAO,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxB9C,wBAAwB;IAAA+C,SAAA;IAAAC,MAAA;MAAAvB,KAAA;MAAAD,MAAA;MAAAT,aAAA;IAAA;IAAAkC,OAAA;MAAA3C,mBAAA;MAAAC,kBAAA;IAAA;IAAA2C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1B7B3E,EAJR,CAAAC,cAAA,aAAwC,kBACH,sBAChB,qBACC,kBACY;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5CH,EAAA,CAAAE,MAAA,8BACF;QACFF,EADE,CAAAG,YAAA,EAAiB,EACD;QAIdH,EAFJ,CAAAC,cAAA,uBAAkB,aACa,kBACkB;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAE1DH,EADF,CAAAC,cAAA,WAAK,UACC;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qIAEF;QAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAGJH,EADF,CAAAC,cAAA,cAAoC,UAC9B;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvBH,EAAA,CAAAC,cAAA,cAAuB;QAyBrBD,EAxBA,CAAAW,UAAA,KAAAkE,wCAAA,iBAAuE,KAAAC,wCAAA,iBAQI,KAAAC,wCAAA,iBAQT,KAAAC,wCAAA,iBAQK;QAQ3EhF,EADE,CAAAG,YAAA,EAAM,EACF;QAyCNH,EAvCA,CAAAW,UAAA,KAAAsE,yCAAA,mBAA0D,KAAAC,wCAAA,iBAuCL;QAMnDlF,EADF,CAAAC,cAAA,eAAqB,kBAC4B;QAA5BD,EAAA,CAAAmF,UAAA,mBAAAC,2DAAA;UAAA,OAASR,GAAA,CAAAtB,eAAA,EAAiB;QAAA,EAAC;QAC5CtD,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnCH,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAEqD;QAD7CD,EAAA,CAAAmF,UAAA,mBAAAE,2DAAA;UAAA,OAAST,GAAA,CAAAtC,WAAA,EAAa;QAAA,EAAC;QAG7BtC,EADA,CAAAW,UAAA,KAAA2E,gDAAA,0BAA6C,KAAAC,6CAAA,uBAChB;QAC7BvF,EAAA,CAAAE,MAAA,+BACF;QAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;QA7F4BH,EAAA,CAAAI,SAAA,IAA6C;QAA7CJ,EAAA,CAAAgB,UAAA,SAAA4D,GAAA,CAAArE,oBAAA,CAAAC,cAAA,KAA6C;QAQ7CR,EAAA,CAAAI,SAAA,EAAiD;QAAjDJ,EAAA,CAAAgB,UAAA,SAAA4D,GAAA,CAAArE,oBAAA,CAAAE,kBAAA,KAAiD;QAQjDT,EAAA,CAAAI,SAAA,EAAwC;QAAxCJ,EAAA,CAAAgB,UAAA,SAAA4D,GAAA,CAAArE,oBAAA,CAAAW,aAAA,CAAwC;QAQxClB,EAAA,CAAAI,SAAA,EAA6C;QAA7CJ,EAAA,CAAAgB,UAAA,SAAA4D,GAAA,CAAArE,oBAAA,CAAAG,cAAA,KAA6C;QAUxCV,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAAgB,UAAA,SAAA4D,GAAA,CAAArB,iBAAA,CAAuB;QAuC7BvD,EAAA,CAAAI,SAAA,EAAwB;QAAxBJ,EAAA,CAAAgB,UAAA,UAAA4D,GAAA,CAAArB,iBAAA,CAAwB;QAYzCvD,EAAA,CAAAI,SAAA,GAA4C;QAA5CJ,EAAA,CAAAgB,UAAA,aAAA4D,GAAA,CAAAhD,SAAA,KAAAgD,GAAA,CAAArB,iBAAA,CAA4C;QACtBvD,EAAA,CAAAI,SAAA,EAAe;QAAfJ,EAAA,CAAAgB,UAAA,SAAA4D,GAAA,CAAAhD,SAAA,CAAe;QAChC5B,EAAA,CAAAI,SAAA,EAAgB;QAAhBJ,EAAA,CAAAgB,UAAA,UAAA4D,GAAA,CAAAhD,SAAA,CAAgB;;;mBD3FjCpC,YAAY,EAAAgG,EAAA,CAAAC,IAAA,EACZhG,mBAAmB,EAAAiE,EAAA,CAAAgC,aAAA,EAAAhC,EAAA,CAAAiC,eAAA,EAAAjC,EAAA,CAAAkC,oBAAA,EAAAlC,EAAA,CAAAmC,kBAAA,EAAAnC,EAAA,CAAAoC,eAAA,EACnBpG,aAAa,EAAAqG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbxG,aAAa,EAAAyG,EAAA,CAAAC,OAAA,EACbzG,iBAAiB,EAAA0G,EAAA,CAAAC,WAAA,EACjB1G,eAAe,EAAA2G,EAAA,CAAAC,SAAA,EACf3G,wBAAwB,EAAA4G,GAAA,CAAAC,kBAAA,EACxB5G,iBAAiB;IAAA6G,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}