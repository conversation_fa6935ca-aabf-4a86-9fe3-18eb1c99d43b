=== Page 425 ===

425Y<PERSON>'ll also have to add a new RESOURCES = [] line to your routes/api.py file for the
new Masonite Entry package if you choose to use it.
This release works with the new craft command release. Upgrade to version masonite-
cli / 1.1+. <1.1 will only work with Masonite 1.4 and below.
Simply run:
You may have to run sudo if you are using a UNIX machine.
Masonite 1.5 now has sessions that can be used to hold temporary data. It comes with
the cookie and memory drivers. Memory stores all data in a class which is lost when the
server restarts and the cookie driver sets cookies in the browser.
There is a new config/session.py file you can copy and paste:$ pip install --upgrade masonite-cli
Craft Commands
Sessions6/12/25, 3:02 AM Masonite Documentation