=== Page 396 ===

396All new 2.1 projects have a seeder setup so you can quickly make some mock users to
start off your application. All users have a randomly generated email and the password of
"secret".
You can run seeders by running:
When setting headers we had to set the http_prefix to None more times then not. So it is
set by default.
This:
can change to:
Originally the code:
would return None if there was no header. Now this returns a blank string.$ craft seed:run
def show(self, request: Request):
    request.header('Content-Type', 'application/xml', http_prefix=None)
def show(self, request: Request):
    request.header('Content-Type', 'application/xml')
def show(self, request: Request):
    request.header('Content-Type') #== ''Made HTTP Prefix to None by Default
Getting a header returns blank string rather than
None6/12/25, 3:02 AM Masonite Documentation