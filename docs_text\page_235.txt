=== Page 236 ===

236Task Scheduling
Masonite ships with an incredibly simple way to run recurring tasks. Tasks could be as
simple as cleaning records on a database table every minute, or syncing records between
databases, or sending invoices out at the end of the month. They are those automated
recurring tasks you need to run on a schedule, like every minute, every hour, every day,
every month, or anywhere in between.
Tasks are what you use to register to the Masonite scheduler do it knows which tasks to
run and how often.
To create a task, simply run the command:
This will create a task that looks like this:
You can change the task to do whatever you need it to do:$ python craft task SendInvoices
from masonite.scheduling import Task
class SendInvoices(Task):
    def handle(self):
        pass
from masonite.scheduling import Task
class SendInvoices(Task):
    def handle(self):
      users = User.have_invoices().get()
      for user in users:
        # send invoice to user
        passCreating Tasks6/12/25, 3:02 AM Masonite Documentation