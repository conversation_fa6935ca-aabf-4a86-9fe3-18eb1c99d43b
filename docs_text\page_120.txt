=== Page 121 ===

121This will create the below routes which will match up with your API Controller methods:
The routes we added earlier contain 2 authentication methods. The /api/auth route
can be used to get a new authentication token:
First, send a POST request with a username and password to get back a JWT token:
You should then send this token with either a token input or a Authorization header:Method URL Action Route Name
GET /users index users.show
GET /users/@id show users.show
POST /users store users.store
PUT/PATCH /users/@id update users.update
DELETE /users/@id destroy users.destroy
{
    "data": 
"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJleHBpcmVzIjpudWxsLCJ2ZXJzaW9uI
jpudWxsfQ.OFBijJFsVm4IombW6Md1RUsN5v_btPwl-qtY-
QSTBQ0b7N2pca8BnnT4OjfXOVRrKCWaKUM3QsGj8zqxCD4xJg"
}
Authorization: Bearer 
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJleHBpcmVzIjpudWxsLCJ2ZXJzaW9uIj
pudWxsfQ.OFBijJFsVm4IombW6Md1RUsN5v_btPwl-qtY-
QSTBQ0b7N2pca8BnnT4OjfXOVRrKCWaKUM3QsGj8zqxCD4xJg Authentication
Reauthentication6/12/25, 3:02 AM Masonite Documentation