=== Page 474 ===

474Routes have changed slightly.
First, routes are now all under the Route class. The route classes have moved into
methods:
The WSGI file has also changed.
Go to this file and replace your own wsgi.py file
The import path has changed for the following:from masonite.providers import FrameworkProvider, HelpersProvider, 
ExceptionProvider, EventProvider, HashServiceProvider
PROVIDERS = [
FrameworkProvider,
  ExceptionProvider,
  EventProvider,
  HashServiceProvider,
  
from masonite.routes import Route
ROUTES = [
-     Get('/', 'Controller@method').name('home')
-     Post('/login', 'Controller@method').name('home')
+     Route.get('/', 'Controller@method').name('home')
+     Route.post('/', 'Controller@method').name('home')
]
Routes
WSGI File
Import Path Changes6/12/25, 3:02 AM Masonite Documentation