=== Page 86 ===

86Y<PERSON> can also get the current authenticated user. This is the same as doing 
request.user().
This will now submit this form as a PUT request.
You can get a route by it's name by using this method:
If your route contains variables you need to pass then you can supply a dictionary as the
second argument.
or a list:<p> Token: {{ csrf_token }} </p>
<p> User: {{ auth().email }} </p>
<form action="{{ route('route.name') }}" method="POST">
    ..
</form>
<form action="{{ route('route.name', {'id': 1}) }}" method="POST">
    ..
</form>
<form action="{{ route('route.name', [1]) }}" method="POST">
    ..
</form>Current User
Route
Back6/12/25, 3:02 AM Masonite Documentation