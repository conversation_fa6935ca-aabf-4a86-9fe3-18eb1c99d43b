=== Page 397 ===

397There is now an up and down command so you can put that in your application in a
maintenance state via craft commands:
There is also a new MaintenanceModeMiddleware:
We removed the store_prepend() method on the upload drivers for the filename
keyword arg on the store method.
So this:
now becomes:$ craft down
$ craft up
from masonite.middleware import MaintenanceModeMiddleware
HTTP_MIDDLEWARE = [
    ...
    MaintenanceModeMiddleware,
    ...
]
upload.store_prepend('random-string', request.input('file'))
upload.store(request.input('file'), filename='random-string')Added Maintenance Mode
Removed Store Prepend method6/12/25, 3:02 AM Masonite Documentation