=== Page 83 ===

83View sharing is when you want a variable to be available in all templates that are
rendered. This could be the authenticated user, a copyright year, or anything else you
want shared between templates.
View sharing requires you to add a dictionary:
Then inside your template you can do:
You will typically do this inside your appService Provider (if you don't have one, you
should create one):from masonite.views import View
class WelcomeController(Controller):
  # Template is templates/greetings/welcome.html
  def show(self, view: View):
    view.render('greeting.welcome', {
    "name": "<PERSON>"
    })
from masonite.facades import View
View.share({
  'copyright': '2021'
})
<div>
  Copyright {{ copyright }}
</div>View Sharing6/12/25, 3:02 AM Masonite Documentation