=== Page 259 ===

259Sometimes you will need to check values that aren't on the top level of a dictionary like
the examples shown here. In this case we can use dot notation to validate deeper
dictionaries:
notice the dot notation here. Each . being a deeper level to the dictionary.
Sometimes your validations will have lists and you will need to ensure that each element
in the list validates. For example you want to make sure that a user passes in a list of
names and ID's.
For this you can use the * asterisk to validate these:"""
{
  'domain': 'http://google.com',
  'email': '<EMAIL>'
  'user': {
     'id': 1,
     'email': '<EMAIL>',
     'status': {
         'active': 1,
         'banned': 0
     }
  }
}
"""
errors = request.validate(
    validate.required('user.email'),
    validate.truthy('user.status.active')
)
Nested Validations With Lists6/12/25, 3:02 AM Masonite Documentation