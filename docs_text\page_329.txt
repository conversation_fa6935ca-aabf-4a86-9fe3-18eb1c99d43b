=== Page 330 ===

330•dumpRequestHeaders
•dumpResponseHeaders
•ddHeaders
•dumpSession
•ddSession
After getting a test response back from a request you can dump request headers in
console by chaining this helper to the response:
After getting a test response back from a request you can dump response headers in
console:
After getting a test response back from a request you can dump response and request
headers in console and stop the test execution:
Here assertSessionHas will not be executed as the test will be stopped before.self.get("/register").assertRedirect().dumpRequestHeaders().assertSessi
onHas("success")
self.get("/").dumpResponseHeaders()
self.get("/register").assertRedirect().ddHeaders().assertSessionHas("su
ccess")dumpRequestHeaders
dumpResponseHeaders
ddHeaders
dumpSession6/12/25, 3:02 AM Masonite Documentation