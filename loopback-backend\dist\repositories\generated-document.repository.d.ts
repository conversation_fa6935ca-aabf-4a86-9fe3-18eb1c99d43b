import { Getter } from '@loopback/core';
import { DefaultCrudRepository, BelongsToAccessor } from '@loopback/repository';
import { DbDataSource } from '../datasources';
import { GeneratedDocument, GeneratedDocumentRelations, CrawlJob, User } from '../models';
import { CrawlJobRepository } from './crawl-job.repository';
import { UserRepository } from './user.repository';
export declare class GeneratedDocumentRepository extends DefaultCrudRepository<GeneratedDocument, typeof GeneratedDocument.prototype.id, GeneratedDocumentRelations> {
    protected crawlJobRepositoryGetter: Getter<CrawlJobRepository>;
    protected userRepositoryGetter: Getter<UserRepository>;
    readonly crawlJob: BelongsToAccessor<CrawlJob, typeof GeneratedDocument.prototype.id>;
    readonly user: BelongsToAccessor<User, typeof GeneratedDocument.prototype.id>;
    constructor(dataSource: DbDataSource, crawlJobRepositoryGetter: Getter<CrawlJobRepository>, userRepositoryGetter: Getter<UserRepository>);
    /**
     * Find documents by crawl job ID
     */
    findByCrawlJobId(crawlJobId: string): Promise<GeneratedDocument[]>;
    /**
     * Find documents by user ID
     */
    findByUserId(userId: string): Promise<GeneratedDocument[]>;
    /**
     * Find documents by status
     */
    findByStatus(status: string): Promise<GeneratedDocument[]>;
    /**
     * Find documents by format
     */
    findByFormat(format: string, userId?: string): Promise<GeneratedDocument[]>;
    /**
     * Update document generation progress
     */
    updateProgress(id: string, processedPages: number, totalPages: number, status?: string): Promise<void>;
    /**
     * Record document download
     */
    recordDownload(id: string): Promise<void>;
    /**
     * Find expired documents
     */
    findExpiredDocuments(): Promise<GeneratedDocument[]>;
    /**
     * Get user document statistics
     */
    getUserDocumentStatistics(userId: string): Promise<object>;
    /**
     * Group documents by format
     */
    private groupDocumentsByFormat;
    /**
     * Group documents by organization type
     */
    private groupDocumentsByOrganization;
    /**
     * Clean up expired documents
     */
    cleanupExpiredDocuments(): Promise<number>;
    /**
     * Find documents ready for cleanup (old completed documents)
     */
    findDocumentsForCleanup(daysOld?: number): Promise<GeneratedDocument[]>;
}
