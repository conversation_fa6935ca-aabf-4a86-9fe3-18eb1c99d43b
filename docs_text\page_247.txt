=== Page 248 ===

248Our rule class needs 3 methods that you see when you run the rule command, a passes,
message and negated_message methods.
Passes Method
The passes method needs to return some kind of boolean value for the use case in which
this rule passes.
For example if you need to make a rule that a value always equals Masonite then you can
make the method look like this:class equals_masonite(BaseValidation):
    """A rule_name validation class
    """
    def passes(self, attribute, key, dictionary):
        """The passing criteria for this rule.
        ...
        """
        return attribute
    def message(self, key):
        """A message to show when this rule fails
        ...
        """
        return '{} is required'.format(key)
    def negated_message(self, key):
        """A message to show when this rule is negated using a negation 
rule like 'isnt()'
        ...
        """
        return '{} is not required'.format(key)
Constructing our Rule6/12/25, 3:02 AM Masonite Documentation