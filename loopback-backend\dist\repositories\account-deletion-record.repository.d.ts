import { DefaultCrudRepository } from '@loopback/repository';
import { DbDataSource } from '../datasources';
import { AccountDeletionRecord, AccountDeletionRecordRelations } from '../models';
export declare class AccountDeletionRecordRepository extends DefaultCrudRepository<AccountDeletionRecord, typeof AccountDeletionRecord.prototype.id, AccountDeletionRecordRelations> {
    constructor(dataSource: DbDataSource);
    /**
     * Find deletion record by email
     */
    findByEmail(email: string): Promise<AccountDeletionRecord | null>;
    /**
     * Find active deletion records that can be restored
     */
    findRestorableByEmail(email: string): Promise<AccountDeletionRecord | null>;
    /**
     * Find expired deletion records for cleanup
     */
    findExpiredRecords(): Promise<AccountDeletionRecord[]>;
    /**
     * Update deletion status and completion date
     */
    completeDeletion(id: string, status: string): Promise<void>;
}
