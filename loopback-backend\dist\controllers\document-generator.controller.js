"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentGeneratorController = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const core_1 = require("@loopback/core");
const authentication_1 = require("@loopback/authentication");
const security_1 = require("@loopback/security");
const models_1 = require("../models");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let DocumentGeneratorController = class DocumentGeneratorController {
    constructor(generatedDocumentRepository, crawlJobRepository, crawledContentRepository, documentGeneratorService) {
        this.generatedDocumentRepository = generatedDocumentRepository;
        this.crawlJobRepository = crawlJobRepository;
        this.crawledContentRepository = crawledContentRepository;
        this.documentGeneratorService = documentGeneratorService;
    }
    async generateDocument(options, currentUser) {
        // Verify crawl job ownership
        const crawlJob = await this.crawlJobRepository.findById(options.crawlJobId);
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        // Verify crawl job is completed
        if (crawlJob.status !== 'completed') {
            throw new rest_1.HttpErrors.BadRequest('Crawl job must be completed before generating documents');
        }
        // Verify selected content exists and belongs to the crawl job
        const selectedContent = await this.crawledContentRepository.find({
            where: {
                id: { inq: options.selectedContentIds },
                crawlJobId: options.crawlJobId,
            },
        });
        if (selectedContent.length !== options.selectedContentIds.length) {
            throw new rest_1.HttpErrors.BadRequest('Some selected content items do not exist or do not belong to this crawl job');
        }
        // Generate document
        return this.documentGeneratorService.generateDocument(options.crawlJobId, currentUser.id, options);
    }
    async findGeneratedDocuments(currentUser, filter) {
        // Filter by current user
        const userFilter = {
            ...filter,
            where: {
                ...filter?.where,
                userId: currentUser.id,
            },
        };
        return this.generatedDocumentRepository.find(userFilter);
    }
    async findGeneratedDocumentById(id, currentUser, filter) {
        const document = await this.generatedDocumentRepository.findById(id, filter);
        // Check ownership
        if (document.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return document;
    }
    async getGenerationProgress(id, currentUser) {
        const document = await this.generatedDocumentRepository.findById(id);
        // Check ownership
        if (document.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return this.documentGeneratorService.getGenerationProgress(id);
    }
    async cancelGeneration(id, currentUser) {
        const document = await this.generatedDocumentRepository.findById(id);
        // Check ownership
        if (document.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        if (document.status !== 'generating' && document.status !== 'pending') {
            throw new rest_1.HttpErrors.BadRequest('Document generation cannot be cancelled in current status');
        }
        await this.documentGeneratorService.cancelGeneration(id);
        return { message: 'Document generation cancelled successfully' };
    }
    async downloadDocument(id, currentUser) {
        const document = await this.generatedDocumentRepository.findById(id);
        // Check ownership
        if (document.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        if (document.status !== 'completed') {
            throw new rest_1.HttpErrors.BadRequest('Document is not ready for download');
        }
        if (!document.filePath) {
            throw new rest_1.HttpErrors.NotFound('Document file not found');
        }
        // Record download
        await this.generatedDocumentRepository.recordDownload(id);
        // Return download information
        return {
            filename: document.filename,
            filePath: document.filePath,
            fileSize: document.fileSize,
            downloadUrl: document.downloadUrl,
        };
    }
    async deleteGeneratedDocument(id, currentUser) {
        const document = await this.generatedDocumentRepository.findById(id);
        // Check ownership
        if (document.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        // Cancel generation if running
        if (document.status === 'generating' || document.status === 'pending') {
            await this.documentGeneratorService.cancelGeneration(id);
        }
        // Delete document
        await this.generatedDocumentRepository.deleteById(id);
    }
    async getUserStatistics(currentUser) {
        return this.generatedDocumentRepository.getUserDocumentStatistics(currentUser.id);
    }
    async updateContentSelection(selectionData, currentUser) {
        // Verify crawl job ownership
        const crawlJob = await this.crawlJobRepository.findById(selectionData.crawlJobId);
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        // Update content selection
        await this.crawledContentRepository.updateSelection(selectionData.contentIds, selectionData.isSelected, selectionData.selectionGroup);
        return { message: 'Content selection updated successfully' };
    }
    async getSelectedContent(crawlJobId, currentUser, selectionGroup) {
        // Verify crawl job ownership
        const crawlJob = await this.crawlJobRepository.findById(crawlJobId);
        if (crawlJob.userId !== currentUser.id) {
            throw new rest_1.HttpErrors.Forbidden('Access denied');
        }
        return this.crawledContentRepository.findSelectedContent(crawlJobId, selectionGroup);
    }
};
exports.DocumentGeneratorController = DocumentGeneratorController;
tslib_1.__decorate([
    (0, rest_1.post)('/document-generator/generate'),
    (0, rest_1.response)(200, {
        description: 'Generate document from crawled content',
        content: { 'application/json': { schema: (0, rest_1.getModelSchemaRef)(models_1.GeneratedDocument) } },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['crawlJobId', 'format', 'organizationType', 'selectedContentIds'],
                    properties: {
                        crawlJobId: { type: 'string' },
                        format: {
                            type: 'string',
                            enum: ['pdf', 'docx', 'markdown', 'html', 'txt'],
                        },
                        organizationType: {
                            type: 'string',
                            enum: ['single_file', 'separate_files', 'grouped_folders'],
                        },
                        selectedContentIds: {
                            type: 'array',
                            items: { type: 'string' },
                        },
                        destinationFolder: { type: 'string' },
                        includeImages: { type: 'boolean' },
                        includeToc: { type: 'boolean' },
                        customStyles: { type: 'object' },
                        template: { type: 'string' },
                        metadata: { type: 'object' },
                    },
                },
            },
        },
    })),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "generateDocument", null);
tslib_1.__decorate([
    (0, rest_1.get)('/document-generator/documents'),
    (0, rest_1.response)(200, {
        description: 'Array of GeneratedDocument model instances',
        content: {
            'application/json': {
                schema: {
                    type: 'array',
                    items: (0, rest_1.getModelSchemaRef)(models_1.GeneratedDocument, { includeRelations: true }),
                },
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, rest_1.param.filter(models_1.GeneratedDocument)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "findGeneratedDocuments", null);
tslib_1.__decorate([
    (0, rest_1.get)('/document-generator/documents/{id}'),
    (0, rest_1.response)(200, {
        description: 'GeneratedDocument model instance',
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.GeneratedDocument, { includeRelations: true }),
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(2, rest_1.param.filter(models_1.GeneratedDocument, { exclude: 'where' })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "findGeneratedDocumentById", null);
tslib_1.__decorate([
    (0, rest_1.get)('/document-generator/documents/{id}/progress'),
    (0, rest_1.response)(200, {
        description: 'Get document generation progress',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "getGenerationProgress", null);
tslib_1.__decorate([
    (0, rest_1.post)('/document-generator/documents/{id}/cancel'),
    (0, rest_1.response)(200, {
        description: 'Cancel document generation',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "cancelGeneration", null);
tslib_1.__decorate([
    (0, rest_1.get)('/document-generator/documents/{id}/download'),
    (0, rest_1.response)(200, {
        description: 'Download generated document',
        content: {
            'application/octet-stream': {
                schema: { type: 'string', format: 'binary' },
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "downloadDocument", null);
tslib_1.__decorate([
    (0, rest_1.del)('/document-generator/documents/{id}'),
    (0, rest_1.response)(204, {
        description: 'GeneratedDocument DELETE success',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('id')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "deleteGeneratedDocument", null);
tslib_1.__decorate([
    (0, rest_1.get)('/document-generator/statistics'),
    (0, rest_1.response)(200, {
        description: 'Get user document generation statistics',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "getUserStatistics", null);
tslib_1.__decorate([
    (0, rest_1.post)('/document-generator/content/select'),
    (0, rest_1.response)(200, {
        description: 'Update content selection for document generation',
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['crawlJobId', 'contentIds', 'isSelected'],
                    properties: {
                        crawlJobId: { type: 'string' },
                        contentIds: {
                            type: 'array',
                            items: { type: 'string' },
                        },
                        isSelected: { type: 'boolean' },
                        selectionGroup: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "updateContentSelection", null);
tslib_1.__decorate([
    (0, rest_1.get)('/document-generator/content/{crawlJobId}/selected'),
    (0, rest_1.response)(200, {
        description: 'Get selected content for document generation',
        content: {
            'application/json': {
                schema: {
                    type: 'array',
                    items: (0, rest_1.getModelSchemaRef)(models_1.GeneratedDocument),
                },
            },
        },
    }),
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, rest_1.param.path.string('crawlJobId')),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(2, rest_1.param.query.string('selectionGroup')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, String]),
    tslib_1.__metadata("design:returntype", Promise)
], DocumentGeneratorController.prototype, "getSelectedContent", null);
exports.DocumentGeneratorController = DocumentGeneratorController = tslib_1.__decorate([
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.GeneratedDocumentRepository)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.CrawlJobRepository)),
    tslib_1.__param(2, (0, repository_1.repository)(repositories_1.CrawledContentRepository)),
    tslib_1.__param(3, (0, core_1.inject)('services.DocumentGeneratorService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.GeneratedDocumentRepository,
        repositories_1.CrawlJobRepository,
        repositories_1.CrawledContentRepository,
        services_1.DocumentGeneratorService])
], DocumentGeneratorController);
//# sourceMappingURL=document-generator.controller.js.map