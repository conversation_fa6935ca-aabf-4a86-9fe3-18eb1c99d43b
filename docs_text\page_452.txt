=== Page 453 ===

453All classes have now been changed to unittest classes. This will still work with pytest and
you can still run python -m pytest. The only thing that changes is the structure of the 
setup_method(). This has been renamed to setUp().
A class like this:
Should now look like this:
Previously all methods were snake_case but to continue with the unittest convention, all
testing methods are camelCase.
A method like:from masonite.testing import TestCase
class TestSomeUnit(TestCase):
    ...
from masonite.testing import UnitTest
from routes.web import ROUTES
class TestSomeUnit(UnitTest):
    def setup_method(self):
        super().setup_method()
        self.routes(ROUTES)
from masonite.testing import TestCase
class TestSomeUnit(TestCase):
    def setUp(self):
        super().setUp()Pytest VS Unittest
Method naming6/12/25, 3:02 AM Masonite Documentation