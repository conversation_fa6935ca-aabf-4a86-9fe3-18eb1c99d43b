=== Page 399 ===

399This is used as a wrapper around I/O operations. It will also be a wrapper around the
upload drivers and moving files around and other file management type operations
We can now specify directly in the configuration file whether or not the threading or
multiprocessing for the async type operations.
Learn more in the Queues documentation here.
We added 4 new HTTP verbs: HEAD, CONNECT, OPTIONS, TRACE. You import these
and use them like normal:
Learn more in the Routes documentation here.
from masonite.routes import Connect, Trace
ROUTES = [
    Connect('..'),
    Trace('..'),
]
Added a storage manager and disk storage
drivers
Async driver now can be specified whether to
use threading or processing
Added new HTTP Verbs
JSON error responses6/12/25, 3:02 AM Masonite Documentation