=== Page 273 ===

273Used to make sure the value is a list (a Python list instance)
* notation can also be used
Used to make sure if a value is in a specific value"""
{
  'date': '2019-10-20', # Or date in the future
}
"""
validate.is_future('date', tz='America/New_York')
"""
{
  'tags': [1,3,7]
}
"""
validate.is_list('tags')
"""
{
  'name': '<PERSON>',
  'discounts_ref': [1,2,3]
}
"""
validate.is_list('discounts_ref.*')
"""
{
  'age': 5
}
"""
validate.is_in('age', [2,4,5])Is_list
Is_in6/12/25, 3:02 AM Masonite Documentation