=== Page 62 ===

62Middleware
Middleware is an extremely important aspect of web applications as it allows you to run
important code either before or after every request or even before or after certain routes.
In this documentation we'll talk about how middleware works, how to consume
middleware and how to create your own middlewar
Middleware classes are placed inside the Kernel class. All middleware are just classes
that contain a before method and after method.
There are four types of middleware in total:
•Middleware ran before every request
•Middleware ran after every request
•Middleware ran before certain routes
•Middleware ran after certain routes
We have one of two configuration attributes we need to work with. These attributes both
reside in our Kernel file and are http_middleware and route_middleware.
http_middleware is a simple list which should contain your middleware classes. This
attribute is a list because all middleware will simply run in succession one after another,
similar to Django middleware
In our Kernel file this type of middleware may look something like:Getting Started
Configuration6/12/25, 3:02 AM Masonite Documentation