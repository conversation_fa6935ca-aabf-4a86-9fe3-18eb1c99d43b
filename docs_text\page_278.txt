=== Page 279 ===

279dictionary use exists.
Used to make sure that value is present and not empty only if an other field has a given
value.
Used to make sure that value is present and not empty onlyf if any of the other specified
fields are present."""
{
  'age': 25,
  'email': '<EMAIL>',
  'first_name': ''
}
"""
validate.required(['age', 'email'])
validate.required('first_name')  # would fail
validate.required('last_name')  # would fail
"""
{
  'first_name': '<PERSON>',
  'last_name': '<PERSON><PERSON><PERSON>'
}
"""
validate.required_if('first_name', 'last_name', '<PERSON>am<PERSON>')
"""
{
  'first_name': '<PERSON>',
  'last_name': '<PERSON><PERSON><PERSON>'
  'email': '<EMAIL>'
}
"""
validate.required_with('email', ['last_name', 'nick_name'])Required If
Required With6/12/25, 3:02 AM Masonite Documentation