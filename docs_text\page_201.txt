=== Page 202 ===

202If you would like to broadcast the notification then you need to:
•inherit the CanBroadcast class and specify the broadcast_on method
•define a to_broadcast method on the notification class to specify how to build the
notification content that will be broadcasted
By default notifications will be broadcasted to channel(s) defined in broadcast_on
method but you can override this per notifiable by implementing 
route_notification_for_broadcast method on your notifiable:class Welcome(Notification, CanBroadcast):
    def to_broadcast(self, notifiable):
        return f"Welcome {notifiable.name} !"
    def broadcast_on(self):
        return "channel1"
    def via(self, notifiable):
        return ["broadcast"]
class User(Model, Notifiable):
    def route_notification_for_broadcast(self):
        return ["general", f"user_{self.id}"]
notification.route("broadcast", "channel1").notify(Welcome())Broadcasting Notifications
Broadcasting to notifiables
Broadcasting to anonymous6/12/25, 3:02 AM Masonite Documentation