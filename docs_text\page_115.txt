=== Page 116 ===

116API Development
Adding API Support to your Masonite project is very simple. Masonite comes with
supporting for returning JSON responses already but there are a few API features for
authenticating and authorizing that are helpful.
Default projects don't come with these features so you'll have to simply register them.
First, register the ApiProvider in your project by adding the service provider to your
PROVIDERS list:
This will register some required classes used for API development.
You should now create an api.py file for adding your API routes to:
You will then have to add a binding to the container for the location of this file. You can
do so in your Kernel.py file inside the register_routes method:from masonite.api.providers import ApiProvider
PROVIDERS = [
    #..
    ApiProvider
]
# routes/api.py
ROUTES = [
    Route.get('/users', 'UsersController@index')
]Getting Started
Provider6/12/25, 3:02 AM Masonite Documentation