=== Page 211 ===

211Now you're ready to upload your package to PyPI. Ensure that all parameters of 
setup.py file are up to date. It's the file describing your package. Fill in the correct
version number you want to publish. When ready you can run:
This will install twine if not installed yet, build the package, upload it to PyPi and delete
the build artifacts. You should then see a success message and be able to browse your
package on PyPi.
You should always check that the package name is available on PyPi and that the version
number to publish has not been published before. Else you won't be able to publish your
package.
Make the package available on masonite packages list
To make your package available on packages.masoniteproject.com (alpha version) you
need to add Framework :: Masonite classifier in setup.py:
You can find more information on the website FAQ.
When developing a package you might need to use a configuration file, to add migrations,
routes and controllers or views. All those resources can be located in your package but at
one point a user might want to override it and will need to publish those resources locally
in its project.
The following section will explain how to register those resources in your package to be
used in a Masonite project and how to make those resources publishable.$ make publish
# setup.py
    classifiers=[
        #...
        "Framework :: Masonite",
    ]
Registering Resources6/12/25, 3:02 AM Masonite Documentation