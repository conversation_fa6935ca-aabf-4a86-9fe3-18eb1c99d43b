=== Page 95 ===

95Using alternative Jinja2 syntax:
Any place you have repeating code you can break out and put it into an include template.
These templates will have access to all variables in the current template.
This is useful for having a child template extend a parent template. There can only be 1
extends per template:
Line Statements:
Using alternative Jinja2 syntax:@include 'components/errors.html'
<form action="/">
</form>
<div data-gb-custom-block data-tag="include" data-
0='components/errors.html'></div>
<form action="/">
</form>
@extends 'components/base.html'
@block content
    <p> read below to find out what a block is </p>
@endblockExtends6/12/25, 3:02 AM Masonite Documentation