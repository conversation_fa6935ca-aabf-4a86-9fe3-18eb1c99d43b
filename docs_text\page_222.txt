=== Page 223 ===

223Y<PERSON> can also specify any number of options using keyword arguments on the push
method:
To run a queue worker, which is a terminal process than runs the jobs, you can use the 
queue:work command:
This will start up a worker using the default queue configurations. You can also modify
the options:from masonite.queues import Queue
from app.jobs.CreateInvoice import CreateInvoice
class InvoiceController:
  def generate(self, queue: Queue):
    # Jobs with no parameters
    queue.push(CreateInvoice())
    # Jobs with parameters
    # Create an invoice from a payment
    queue.push(CreateInvoice(Order.find(1).id))
queue.push(
  CreateInvoice(Order.find(1).id),
  driver="async", # The queue driver to use
  queue="invoices", # The queue name to put the job on
)
$ python craft queue:work
Option Description
--driver database Specifies which driver to use for this worker.
--queue invoices Specifis which queue to use for this worker.
--connection mysql Specifies the connection to use for the worker.Queue Workers6/12/25, 3:02 AM Masonite Documentation