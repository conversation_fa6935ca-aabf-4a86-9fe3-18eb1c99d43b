=== Page 367 ===

367HTML forms only support GET and POST so there is no the ability to add any other HTTP
methods like PUT and PATCH which will change the actual request method on
submission. You can read more about this in the Requests documentation.
You can now use functions for routes instead of the classes. These new functions are
just wrappers around the classes itself. Read more about this under the Routing
documentation.
In Masonite 1.4 and below was the Api() route which added some very basic API
endpoints. All references to API's have been removed from core and added to the new
Masonite Entry official package.
If you would like to use API's you will have to use the Masonite Entry package instead.
You can now get and set any header information using the new Request.header
method. This also allows third party packages to manipulate header information. Read
more about this in the Requests documentation.
You can now delete cookies using the delete_cookie method as well as set expiration
dates for them. See the Requests documentation for more information.
Added Shorthand Route Helpers
Removed API from core
Headers
Cookies6/12/25, 3:02 AM Masonite Documentation