import { DefaultCrudRepository } from '@loopback/repository';
import { OAuthAuthorizationCode, OAuthAuthorizationCodeRelations } from '../models';
import { DbDataSource } from '../datasources';
export declare class OAuthAuthorizationCodeRepository extends DefaultCrudRepository<OAuthAuthorizationCode, typeof OAuthAuthorizationCode.prototype.code, OAuthAuthorizationCodeRelations> {
    constructor(dataSource: DbDataSource);
    /**
     * Clean up expired authorization codes
     */
    cleanupExpiredCodes(): Promise<void>;
    /**
     * Find and mark code as used
     */
    findAndUseCode(code: string): Promise<OAuthAuthorizationCode | null>;
}
