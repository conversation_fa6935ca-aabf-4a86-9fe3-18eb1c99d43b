=== Page 201 ===

201A notifiable entity has a notifications relationship that will returns the notifications
for the entity:
You can directly get the unread/read notifications:
You can mark a notification as read or unread with the following mark_as_read and 
mark_as_unread methods
Finally, keep in mind that database notifications can be used as any Masonite ORM models,
meaning you can for example make more complex queries to fetch notifications, directly on
the model.user = User.find(1)
user.notifications.all() # == Collection of DatabaseNotification 
belonging to users
user = User.find(1)
user.unread_notifications.all() # == Collection of user unread 
DatabaseNotification
user.read_notifications.all() # == Collection of user read 
DatabaseNotification
user = User.find(1)
for notification in user.unread_notifications.all():
    notification.mark_as_read()
from masonite.notification import DatabaseNotification
DatabaseNotification.all()
DatabaseNotification.where("type", "WelcomeNotification")Querying notifications
Managing notifications6/12/25, 3:02 AM Masonite Documentation