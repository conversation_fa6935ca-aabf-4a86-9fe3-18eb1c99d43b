=== Page 79 ===

79Static Files
Masonite tries to make static files extremely easy and comes with whitenoise out of the
box. Whiteno<PERSON> wraps the WSGI application and listens for certain URI requests that can
be registered in your configuration files and serves those assets.
All configurations that are specific to static files can be found in 
config/filesystem.py. In this file you'll find a constant file called STATICFILES which
is simply a dictionary of directories as keys and aliases as the value.
The directories to include as keys are simply the location of your static file locations as a
relative path starting from the base of your application. For example, if your css files are
in storage/assets/css then put that folder location as the key. For the value, put the
alias you want to use in your templates. For this example, we will use css/ as the alias.
For this setup, our STATICFILES constant should look like:
Now in our templates we can use:
Which will get the storage/assets/css/style.css file.
All templates have a static function that can be used to assist in getting locations of
static files. You can specify the driver and locations you want using the driver name orSTATICFILES = {
    'storage/assets/css': 'assets/',
}
<img src="/assets/style.css">Configuration
Static Template Functionconfig/filesystem.py6/12/25, 3:02 AM Masonite Documentation