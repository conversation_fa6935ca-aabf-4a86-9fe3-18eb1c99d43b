=== Page 447 ===

447Masonite 2.1 to 2.2
Welcome to the upgrade guide to get your Masonite 2.1 application working with
Masonite 2.2. We'll be focusing on all the breaking changes so we can get all your code
working on a Masonite 2.2 release cycle.
We will not go into all the better ways to use some of the features. For those changes be
sure to read the "Whats New in 2.2" documentation to the left to see what fits into your
application and what doesn't. We will only focus on the breaking changes here.
Masonite 2.2 is jam packed with amazing new features and most of which are backwards
compatible so upgrading from Masonite 2.1 to 2.2 is really simple.
We'll go through each section that your application will need to be upgraded and how it
can be done.
Each upgrade will have an impact rating from LOW to HIGH. The lower the rating, the
less likely it will be that your specific application needs the upgrade.
First let's upgrade Masonite to 2.2 first so we can see any exceptions that will be raised.
Let's upgrade by doing:
You can also add it to your requirements.txt or Pipfile.pip install masonite==2.2.0Masonite 2.1 to 2.2
Introduction
Getting Started6/12/25, 3:02 AM Masonite Documentation