=== Page 9 ===

9Django framework
Let's create our first route now. We can put all routes inside routes/web.py and inside
the ROUTES list. You'll see we have a route for the home page. Let's add a route for
creating blogs.
You'll notice here we have a Blog<PERSON>ontroller@show string. This means "use the blog
controller's show method to render this route". The only problem here is that we don't yet
have a blog controller.
All controllers are located in the app/controllers directory by default and Masonite
promotes a 1 controller per file structure. This has proven efficient for larger application
development because most developers use text editors with advanced search features
such as Sublime, VSCode or Atom. Switching between classes in this instance is simple
and promotes faster development. It's easy to remember where the controller exactly is
because the name of the file is the controller.
You can of course move controllers around wherever you like them but the craft
command line tool will default to putting them in separate files. If this seems weird to you
it might be worth a try to see if you like this opinionated layout.
Like most parts of Masonite, you can scaffold a controller with a craft command:routes/web.py
ROUTES = [
    Route.get('/', 'WelcomeController@show').name('welcome'),
    # Blog Routes
    Route.get('/blog', 'BlogController@show')
]
Creating a Controller
Creating Our First Controller6/12/25, 3:02 AM Masonite Documentation