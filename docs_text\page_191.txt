=== Page 192 ===

192You should define a to_mail method on the notification class to specify how to build
the email notification content.
The notification will be sent using the default mail driver defined in config/mail.py.
For more information about options to build mail notifications, please check out Mailable
options.
If you want to override the mail driver for a given notification you can do:from masonite.notification import Notification
from masonite.queues import Queueable
class Welcome(Notification, Queueable):
    # ...
class Welcome(Notification, Mailable):
    def to_mail(self, notifiable):
        return (
            self.to(notifiable.email)
            .subject("Welcome to our site!")
            .from_("<EMAIL>")
            .text(f"Hello {notifiable.name}")
        )
    def via(self, notifiable):
        return ["mail"]Channels
Mail6/12/25, 3:02 AM Masonite Documentation