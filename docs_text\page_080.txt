=== Page 81 ===

81Sometimes you may need to serve files that are normally in the root of your application
such as a robots.txt or manifest.json. These files can be aliased in your 
STATICFILES directory in config/filesystem.py. They do not have to be in the root
of your project but instead could be in a storage/root or storage/public directory
and aliased with a simple /.
For example a basic setup would have this as your directory:
and you can alias this in your STATICFILES constant:
You will now be able to access localhost:8000/robots.txt and you will have your
robots.txt served correctly and it can be indexed by search engines properly.
Thats it! Static files are extremely simple. You are now a master at static files!...
<img src="{{ asset('s3.east', 'profile.jpg') }}" alt="profile" />
...
<img src="{{ asset('s3.west', 'profile.jpg') }}" alt="profile" />
...
resources/
routes/
storage/
  static/
  root/
    robots.txt
    manifest.json
STATICFILES = {
    # folder          # template alias
    'storage/static': 'static/',
    ...
    'storage/public': '/'
}Serving "Root" Files
config/filesystem.py6/12/25, 3:02 AM Masonite Documentation