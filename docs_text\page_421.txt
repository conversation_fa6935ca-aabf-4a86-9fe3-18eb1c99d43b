=== Page 422 ===

422Masonite comes with a lot of out of the box functionality and nearly all of it is optional
but Masonite 1.4 ships with three new providers. Most Service Providers are not ran on
every request and therefore does not add significant overhead to each request. To add
these 3 new Service Providers simple add these to the bottom of the list of framework
providers:
Note however that if you add the CsrfProvider then you will also need the CSRF
middleware which is new in Masonite 1.4. Read the section below to add the middleware
Masonite 1.4 adds CSRF protection. So anywhere there is any POST form request, you
will need to add the {{ csrf_field }} to it. For example:
This type of protection prevents cross site forgery. In order to activate this feature, we
also need to add the CSRF middleware. Copy and paste the middleware into yourPROVIDERS = [
    # Framework Providers
    ...
    'masonite.providers.HelpersProvider.HelpersProvider',
    'masonite.providers.QueueProvider.QueueProvider',
    # 3 New Providers in Masonite 1.4
    'masonite.providers.BroadcastProvider.BroadcastProvider',
    'masonite.providers.CacheProvider.CacheProvider',
    'masonite.providers.CsrfProvider.CsrfProvider',
    # Third Party Providers
    # Application Providers
    'app.providers.UserModelProvider.UserModelProvider',
    'app.providers.MiddlewareProvider.MiddlewareProvider',
]
<form action="/dashboard" method="POST">
    {{ csrf_field }}
    <input type="text" name="first_name">
</form>
CSRF and CSRF Middleware6/12/25, 3:02 AM Masonite Documentation