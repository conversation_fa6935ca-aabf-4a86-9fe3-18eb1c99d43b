=== Page 450 ===

450This is now completely changed to use a better and more sleeker validation. The above
validation can now be written like this:
You can do a lot of other awesome things like rule enclosures. Read more under the 
Validation documentation
Masonite 2.2 changes a bit how the masonite.auth.Auth class resolves out of the
container and how it resolves its own dependencies.
Now instead of doing something like:from app.validators import RegisterValidator
    def store(self):
        validate = RegisterValidator(self.request).register()
        if validate.check():
            validate.check_exists()
        if not validate.check():
            self.request.session.flash('validation', 
json.dumps(validate.errors()))
            return self.request.redirect_to('register')
from masonite.validation import Validator
    def store(self, request: Request, validator: Validator):
        errors = request.validate(
            validator.required(['username', 'email', 'password'])
            validator.length('username', min=3, max=20)
            validator.length('email', min=3)
            validator.isnt(
                validator.is_in('email', User.all().pluck('email'))
            )
        )
        if errors:
            return 
self.request.redirect_to('register').with_errors(errors)
Auth class now auto resolves it's own request class6/12/25, 3:02 AM Masonite Documentation