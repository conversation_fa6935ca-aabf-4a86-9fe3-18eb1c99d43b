=== Page 256 ===

256Working with errors may be a lot especially if you have a lot of errors which results in
quite a big dictionary to work with.
Because of this, Masonite Validation comes with a MessageBag class which you can use
to wrap your errors in. This will look like this:
You can easily get all errors using the all() method:
This is just the opposite of the any() method.from masonite.validation import MessageBag
# ...
def show(self, request: Request):
    errors = request.validate(
        email('email')
    ) #== <masonite.validation.MessageBag>
errors.all()
"""
{
  'email': ['Your email is required'],
  'name': ['Your name is required']
}
"""
errors.any() #== True
errors.empty() #== FalseGetting All Errors:
Checking for any errors
Checking if the bag is Empty6/12/25, 3:02 AM Masonite Documentation