=== Page 181 ===

181The optional helper takes an object and allows any method calls on the object. If the
method exists on the object it will return the value or else it will return None:
You may have a peice of code that looks like this:
with the optional helper you can condense the code into something like this:
You can easily dump variables into console for debugging, from inside a controller for
example: For this you can use Dump facade or the built-in dump python method:from masonite.view import View
from masonite.helpers import compact
def show(self, view: View):
  users = User.all()
  articles = Article.all()
  return view.render('some.view', compact(users, articles))
if request.user() and request.user().admin == 1:
  #.. do something
from masonite.helpers import optional
if optional(request.user()).admin == 1:
  #.. do somethingOptional
Dump6/12/25, 3:02 AM Masonite Documentation