=== Page 478 ===

478Authentication has also changes slightly. Whenever you are logging in a user the
following UI has changed:
Controllers have not changes much but in order for routes to pick up your string
controllers, you must inherit from Masonite controller class:
The rest of your controller structure remains the same.
Add the following to the config/application.py file.- auth.login(request.input('email'), request.input('password'))
+ auth.attempt(request.input('email'), request.input('password'))
from masonite.controllers import Controller
class DashboardController(Controller):
  # ..
HASHING = {
    "default": "bcrypt",
    "bcrypt": {"rounds": 10},
    "argon2": {"memory": 1024, "threads": 2, "time": 2},
}Controllers
Routes
Config Changes
Application6/12/25, 3:02 AM Masonite Documentation