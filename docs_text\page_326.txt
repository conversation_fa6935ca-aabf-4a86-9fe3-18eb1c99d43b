=== Page 327 ===

327If a user instance is given it will assert that this user is authenticated:
The authentication guard can also be specified to check the authentication state on the
given guard.
Assert that a user is not authenticated after the current request.
The authentication guard can also be specified to check the authentication state on the
given guard.
Assert that the request has the given HTTP middleware. An HTTP middleware class
should be provided.self.get("/login").assertAuthenticated()
user = User.find(1)
self.get("/login").assertAuthenticated(user)
self.get("/api/login").assertAuthenticated(user, "jwt")
self.get("/").assertGuest()
self.get("/").assertHasHttpMiddleware(middleware_class)
from app.middleware import MyAppMiddleware
self.get("/").assertHasHttpMiddleware(MyAppMiddleware)assertGuest
assertHasHttpMiddleware
assertHasRouteMiddleware6/12/25, 3:02 AM Masonite Documentation