import { Response } from '@loopback/rest';
import { TokenService } from '@loopback/authentication';
import { UserRepository, OAuthAuthorizationCodeRepository } from '../repositories';
import { OAuthService } from '../services';
export declare class OAuthController {
    jwtService: TokenService;
    protected userRepository: UserRepository;
    protected oauthAuthCodeRepository: OAuthAuthorizationCodeRepository;
    oauthService: OAuthService;
    private response;
    constructor(jwtService: TokenService, userRepository: UserRepository, oauthAuthCodeRepository: OAuthAuthorizationCodeRepository, oauthService: OAuthService, response: Response);
    getOAuthUrl(provider: string): Promise<{
        url: string;
        provider: string;
        state?: string;
    }>;
    handleOAuthCallback(provider: string, request: {
        code: string;
        state?: string;
    }): Promise<{
        token: string;
        user: any;
        isNewUser: boolean;
    }>;
    handleOAuthRedirect(code?: string, state?: string, error?: string): Promise<void>;
    exchangeAuthorizationCode(request: {
        code: string;
    }): Promise<{
        token: string;
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            avatarUrl?: string;
            emailVerified: boolean;
            roles: string[];
        };
        isNewUser: boolean;
        provider: string;
    }>;
    getAvailableProviders(): Promise<{
        providers: Array<{
            name: string;
            enabled: boolean;
            configured: boolean;
        }>;
    }>;
}
