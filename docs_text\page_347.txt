=== Page 348 ===

348Finally ensure debug mode is enabled else the debugbar will not be displayed !
Now when you go to a page in your application, you will see a debug bar at the bottom of
the page.
The configuration file is created on collectors
Not all collectors may be equally important to you so you can set anyone of these to
either True or False in order to enable or disable them in the debugbar.
Masonite Debugbar collects data to show in your debugbar as a tab. Each collector is 1
tab in the debugbar.
Below is a detailed explanation of each collector
The model collector shows how many models are hydrated on that request. Whenever
you make a query call, a model instance has to be created to store that rows data. This
could be a costly operation depending on how many rows are in the table you are calling.
OPTIONS = {
    "models": True,
    "queries": True,
    "request": True,
    "measures": True,
    "messages": True,
    "environment": True,
}Config
Collectors
Model Collector6/12/25, 3:02 AM Masonite Documentation