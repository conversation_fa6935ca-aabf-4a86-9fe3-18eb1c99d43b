{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/two-factor.service\";\nimport * as i3 from \"../../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction Disable2FAConfirmComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"mat-spinner\", 6);\n    i0.ɵɵelementStart(2, \"h2\");\n    i0.ɵɵtext(3, \"Processing your request...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please wait while we disable two-factor authentication on your account.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Disable2FAConfirmComponent_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h3\");\n    i0.ɵɵtext(2, \"Account Details:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"strong\");\n    i0.ɵɵtext(9, \"Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userInfo.email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.userInfo.firstName, \" \", ctx_r1.userInfo.lastName);\n  }\n}\nfunction Disable2FAConfirmComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\", 8);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Two-Factor Authentication Disabled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6, \" Two-factor authentication has been successfully disabled on your account. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, Disable2FAConfirmComponent_div_3_div_7_Template, 11, 3, \"div\", 10);\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"mat-icon\", 12);\n    i0.ɵɵtext(10, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"h4\");\n    i0.ɵɵtext(13, \"Important Security Notice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15, \"Your account is now less secure without two-factor authentication. Consider:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ul\")(17, \"li\");\n    i0.ɵɵtext(18, \"Using a strong, unique password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"Re-enabling 2FA from your profile settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"li\");\n    i0.ɵɵtext(22, \"Monitoring your account for any suspicious activity\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_3_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToDashboard());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Go to Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_3_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToProfile());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Profile Settings \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userInfo);\n  }\n}\nfunction Disable2FAConfirmComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Unable to Disable 2FA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"h4\");\n    i0.ɵɵtext(9, \"What you can do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ul\")(11, \"li\");\n    i0.ɵɵtext(12, \"Check if the link has expired (links are valid for 1 hour)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"li\");\n    i0.ɵɵtext(14, \"Request a new disable email from the login screen\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"li\");\n    i0.ɵɵtext(16, \"Contact support if you continue to have issues\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_4_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_4_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestNewLink());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Request New Link \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nexport class Disable2FAConfirmComponent {\n  constructor(route, router, twoFactorService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.twoFactorService = twoFactorService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.loading = true;\n    this.success = false;\n    this.errorMessage = '';\n    this.userInfo = null;\n  }\n  ngOnInit() {\n    const token = this.route.snapshot.queryParamMap.get('token');\n    if (!token) {\n      this.loading = false;\n      this.success = false;\n      this.errorMessage = 'Invalid or missing disable token. Please request a new disable link.';\n      return;\n    }\n    this.confirmDisable2FA(token);\n  }\n  confirmDisable2FA(token) {\n    this.twoFactorService.confirmDisable2FA(token).subscribe({\n      next: response => {\n        this.loading = false;\n        this.success = response.success;\n        this.userInfo = response.user;\n        if (response.success) {\n          // Update current user if they're logged in\n          if (this.authService.currentUserValue) {\n            const updatedUser = {\n              ...this.authService.currentUserValue\n            };\n            updatedUser.twoFactorEnabled = false;\n            // Update the user in auth service if needed\n          }\n          this.snackBar.open('Two-factor authentication has been disabled successfully.', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = response.message || 'Failed to disable 2FA';\n        }\n      },\n      error: error => {\n        console.error('Error confirming 2FA disable:', error);\n        this.loading = false;\n        this.success = false;\n        if (error.status === 400) {\n          this.errorMessage = 'The disable link has expired or is invalid. Please request a new one.';\n        } else {\n          this.errorMessage = error.error?.message || 'An unexpected error occurred. Please try again.';\n        }\n      }\n    });\n  }\n  goToDashboard() {\n    this.router.navigate(['/dashboard']);\n  }\n  goToProfile() {\n    this.router.navigate(['/profile']);\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  requestNewLink() {\n    this.router.navigate(['/auth/login'], {\n      queryParams: {\n        message: 'Please use the \"Request disable 2FA\" option to get a new link.'\n      }\n    });\n  }\n  static #_ = this.ɵfac = function Disable2FAConfirmComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Disable2FAConfirmComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: Disable2FAConfirmComponent,\n    selectors: [[\"app-disable-2fa-confirm\"]],\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"disable-confirm-container\"], [1, \"disable-confirm-card\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"success-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"60\"], [1, \"success-state\"], [1, \"success-icon\"], [1, \"success-message\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"warning-icon\"], [1, \"notice-content\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"user-info\"], [1, \"error-state\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-help\"]],\n    template: function Disable2FAConfirmComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1);\n        i0.ɵɵtemplate(2, Disable2FAConfirmComponent_div_2_Template, 6, 0, \"div\", 2)(3, Disable2FAConfirmComponent_div_3_Template, 32, 1, \"div\", 3)(4, Disable2FAConfirmComponent_div_4_Template, 26, 1, \"div\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.success);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.success);\n      }\n    },\n    dependencies: [i5.NgIf, i6.MatCard, i7.MatButton, i8.MatIcon, i9.MatProgressSpinner],\n    styles: [\".disable-confirm-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n      padding: 20px;\\n    }\\n\\n    .disable-confirm-card[_ngcontent-%COMP%] {\\n      max-width: 600px;\\n      width: 100%;\\n      padding: 40px;\\n      text-align: center;\\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n    }\\n\\n    .loading-state[_ngcontent-%COMP%] {\\n      padding: 20px;\\n    }\\n\\n    .loading-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n      margin: 20px 0 10px 0;\\n      color: #333;\\n    }\\n\\n    .loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      color: #666;\\n      margin-bottom: 0;\\n    }\\n\\n    .success-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%] {\\n      padding: 20px;\\n    }\\n\\n    .success-icon[_ngcontent-%COMP%] {\\n      font-size: 80px;\\n      width: 80px;\\n      height: 80px;\\n      color: #4caf50;\\n      margin-bottom: 20px;\\n    }\\n\\n    .error-icon[_ngcontent-%COMP%] {\\n      font-size: 80px;\\n      width: 80px;\\n      height: 80px;\\n      color: #f44336;\\n      margin-bottom: 20px;\\n    }\\n\\n    .success-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n      margin: 0 0 20px 0;\\n      color: #333;\\n    }\\n\\n    .success-message[_ngcontent-%COMP%], .error-message[_ngcontent-%COMP%] {\\n      font-size: 16px;\\n      color: #666;\\n      margin-bottom: 30px;\\n      line-height: 1.6;\\n    }\\n\\n    .user-info[_ngcontent-%COMP%] {\\n      background: #f8f9fa;\\n      border-radius: 8px;\\n      padding: 20px;\\n      margin: 20px 0;\\n      text-align: left;\\n    }\\n\\n    .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      margin: 0 0 15px 0;\\n      color: #333;\\n      font-size: 16px;\\n    }\\n\\n    .user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 8px 0;\\n      color: #555;\\n    }\\n\\n    .security-notice[_ngcontent-%COMP%] {\\n      background: #fff3e0;\\n      border: 1px solid #ff9800;\\n      border-radius: 8px;\\n      padding: 20px;\\n      margin: 20px 0;\\n      display: flex;\\n      align-items: flex-start;\\n      gap: 16px;\\n      text-align: left;\\n    }\\n\\n    .warning-icon[_ngcontent-%COMP%] {\\n      color: #ff9800;\\n      font-size: 32px;\\n      width: 32px;\\n      height: 32px;\\n      flex-shrink: 0;\\n    }\\n\\n    .notice-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n      margin: 0 0 10px 0;\\n      color: #f57c00;\\n      font-size: 16px;\\n    }\\n\\n    .notice-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 0 0 10px 0;\\n      color: #e65100;\\n    }\\n\\n    .notice-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n      margin: 0;\\n      padding-left: 20px;\\n      color: #e65100;\\n    }\\n\\n    .notice-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n      margin-bottom: 5px;\\n    }\\n\\n    .error-help[_ngcontent-%COMP%] {\\n      background: #ffebee;\\n      border: 1px solid #f44336;\\n      border-radius: 8px;\\n      padding: 20px;\\n      margin: 20px 0;\\n      text-align: left;\\n    }\\n\\n    .error-help[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n      margin: 0 0 10px 0;\\n      color: #d32f2f;\\n      font-size: 16px;\\n    }\\n\\n    .error-help[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n      margin: 0;\\n      padding-left: 20px;\\n      color: #c62828;\\n    }\\n\\n    .error-help[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n      margin-bottom: 8px;\\n    }\\n\\n    .action-buttons[_ngcontent-%COMP%] {\\n      margin-top: 30px;\\n      display: flex;\\n      gap: 16px;\\n      justify-content: center;\\n      flex-wrap: wrap;\\n    }\\n\\n    .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n      min-width: 150px;\\n    }\\n\\n    @media (max-width: 600px) {\\n      .disable-confirm-container[_ngcontent-%COMP%] {\\n        padding: 10px;\\n      }\\n\\n      .disable-confirm-card[_ngcontent-%COMP%] {\\n        padding: 20px;\\n      }\\n\\n      .action-buttons[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        align-items: center;\\n      }\\n\\n      .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n        width: 100%;\\n        max-width: 250px;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "userInfo", "email", "ɵɵtextInterpolate2", "firstName", "lastName", "ɵɵtemplate", "Disable2FAConfirmComponent_div_3_div_7_Template", "ɵɵlistener", "Disable2FAConfirmComponent_div_3_Template_button_click_24_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "goToDashboard", "Disable2FAConfirmComponent_div_3_Template_button_click_28_listener", "goToProfile", "ɵɵproperty", "Disable2FAConfirmComponent_div_4_Template_button_click_18_listener", "_r3", "goToLogin", "Disable2FAConfirmComponent_div_4_Template_button_click_22_listener", "requestNewLink", "ɵɵtextInterpolate", "errorMessage", "Disable2FAConfirmComponent", "constructor", "route", "router", "twoFactorService", "authService", "snackBar", "loading", "success", "ngOnInit", "token", "snapshot", "queryParamMap", "get", "confirmDisable2FA", "subscribe", "next", "response", "user", "currentUserValue", "updatedUser", "twoFactorEnabled", "open", "duration", "panelClass", "message", "error", "console", "status", "navigate", "queryParams", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "TwoFactorService", "i3", "AuthService", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "Disable2FAConfirmComponent_Template", "rf", "ctx", "Disable2FAConfirmComponent_div_2_Template", "Disable2FAConfirmComponent_div_3_Template", "Disable2FAConfirmComponent_div_4_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\disable-2fa-confirm\\disable-2fa-confirm.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { TwoFactorService } from '../../../services/two-factor.service';\r\nimport { AuthService } from '../../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-disable-2fa-confirm',\r\n  template: `\r\n    <div class=\"disable-confirm-container\">\r\n      <mat-card class=\"disable-confirm-card\">\r\n        <!-- Loading State -->\r\n        <div *ngIf=\"loading\" class=\"loading-state\">\r\n          <mat-spinner diameter=\"60\"></mat-spinner>\r\n          <h2>Processing your request...</h2>\r\n          <p>Please wait while we disable two-factor authentication on your account.</p>\r\n        </div>\r\n\r\n        <!-- Success State -->\r\n        <div *ngIf=\"!loading && success\" class=\"success-state\">\r\n          <mat-icon class=\"success-icon\">check_circle</mat-icon>\r\n          <h2>Two-Factor Authentication Disabled</h2>\r\n          <p class=\"success-message\">\r\n            Two-factor authentication has been successfully disabled on your account.\r\n          </p>\r\n          \r\n          <div class=\"user-info\" *ngIf=\"userInfo\">\r\n            <h3>Account Details:</h3>\r\n            <p><strong>Email:</strong> {{ userInfo.email }}</p>\r\n            <p><strong>Name:</strong> {{ userInfo.firstName }} {{ userInfo.lastName }}</p>\r\n          </div>\r\n\r\n          <div class=\"security-notice\">\r\n            <mat-icon class=\"warning-icon\">warning</mat-icon>\r\n            <div class=\"notice-content\">\r\n              <h4>Important Security Notice</h4>\r\n              <p>Your account is now less secure without two-factor authentication. Consider:</p>\r\n              <ul>\r\n                <li>Using a strong, unique password</li>\r\n                <li>Re-enabling 2FA from your profile settings</li>\r\n                <li>Monitoring your account for any suspicious activity</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"action-buttons\">\r\n            <button mat-raised-button color=\"primary\" (click)=\"goToDashboard()\">\r\n              <mat-icon>dashboard</mat-icon>\r\n              Go to Dashboard\r\n            </button>\r\n            <button mat-button (click)=\"goToProfile()\">\r\n              <mat-icon>settings</mat-icon>\r\n              Profile Settings\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Error State -->\r\n        <div *ngIf=\"!loading && !success\" class=\"error-state\">\r\n          <mat-icon class=\"error-icon\">error</mat-icon>\r\n          <h2>Unable to Disable 2FA</h2>\r\n          <p class=\"error-message\">{{ errorMessage }}</p>\r\n          \r\n          <div class=\"error-help\">\r\n            <h4>What you can do:</h4>\r\n            <ul>\r\n              <li>Check if the link has expired (links are valid for 1 hour)</li>\r\n              <li>Request a new disable email from the login screen</li>\r\n              <li>Contact support if you continue to have issues</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div class=\"action-buttons\">\r\n            <button mat-raised-button color=\"primary\" (click)=\"goToLogin()\">\r\n              <mat-icon>login</mat-icon>\r\n              Back to Login\r\n            </button>\r\n            <button mat-button (click)=\"requestNewLink()\">\r\n              <mat-icon>email</mat-icon>\r\n              Request New Link\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .disable-confirm-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      padding: 20px;\r\n    }\r\n\r\n    .disable-confirm-card {\r\n      max-width: 600px;\r\n      width: 100%;\r\n      padding: 40px;\r\n      text-align: center;\r\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\r\n    }\r\n\r\n    .loading-state {\r\n      padding: 20px;\r\n    }\r\n\r\n    .loading-state h2 {\r\n      margin: 20px 0 10px 0;\r\n      color: #333;\r\n    }\r\n\r\n    .loading-state p {\r\n      color: #666;\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    .success-state, .error-state {\r\n      padding: 20px;\r\n    }\r\n\r\n    .success-icon {\r\n      font-size: 80px;\r\n      width: 80px;\r\n      height: 80px;\r\n      color: #4caf50;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .error-icon {\r\n      font-size: 80px;\r\n      width: 80px;\r\n      height: 80px;\r\n      color: #f44336;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .success-state h2, .error-state h2 {\r\n      margin: 0 0 20px 0;\r\n      color: #333;\r\n    }\r\n\r\n    .success-message, .error-message {\r\n      font-size: 16px;\r\n      color: #666;\r\n      margin-bottom: 30px;\r\n      line-height: 1.6;\r\n    }\r\n\r\n    .user-info {\r\n      background: #f8f9fa;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin: 20px 0;\r\n      text-align: left;\r\n    }\r\n\r\n    .user-info h3 {\r\n      margin: 0 0 15px 0;\r\n      color: #333;\r\n      font-size: 16px;\r\n    }\r\n\r\n    .user-info p {\r\n      margin: 8px 0;\r\n      color: #555;\r\n    }\r\n\r\n    .security-notice {\r\n      background: #fff3e0;\r\n      border: 1px solid #ff9800;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin: 20px 0;\r\n      display: flex;\r\n      align-items: flex-start;\r\n      gap: 16px;\r\n      text-align: left;\r\n    }\r\n\r\n    .warning-icon {\r\n      color: #ff9800;\r\n      font-size: 32px;\r\n      width: 32px;\r\n      height: 32px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .notice-content h4 {\r\n      margin: 0 0 10px 0;\r\n      color: #f57c00;\r\n      font-size: 16px;\r\n    }\r\n\r\n    .notice-content p {\r\n      margin: 0 0 10px 0;\r\n      color: #e65100;\r\n    }\r\n\r\n    .notice-content ul {\r\n      margin: 0;\r\n      padding-left: 20px;\r\n      color: #e65100;\r\n    }\r\n\r\n    .notice-content li {\r\n      margin-bottom: 5px;\r\n    }\r\n\r\n    .error-help {\r\n      background: #ffebee;\r\n      border: 1px solid #f44336;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin: 20px 0;\r\n      text-align: left;\r\n    }\r\n\r\n    .error-help h4 {\r\n      margin: 0 0 10px 0;\r\n      color: #d32f2f;\r\n      font-size: 16px;\r\n    }\r\n\r\n    .error-help ul {\r\n      margin: 0;\r\n      padding-left: 20px;\r\n      color: #c62828;\r\n    }\r\n\r\n    .error-help li {\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .action-buttons {\r\n      margin-top: 30px;\r\n      display: flex;\r\n      gap: 16px;\r\n      justify-content: center;\r\n      flex-wrap: wrap;\r\n    }\r\n\r\n    .action-buttons button {\r\n      min-width: 150px;\r\n    }\r\n\r\n    @media (max-width: 600px) {\r\n      .disable-confirm-container {\r\n        padding: 10px;\r\n      }\r\n\r\n      .disable-confirm-card {\r\n        padding: 20px;\r\n      }\r\n\r\n      .action-buttons {\r\n        flex-direction: column;\r\n        align-items: center;\r\n      }\r\n\r\n      .action-buttons button {\r\n        width: 100%;\r\n        max-width: 250px;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class Disable2FAConfirmComponent implements OnInit {\r\n  loading = true;\r\n  success = false;\r\n  errorMessage = '';\r\n  userInfo: any = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private twoFactorService: TwoFactorService,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const token = this.route.snapshot.queryParamMap.get('token');\r\n    \r\n    if (!token) {\r\n      this.loading = false;\r\n      this.success = false;\r\n      this.errorMessage = 'Invalid or missing disable token. Please request a new disable link.';\r\n      return;\r\n    }\r\n\r\n    this.confirmDisable2FA(token);\r\n  }\r\n\r\n  confirmDisable2FA(token: string): void {\r\n    this.twoFactorService.confirmDisable2FA(token).subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        this.success = response.success;\r\n        this.userInfo = response.user;\r\n        \r\n        if (response.success) {\r\n          // Update current user if they're logged in\r\n          if (this.authService.currentUserValue) {\r\n            const updatedUser = { ...this.authService.currentUserValue };\r\n            updatedUser.twoFactorEnabled = false;\r\n            // Update the user in auth service if needed\r\n          }\r\n          \r\n          this.snackBar.open(\r\n            'Two-factor authentication has been disabled successfully.',\r\n            'Close',\r\n            { duration: 5000, panelClass: ['success-snackbar'] }\r\n          );\r\n        } else {\r\n          this.errorMessage = response.message || 'Failed to disable 2FA';\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error confirming 2FA disable:', error);\r\n        this.loading = false;\r\n        this.success = false;\r\n        \r\n        if (error.status === 400) {\r\n          this.errorMessage = 'The disable link has expired or is invalid. Please request a new one.';\r\n        } else {\r\n          this.errorMessage = error.error?.message || 'An unexpected error occurred. Please try again.';\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  goToDashboard(): void {\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n\r\n  goToProfile(): void {\r\n    this.router.navigate(['/profile']);\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  requestNewLink(): void {\r\n    this.router.navigate(['/auth/login'], {\r\n      queryParams: { message: 'Please use the \"Request disable 2FA\" option to get a new link.' }\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;IAYQA,EAAA,CAAAC,cAAA,aAA2C;IACzCD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8EAAuE;IAC5EH,EAD4E,CAAAI,YAAA,EAAI,EAC1E;;;;;IAWFJ,EADF,CAAAC,cAAA,cAAwC,SAClC;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAH,CAAAC,cAAA,QAAG,aAAQ;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAH,CAAAC,cAAA,QAAG,aAAQ;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,IAAgD;IAC5EH,EAD4E,CAAAI,YAAA,EAAI,EAC1E;;;;IAFuBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,QAAA,CAAAC,KAAA,CAAoB;IACrBT,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAC,QAAA,CAAAG,SAAA,OAAAJ,MAAA,CAAAC,QAAA,CAAAI,QAAA,CAAgD;;;;;;IAT5EZ,EADF,CAAAC,cAAA,aAAuD,kBACtB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACtDJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,yCAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3CJ,EAAA,CAAAC,cAAA,WAA2B;IACzBD,EAAA,CAAAG,MAAA,kFACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAa,UAAA,IAAAC,+CAAA,mBAAwC;IAOtCd,EADF,CAAAC,cAAA,cAA6B,mBACI;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE/CJ,EADF,CAAAC,cAAA,eAA4B,UACtB;IAAAD,EAAA,CAAAG,MAAA,iCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,oFAA4E;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjFJ,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAG,MAAA,uCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,kDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,2DAAmD;IAG7DH,EAH6D,CAAAI,YAAA,EAAK,EACzD,EACD,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA4B,kBAC0C;IAA1BD,EAAA,CAAAe,UAAA,mBAAAC,mEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IACjErB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA2C;IAAxBD,EAAA,CAAAe,UAAA,mBAAAO,mEAAA;MAAAtB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAgB,WAAA,EAAa;IAAA,EAAC;IACxCvB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,0BACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IA7BoBJ,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAwB,UAAA,SAAAjB,MAAA,CAAAC,QAAA,CAAc;;;;;;IAiCtCR,EADF,CAAAC,cAAA,cAAsD,mBACvB;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7CJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG7CJ,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEvBJ,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAG,MAAA,kEAA0D;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnEJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,yDAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1DJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,sDAA8C;IAEtDH,EAFsD,CAAAI,YAAA,EAAK,EACpD,EACD;IAGJJ,EADF,CAAAC,cAAA,eAA4B,kBACsC;IAAtBD,EAAA,CAAAe,UAAA,mBAAAU,mEAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAS,GAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAoB,SAAA,EAAW;IAAA,EAAC;IAC7D3B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8C;IAA3BD,EAAA,CAAAe,UAAA,mBAAAa,mEAAA;MAAA5B,EAAA,CAAAiB,aAAA,CAAAS,GAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAsB,cAAA,EAAgB;IAAA,EAAC;IAC3C7B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,0BACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IArBqBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA8B,iBAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAkB;;;AA+MrD,OAAM,MAAOC,0BAA0B;EAMrCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,gBAAkC,EAClCC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAVlB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAT,YAAY,GAAG,EAAE;IACjB,KAAAvB,QAAQ,GAAQ,IAAI;EAQjB;EAEHiC,QAAQA,CAAA;IACN,MAAMC,KAAK,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC;IAE5D,IAAI,CAACH,KAAK,EAAE;MACV,IAAI,CAACH,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,OAAO,GAAG,KAAK;MACpB,IAAI,CAACT,YAAY,GAAG,sEAAsE;MAC1F;IACF;IAEA,IAAI,CAACe,iBAAiB,CAACJ,KAAK,CAAC;EAC/B;EAEAI,iBAAiBA,CAACJ,KAAa;IAC7B,IAAI,CAACN,gBAAgB,CAACU,iBAAiB,CAACJ,KAAK,CAAC,CAACK,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACV,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,OAAO,GAAGS,QAAQ,CAACT,OAAO;QAC/B,IAAI,CAAChC,QAAQ,GAAGyC,QAAQ,CAACC,IAAI;QAE7B,IAAID,QAAQ,CAACT,OAAO,EAAE;UACpB;UACA,IAAI,IAAI,CAACH,WAAW,CAACc,gBAAgB,EAAE;YACrC,MAAMC,WAAW,GAAG;cAAE,GAAG,IAAI,CAACf,WAAW,CAACc;YAAgB,CAAE;YAC5DC,WAAW,CAACC,gBAAgB,GAAG,KAAK;YACpC;UACF;UAEA,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAChB,2DAA2D,EAC3D,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,kBAAkB;UAAC,CAAE,CACrD;QACH,CAAC,MAAM;UACL,IAAI,CAACzB,YAAY,GAAGkB,QAAQ,CAACQ,OAAO,IAAI,uBAAuB;QACjE;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACnB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,OAAO,GAAG,KAAK;QAEpB,IAAIkB,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;UACxB,IAAI,CAAC7B,YAAY,GAAG,uEAAuE;QAC7F,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,GAAG2B,KAAK,CAACA,KAAK,EAAED,OAAO,IAAI,iDAAiD;QAC/F;MACF;KACD,CAAC;EACJ;EAEApC,aAAaA,CAAA;IACX,IAAI,CAACc,MAAM,CAAC0B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAtC,WAAWA,CAAA;IACT,IAAI,CAACY,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAlC,SAASA,CAAA;IACP,IAAI,CAACQ,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAhC,cAAcA,CAAA;IACZ,IAAI,CAACM,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;MACpCC,WAAW,EAAE;QAAEL,OAAO,EAAE;MAAgE;KACzF,CAAC;EACJ;EAAC,QAAAM,CAAA,G;qCAjFU/B,0BAA0B,EAAAhC,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAnE,EAAA,CAAAgE,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAArE,EAAA,CAAAgE,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAvE,EAAA,CAAAgE,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1B1C,0BAA0B;IAAA2C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAlQjCjF,EADF,CAAAC,cAAA,aAAuC,kBACE;QAgDrCD,EA9CA,CAAAa,UAAA,IAAAsE,yCAAA,iBAA2C,IAAAC,yCAAA,kBAOY,IAAAC,yCAAA,kBAuCD;QA0B1DrF,EADE,CAAAI,YAAA,EAAW,EACP;;;QAxEIJ,EAAA,CAAAK,SAAA,GAAa;QAAbL,EAAA,CAAAwB,UAAA,SAAA0D,GAAA,CAAA3C,OAAA,CAAa;QAObvC,EAAA,CAAAK,SAAA,EAAyB;QAAzBL,EAAA,CAAAwB,UAAA,UAAA0D,GAAA,CAAA3C,OAAA,IAAA2C,GAAA,CAAA1C,OAAA,CAAyB;QAuCzBxC,EAAA,CAAAK,SAAA,EAA0B;QAA1BL,EAAA,CAAAwB,UAAA,UAAA0D,GAAA,CAAA3C,OAAA,KAAA2C,GAAA,CAAA1C,OAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}