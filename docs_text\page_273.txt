=== Page 274 ===

274notice how 5 is in the list
Checks to see the date and time passed is in the past. This will pass even if the datetime
is 5 minutes in the past.
You may also pass in a timezone for this rule:
This will negate all rules. So if you need to get the opposite of any of these rules you will
add them as rules inside this rule.
For example to get the opposite if is_in you will do:"""
{
  'date': '2019-10-20', # Or date in the future
}
"""
validate.is_past('date')
"""
{
  'date': '2019-10-20', # Or date in the future
}
"""
validate.is_past('date', tz='America/New_York')
"""
{
  'age': 5
}
"""
validate.isnt(
  validate.is_in('age', [2,4,5])
)Is_past
Isnt6/12/25, 3:02 AM Masonite Documentation