import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {GeneratedDocumentRepository, CrawledContentRepository} from '../repositories';
import {GeneratedDocument, CrawledContent} from '../models';
import {spawn, ChildProcess} from 'child_process';
import * as path from 'path';
import * as fs from 'fs/promises';
import * as crypto from 'crypto';

export interface DocumentGenerationOptions {
  format: 'pdf' | 'docx' | 'markdown' | 'html' | 'txt';
  organizationType: 'single_file' | 'separate_files' | 'grouped_folders';
  selectedContentIds: string[];
  destinationFolder?: string;
  template?: string;
  includeImages?: boolean;
  includeToc?: boolean;
  customStyles?: object;
  metadata?: object;
}

export interface DocumentGenerationProgress {
  documentId: string;
  status: string;
  processedPages: number;
  totalPages: number;
  currentPage?: string;
  errorMessage?: string;
  filePath?: string;
}

@injectable({scope: BindingScope.SINGLETON})
export class DocumentGeneratorService {
  private activeGenerators: Map<string, ChildProcess> = new Map();
  private generatorScriptPath: string;
  private outputDirectory: string;

  constructor(
    @repository(GeneratedDocumentRepository)
    public generatedDocumentRepository: GeneratedDocumentRepository,
    @repository(CrawledContentRepository)
    public crawledContentRepository: CrawledContentRepository,
  ) {
    // Path to the Python document generator script
    this.generatorScriptPath = path.join(__dirname, '../../scripts/document_generator.py');
    this.outputDirectory = path.join(__dirname, '../../storage/generated_documents');
    this.ensureOutputDirectory();
  }

  /**
   * Ensure output directory exists
   */
  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.access(this.outputDirectory);
    } catch {
      await fs.mkdir(this.outputDirectory, {recursive: true});
    }
  }

  /**
   * Generate document from crawled content
   */
  async generateDocument(
    crawlJobId: string,
    userId: string,
    options: DocumentGenerationOptions,
  ): Promise<GeneratedDocument> {
    // Create document record
    const filename = this.generateFilename(options.format, options.organizationType);
    const accessToken = this.generateAccessToken();
    
    const document = await this.generatedDocumentRepository.create({
      crawlJobId,
      userId,
      filename,
      format: options.format,
      status: 'pending',
      organizationType: options.organizationType,
      selectedContentIds: options.selectedContentIds,
      generationOptions: options,
      destinationFolder: options.destinationFolder,
      accessToken,
      expiresAt: this.calculateExpiryDate(),
    });

    // Start generation process
    await this.startGenerationProcess(document.id, options);

    return document;
  }

  /**
   * Start document generation process
   */
  private async startGenerationProcess(
    documentId: string,
    options: DocumentGenerationOptions,
  ): Promise<void> {
    try {
      // Update status to generating
      await this.generatedDocumentRepository.updateById(documentId, {
        status: 'generating',
        startedAt: new Date(),
        updatedAt: new Date(),
      });

      // Get selected content
      const selectedContent = await this.getSelectedContent(options.selectedContentIds);
      
      // Prepare generation data
      const generationData = {
        documentId,
        options,
        content: selectedContent,
        outputDirectory: this.outputDirectory,
      };

      // Start Python generator process
      await this.spawnGeneratorProcess(documentId, generationData);

    } catch (error) {
      await this.handleGenerationError(documentId, error);
    }
  }

  /**
   * Get selected content for document generation
   */
  private async getSelectedContent(contentIds: string[]): Promise<CrawledContent[]> {
    const contents = await this.crawledContentRepository.find({
      where: {
        id: {inq: contentIds},
        status: 'completed',
      },
      order: ['depth ASC', 'createdAt ASC'],
    });

    return contents;
  }

  /**
   * Spawn Python document generator process
   */
  private async spawnGeneratorProcess(
    documentId: string,
    generationData: any,
  ): Promise<void> {
    const args = [
      this.generatorScriptPath,
      '--document-id', documentId,
      '--data', JSON.stringify(generationData),
      '--callback-url', `http://localhost:3002/api/document-generator/callback`,
    ];

    const generator = spawn('python', args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        PYTHONPATH: path.join(__dirname, '../../scripts'),
      },
    });

    this.activeGenerators.set(documentId, generator);

    // Handle generator output
    generator.stdout.on('data', (data) => {
      console.log(`Generator ${documentId} stdout:`, data.toString());
      this.handleGeneratorOutput(documentId, data.toString());
    });

    generator.stderr.on('data', (data) => {
      console.error(`Generator ${documentId} stderr:`, data.toString());
    });

    generator.on('close', (code) => {
      console.log(`Generator ${documentId} exited with code ${code}`);
      this.activeGenerators.delete(documentId);
      this.handleGeneratorExit(documentId, code || 0);
    });

    generator.on('error', (error) => {
      console.error(`Generator ${documentId} error:`, error);
      this.activeGenerators.delete(documentId);
      this.handleGenerationError(documentId, error);
    });
  }

  /**
   * Handle generator output for progress updates
   */
  private async handleGeneratorOutput(documentId: string, output: string): Promise<void> {
    try {
      const lines = output.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (line.startsWith('PROGRESS:')) {
          const progressData = JSON.parse(line.substring(9));
          await this.updateGenerationProgress(documentId, progressData);
        } else if (line.startsWith('COMPLETED:')) {
          const completionData = JSON.parse(line.substring(10));
          await this.handleGenerationCompletion(documentId, completionData);
        }
      }
    } catch (error) {
      console.error('Error parsing generator output:', error);
    }
  }

  /**
   * Handle generator process exit
   */
  private async handleGeneratorExit(documentId: string, code: number): Promise<void> {
    if (code !== 0) {
      await this.handleGenerationError(documentId, new Error(`Generator exited with code ${code}`));
    }
  }

  /**
   * Handle generation errors
   */
  private async handleGenerationError(documentId: string, error: any): Promise<void> {
    await this.generatedDocumentRepository.updateById(documentId, {
      status: 'failed',
      completedAt: new Date(),
      updatedAt: new Date(),
      errorMessage: error.message || 'Unknown error occurred',
    });
  }

  /**
   * Update generation progress
   */
  private async updateGenerationProgress(documentId: string, progressData: any): Promise<void> {
    await this.generatedDocumentRepository.updateProgress(
      documentId,
      progressData.processedPages,
      progressData.totalPages,
      progressData.status,
    );
  }

  /**
   * Handle generation completion
   */
  private async handleGenerationCompletion(documentId: string, completionData: any): Promise<void> {
    const filePath = completionData.filePath;
    const fileSize = completionData.fileSize;
    const downloadUrl = this.generateDownloadUrl(documentId);

    await this.generatedDocumentRepository.updateById(documentId, {
      status: 'completed',
      filePath,
      fileSize,
      downloadUrl,
      completedAt: new Date(),
      updatedAt: new Date(),
      progressPercentage: 100,
    });
  }

  /**
   * Get document generation progress
   */
  async getGenerationProgress(documentId: string): Promise<DocumentGenerationProgress> {
    const document = await this.generatedDocumentRepository.findById(documentId);
    
    return {
      documentId,
      status: document.status,
      processedPages: document.processedPages,
      totalPages: document.totalPages,
      errorMessage: document.errorMessage,
      filePath: document.filePath,
    };
  }

  /**
   * Cancel document generation
   */
  async cancelGeneration(documentId: string): Promise<void> {
    const generator = this.activeGenerators.get(documentId);
    if (generator) {
      generator.kill('SIGTERM');
      this.activeGenerators.delete(documentId);
    }
    
    await this.generatedDocumentRepository.updateById(documentId, {
      status: 'failed',
      completedAt: new Date(),
      updatedAt: new Date(),
      errorMessage: 'Generation cancelled by user',
    });
  }

  /**
   * Generate filename for document
   */
  private generateFilename(format: string, organizationType: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const prefix = organizationType === 'single_file' ? 'website-content' : 'website-content-collection';
    return `${prefix}-${timestamp}.${format}`;
  }

  /**
   * Generate access token for document download
   */
  private generateAccessToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Calculate document expiry date (30 days from now)
   */
  private calculateExpiryDate(): Date {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30);
    return expiryDate;
  }

  /**
   * Generate download URL for document
   */
  private generateDownloadUrl(documentId: string): string {
    return `/api/documents/${documentId}/download`;
  }

  /**
   * Get active generators count
   */
  getActiveGeneratorsCount(): number {
    return this.activeGenerators.size;
  }

  /**
   * Get all active generator document IDs
   */
  getActiveGeneratorDocumentIds(): string[] {
    return Array.from(this.activeGenerators.keys());
  }
}
