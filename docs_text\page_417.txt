=== Page 418 ===

418The request class was one of the first classes that was created for Masonite. There
hasen't been much that changed on the class so the class slowly got larger and larger
and took on more responsibility.
One of the things that the class was used for, like the headers, was the response status
code. It did not make sense to set the response status code on the request class so now
the response status is whatever status is set on the response class. Requests don't have
status codes so we removed the status code on the request class all together.
Every Masonite project has had a bootstrap/start.py file. This is a low level method in the
masonite app that really handles the entire application and responsible for the final
response. It is the entry point everytime a request is received. Many of you were probably
not even aware it was there or were confused on what it did.
Since the method was low level and should never be changed we moved this method into
the Masonite codebase and instead of an import from the local bootstrap.start 
import app it is now an import from the masonite codebase from masonite.wsgi 
import response_handler
It is largely the same exact method but is now maintained by the masonite framework.
Masonite queueing had a simple table with not a lot of options on it. Because of this we
had to make some decisions to prevent the duplication of some jobs. Like sleeping and
only fetching 1 job per poll. This made the queue slower than what it should have been.
So now we added more columns on the queue jobs table that will allow us to reserve
some jobs to prevent duplication in other queue workers. If you still experienceClass Responsibility
Start Method
Queue improvements6/12/25, 3:02 AM Masonite Documentation