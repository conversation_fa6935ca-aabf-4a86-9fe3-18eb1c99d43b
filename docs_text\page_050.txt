=== Page 51 ===

51For convenience, you can provide the method class instead:
You can also bind the route to a controller instance:
Here as no method has been defined the __call__ method of the class will be bound to
this route. It means that you should define this method in your controller:
You may define several available methods on your routes to modify their behavior during
the request.class WelcomeController(Controller):
    def __call__(self, request:Request):
        return "Welcome"
from app.controllers import WelcomeController
Route.get('/welcome', WelcomeController.show)
from app.controllers import WelcomeController
controller = WelcomeController()
Route.get('/welcome', controller)
class WelcomeController(Controller):
    def __call__(self, request:Request):
        return "Welcome"Instance Binding
Route Options
Middlewares6/12/25, 3:02 AM Masonite Documentation