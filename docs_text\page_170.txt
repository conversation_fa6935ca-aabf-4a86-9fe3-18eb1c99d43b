=== Page 171 ===

171Filesystem and Uploading
Masonite comes with a simple way to upload files to different file systems.
In Masonite, a Filesystem is a place where assets are stored. These locations could be
somewhere local on the server or in a cloud storage service like Amazon S3.
The configuration for the filesystem feature is broken up into "disks". These "disks" or
connection configurations can be any name you want.
Here is an example configuration:
The default configuration is the name of the disk you want to be used as the default when
using the Filesystem features.DISKS = {
    "default": "local",
    "local": {
        "driver": "file",
        "path": os.path.join(os.getcwd(), 
"storage/framework/filesystem")
        #
    },
    "s3": {
        "driver": "s3",
        "client": os.getenv("AWS_CLIENT"),
        "secret": os.getenv("AWS_SECRET"),
        "bucket": os.getenv("AWS_BUCKET"),
          "path": "https://bucket.s3.us-east-2.amazonaws.com"
    },
}Configuration
Default6/12/25, 3:02 AM Masonite Documentation