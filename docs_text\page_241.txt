=== Page 242 ===

242By default your app models are loaded from the location configured in your project
Kernel. You can override the directory to load models from with the -d flag. It should be
a path relative to your project root. For example you can run the following command if
your models are located in a models/ folder located at your project root:
You can use PYTHONSTARTUP environment variable to add a script that you want to run at
the beginning of the shell session.
With IPython you can use this variable or put some Python scripts in 
~/.ipython/profile_default/startup/. IPython will run those scripts for you at the
beginning of the shell session.python craft tinker -d models/
Startup script6/12/25, 3:02 AM Masonite Documentation