=== Page 136 ===

136To be able to receive broadcast events in the browser you should install Javascript
Pusher SDK.
Include the pusher-js script tag on your page
This is the quickest way to install <PERSON>ush<PERSON>. But for a real app you will often use a build system
to install and compile assets and will install the Javascript Pusher SDK with npm install 
pusher-js and then import Pusher class with import Pusher from 'pusher-js';.
Create a Pusher instance configured with your credentials
It is advised to use environment variables instead of hard-coding credentials client-side. If
you're using Laravel Mix to compile assets then you should prefix your environment
variables with MIX_.
Now you're ready to subscribe to channels and listen for channels events.
Broadcast events are simple classes that inherit from CanBroadcast. You may use any
class, including the Masonite Event classes.
A broadcast event will look like this:pip install pusher
<script src="https://js.pusher.com/7.0.3/pusher.min.js"></script>
const pusher = new Pusher("478b45309560f3456211", {
  cluster: "eu",
});
Client Side
Creating Events6/12/25, 3:02 AM Masonite Documentation