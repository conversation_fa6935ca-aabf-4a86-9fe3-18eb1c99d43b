=== Page 377 ===

377There is a new command that starts a Python shell and imports the container for you
already. Test it out to verify that objects are loaded into your container correctly. It's a
great debugging tool.
Read more in The Craft Command Introduction documentation.
Masonite 2 ships with an awesome little helper command that allows you to see all the
routes in your application
Read more in The Craft Command Introduction documentation.
A huge update to Masonite is the new --reload flag on the serve command. Now the
server will automatically restart when it detects a file change. You can use the -r flag
as a shorthand:
Read more in The Craft Command Introduction documentation.$ craft tinker
$ craft show:routes
$ craft serve -r
Show Routes Command
Server Reloading
Autoloading6/12/25, 3:02 AM Masonite Documentation