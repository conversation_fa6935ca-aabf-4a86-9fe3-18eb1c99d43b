=== Page 427 ===

427Masonite 1.5 to 1.6
Not much has changed in the actual project structure of Masonite 1.6 so we just need to
make some minor changes to our existing 1.5 project
We just have to change our Masonite dependency version in this file:
Masonite 1.6 now wraps the majority of the application in a try and catch block so we can
add exception handling such as the new debug view and other exception features down
the road such as Sentry and other error logging.
In the middle of the file (line 45 if you have not made changes to this file) we can simply
wrap the application:
This will also display an exception view whenever an exception is it....
masonite>=1.6,<=1.6.99
try:
    for provider in container.make('Application').PROVIDERS:
        located_provider = locate(provider)().load_app(container)
        if located_provider.wsgi is True:
            container.resolve(located_provider.boot)
except Exception as e:
    container.make('ExceptionHandler').load_exception(e)Introduction
Requirements.txt
Bootstrap/start.py6/12/25, 3:02 AM Masonite Documentation