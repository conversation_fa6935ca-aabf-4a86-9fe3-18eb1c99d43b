=== Page 165 ===

165The handle method will run when the listener runs. It will pass the event as the first
parameter and any additional arguments that are emitted from the event as additional
parameters.
After your events and listeners are created you will need to register them to the event
class.
You can do this via the AppProvider or a Service Provider you will create yourself:
You can also listen to events without Event classes:
Using event strings allow to use wildcard event listening. For example if the application is
emitting multiple events related to users such as users.added, users.updated and 
users.deleted you can listen to all of those events at once:class EventsProvider(Provider):
    def register(self):
        self.application.make('event').listen(UserAddedEvent, 
[WelcomeListener])
class EventsProvider(Provider):
    def register(self):
        self.application.make('event').listen("users.added", 
[WelcomeListener])
event.listen("users.*", [UsersListener])Handle Method
Registering Events and Listeners
Firing Events6/12/25, 3:02 AM Masonite Documentation