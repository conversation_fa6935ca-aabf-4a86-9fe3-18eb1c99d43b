=== Page 3 ===

3In order to use Masonite, you’ll need:
•Python <= 3.11
•Latest version of OpenSSL
•Pip3
All commands of python and pip in this documentation is assuming they are pointing to the
correct Python 3 versions. For example, anywhere you see the python command ran it is
assuming that is a Python 3.7+ Python installation. If you are having issues with any
installation steps just be sure the commands are for Python 3.7+ and not 2.7 or below.
If you are running on a Linux flavor, you’ll need the Python dev package and the libssl
package. You can download these packages by running:
Debian and Ubuntu based Linux distributions
Or you may need to specify your python3.x-dev version:
Enterprise Linux based distributions (Fedora, CentOS, RHEL, ...)
$ sudo apt install python3-dev python3-pip libssl-dev build-essential 
python3-venv
$ sudo apt-get install python3.7-dev python3-pip libssl-dev build-
essential python3-venv
# dnf install python-devel openssl-develLinux
Installationterminal
terminal
terminal6/12/25, 3:02 AM Masonite Documentation