=== Page 26 ===

26Go ahead and run the server and head over to http://localhost:8000/posts route.
You should see a basic representation of your posts. If you only see 1, go to 
http://localhost:8000/blog to create more so we can show an individual post.
Showing The Author
Remember we made our author relationship before. Masonite ORM will take that
relationship and make an attribute from it so we can display the author's name as well:
Let's repeat the process but change our workflow a bit.
Next we want to just show a single post. We need to add a route for this method:<!-- templates/posts.html -->
@for post in posts
    {{ post.title }}
    <br>
    {{ post.body }}
    <hr>
@endfor
templates/posts.html
@for post in posts
    {{ post.title }} by {{ post.author.name }}
    <br>
    {{ post.body }}
    <hr>
@endfor
routes/web.py
Route.get('/post/@id', 'PostController@single')Single Post Route6/12/25, 3:02 AM Masonite Documentation