=== Page 131 ===

131During the gate authorization process, before and after hooks can be triggered.
A before hook can be added like this:
The after hook works the same way:
If the after callback is returning a value it will take priority over the gate result check.
Policies are classes that organize authorization logic around a specific model.
You can run the craft command:
You can also create a policy with a set of predefined gates by using the --model flag:
A model policy comes with common actions that we can perform on a model:# here admin users will always be authorized based on the boolean value 
of this response
Gate.before(lambda user, permission : user.role == "admin")
Gate.after(lambda user, permission, result : user.role == "admin")
$ python craft policy AccessAdmin
$ python craft policy Post --modelGate Hooks
Policies
Creating Policies6/12/25, 3:02 AM Masonite Documentation