=== Page 104 ===

104mode is enabled (APP_ENV=True). It should stay enabled for local development.
When debug mode is enabled all exceptions (or routes not found) are rendered as an
HTML debug error page containing a lot of information to help you debug the problem.
When disabled, the default 500, 404, 403 error pages are rendered.
You can check if debug mode is enabled through the Masonite app is_debug() helper
or with the config helper:
Never deploy an application in production with debug mode enabled ! This could lead to
expose some sensitive configuration data and environment variables to the end user.app.is_debug() #== True
from masonite.configuration import config
config("application.debug") #== True
6/12/25, 3:02 AM Masonite Documentation