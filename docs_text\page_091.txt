=== Page 92 ===

92It's important to note that Jinja2 statements can be rewritten with line statements and
line statements are preferred in Masonite. In comparison to Jinja2 line statements
evaluate the whole line, thus the name line statement.
So Jinja2 syntax looks like this:
This can be rewritten like this with line statement syntax:
It's important to note though that these are line statements. Meaning nothing else can be
on the line when doing these. For example you CANNOT do this:
But you could achieve that with the regular formatting:
Whichever syntax you choose is up to you.<div data-gb-custom-block data-tag="if">
    <p>do something</p>
</div>
@if expression
    <p>do something</p>
@endif
<form action="@if expression: 'something' @endif">
</form>
<form action="
<div data-gb-custom-block data-tag="if"> 'something' </div>">
</form>Line Statements6/12/25, 3:02 AM Masonite Documentation