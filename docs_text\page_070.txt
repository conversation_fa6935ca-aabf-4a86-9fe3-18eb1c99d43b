=== Page 71 ===

71This will set the header on the response.
You can set the status on the response simply:
You can also very simply download assets like images, PDF's or other files:
This will set all the proper headers required and render the file in the browser.
When setting the name, the file extension will be picked up from the file type. This
example will download the invoice as the name invoice-2021-01.pdf
If you want to force download it, or automatically download the file when the response in
rendered, you can add a force parameter and set it to True:from masonite.response import Response
def show(self, response: Response):
response.header('X-Custom', 'value')
from masonite.response import Response
def show(self, response: Response):
response.status(409)
def show(self, response: Response):
  return response.download("invoice-2021-01", "path/to/invoice.pdf")
def show(self, response: Response):
    return response.download("invoice-2021-01", "path/to/invoice.pdf", 
force=True)Status
Downloading6/12/25, 3:02 AM Masonite Documentation