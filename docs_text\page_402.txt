=== Page 403 ===

403Learn more in the Validation documentation here.
Previously we needed to pass in the request object to the Auth class like this:
Now we have it a bit cleaner and you can just resolve it and the request class will be
injected for you
You may not notice anything but now if you bind a class into the container like this:
It will be resolved when you resolve it:
from masonite.auth import Auth
from masonite.request import Request
def show(self, request: Request):
    Auth(request).login(..)
from masonite.auth import Auth
from masonite.request import Request
def show(self, request: Request, auth: Auth):
    auth.login(..)
from masonite.auth import Auth
def register(self):
    self.app.bind('Auth', Auth)Auth class does not need the request class.
Completely changed how classes are resolved
on the backend6/12/25, 3:02 AM Masonite Documentation