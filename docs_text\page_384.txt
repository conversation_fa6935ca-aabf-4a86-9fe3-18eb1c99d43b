=== Page 385 ===

385which was kind of strange in hindsight. Now we can just straight up use the input:
Again this is only a change for incoming JSON responses. Normal form inputs remain the
same.
Previously we had a facades module but it was being unused and we didn't see a future
for this module so we moved the only class in this module to it's own class. All instances
of:
now become:from masonite.request import Request
def show(self, request: Request):
    return request.input('payload')['id']
from masonite.request import Request
def show(self, request: Request):
    return request.input('id')
from masonite.facades.Auth import Auth
from masonite.auth import AuthRemoved the facades module.
Provider Refactoring
Route Provider
Moved parameter parsing into if statement6/12/25, 3:02 AM Masonite Documentation