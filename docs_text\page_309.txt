=== Page 310 ===

310Note that tests methods are not always ran in the order specified in the class. Anyway you
should not make the assumptions that tests will be run in a given order. You should try to
make your tests idempotent.
All methods that begin with assert can be chained together to run through many
assertions. All other method will return some kind of boolean or value which you can use
to do your own assertions.
Sometimes you need to assert that a given piece of code will raise a given exception. To
do this you can use the standard assertRaises() context manager:
Sometimes you need to test the output of a function that prints to the console. To do this
in your tests you can use the captureOutput() context manager:
with self.assertRaises(ValidationError) as e:
    # run some code here
    raise ValidationError("An error occured !")
self.assertEqual(str(e.exception), "An error occured !")
with self.captureOutput() as output:
    # run some code here
    print("Hello World !")
self.assertEqual(output.getvalue().strip(), "Hello World !")Chaining Assertions
Asserting Exceptions
Capturing Output
Overriding Debug Mode6/12/25, 3:02 AM Masonite Documentation