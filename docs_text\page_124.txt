=== Page 125 ===

125The default key here is the guard to use for authentication. The web dictionary is the
configuration for the web guard.
You can attempt a login by using the Auth class and using the attempt method:
If the attempt succeeds, the user will now be authenticated and the result of the attempt
will be the authenticated model.
If the attempt fails then the result will be None.
If you know the primary key of the model, you can attempt by the id:
You can logout the current user:from app.User import User
GUARDS = {
    "default": "web",
    "web": {"model": User},
    "password_reset_table": "password_resets",
    "password_reset_expiration": 1440,  # in minutes. 24 hours. None if 
disabled
}
from masonite.authentication import Auth
from masonite.request import Request
def login(self, auth: Auth, request: Request):
  user = auth.attempt(request.input('email'), 
request.input("password"))
from masonite.authentication import Auth
from masonite.request import Request
def login(self, auth: Auth, request: Request):
  user = auth.attempt_by_id(1)Login Attempts6/12/25, 3:02 AM Masonite Documentation