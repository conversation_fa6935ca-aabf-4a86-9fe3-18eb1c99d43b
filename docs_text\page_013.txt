=== Page 14 ===

14This will create our users table for us along with a migrations table to keep track of any
migrations we add later.
Now that we have the authentication and the migrations all migrated in, let's create our
first user. Remember that we ran craft auth so we have a few new templates and
controllers.
Go ahead and run the server:
and head over to http://localhost:8000/register and fill out the form. You can use
whatever name and email you like but for this purpose we will use:
We have looked at running migrations but let's look at how to create a new migration.
Now that we have our authentication setup and we are comfortable with migrating our
application, let's create a new migration where we will store our posts.
Our posts table should have a few obvious columns that we will simplify for this tutorial
part.$ python craft migrate
terminal
$ python craft serve
Username: demo
Email: <EMAIL>
Password: password!!11AACreating Users
Migrations6/12/25, 3:02 AM Masonite Documentation