=== Page 148 ===

148File cache driver is storing data by saving it on the server's filesystem. It can be used as is
without third party service.
Location where Masonite will store cache data files defaults to 
storage/framework/cache in project root directory but can be changed with 
location parameter.
Redis cache driver is requiring the redis python package, that you can install with:
Then you should define Redis as default store and configure it with your Redis server
parameters:
Finally ensure that the Redis server is running and you're ready to start using cache.
Memcached cache driver is requiring the pymemcache python package, that you can
install with:pip install redis
STORES = {
    "default": "redis",
    "redis": {
        "driver": "redis",
        "host": env("REDIS_HOST", "127.0.0.1"),
        "port": env("REDIS_PORT", "6379"),
        "password": env("REDIS_PASSWORD"),
        "name": env("REDIS_PREFIX", "project name"),
    },
}File Cache
Redis
Memcached6/12/25, 3:02 AM Masonite Documentation