"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawledContent = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const crawl_job_model_1 = require("./crawl-job.model");
let CrawledContent = class CrawledContent extends repository_1.Entity {
    constructor(data) {
        super(data);
    }
};
exports.CrawledContent = CrawledContent;
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        id: true,
        generated: true,
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "id", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        jsonSchema: {
            format: 'uri',
            minLength: 10,
            maxLength: 2000,
            errorMessage: 'URL should be a valid URL',
        },
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "url", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        jsonSchema: {
            minLength: 1,
            maxLength: 500,
        },
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "title", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            dataType: 'TEXT'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "content", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            dataType: 'TEXT'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "htmlContent", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            dataType: 'TEXT'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "markdownContent", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'text/html',
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "contentType", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
        jsonSchema: {
            minimum: 0,
            maximum: 10,
        },
    }),
    tslib_1.__metadata("design:type", Number)
], CrawledContent.prototype, "depth", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], CrawledContent.prototype, "contentLength", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 200,
        jsonSchema: {
            minimum: 100,
            maximum: 600,
        },
    }),
    tslib_1.__metadata("design:type", Number)
], CrawledContent.prototype, "statusCode", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'pending',
        jsonSchema: {
            enum: ['pending', 'processing', 'completed', 'failed', 'skipped'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "status", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'array',
        itemType: 'string',
        default: [],
    }),
    tslib_1.__metadata("design:type", Array)
], CrawledContent.prototype, "extractedLinks", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'array',
        itemType: 'string',
        default: [],
    }),
    tslib_1.__metadata("design:type", Array)
], CrawledContent.prototype, "extractedImages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
        default: {},
    }),
    tslib_1.__metadata("design:type", Object)
], CrawledContent.prototype, "metadata", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
        default: {},
    }),
    tslib_1.__metadata("design:type", Object)
], CrawledContent.prototype, "headers", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "parentUrl", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "errorMessage", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], CrawledContent.prototype, "processingTimeMs", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'file_path'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "filePath", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        postgresql: {
            columnName: 'file_size'
        }
    }),
    tslib_1.__metadata("design:type", Number)
], CrawledContent.prototype, "fileSize", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'file_hash'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "fileHash", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'boolean',
        default: false,
        postgresql: {
            columnName: 'is_selected'
        }
    }),
    tslib_1.__metadata("design:type", Boolean)
], CrawledContent.prototype, "isSelected", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'selection_group'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "selectionGroup", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'crawled_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawledContent.prototype, "crawledAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'created_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawledContent.prototype, "createdAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'updated_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], CrawledContent.prototype, "updatedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.belongsTo)(() => crawl_job_model_1.CrawlJob, {}, {
        postgresql: {
            columnName: 'crawl_job_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], CrawledContent.prototype, "crawlJobId", void 0);
exports.CrawledContent = CrawledContent = tslib_1.__decorate([
    (0, repository_1.model)({
        settings: {
            strict: true,
            indexes: {
                crawlJobIdIndex: {
                    keys: {
                        crawlJobId: 1,
                    },
                },
                urlIndex: {
                    keys: {
                        url: 1,
                    },
                },
                contentTypeIndex: {
                    keys: {
                        contentType: 1,
                    },
                },
                statusIndex: {
                    keys: {
                        status: 1,
                    },
                },
                createdAtIndex: {
                    keys: {
                        createdAt: -1,
                    },
                },
            },
            postgresql: {
                schema: 'public',
                table: 'crawled_content'
            }
        },
    }),
    tslib_1.__metadata("design:paramtypes", [Object])
], CrawledContent);
//# sourceMappingURL=crawled-content.model.js.map