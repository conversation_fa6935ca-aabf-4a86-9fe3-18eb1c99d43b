=== Page 159 ===

159Here when using iterations, the user should provide a value.
If the argument may or may not have a value, you can use the suffix =? instead.
Finally if a default value should be used when no value is provided, add the suffix =
{default}:
Inside the command, optional arguments can be retrieved with 
self.option(arg_name)"""
command_name
    {--iterations= : Description of the iterations argument}
"""
python craft command_name --iterations 3
python craft command_name --iterations=3
"""
command_name
    {--iterations=?: Description of the iterations argument}
"""
python craft command_name --iterations
python craft command_name --iterations 3
"""
command_name
    {--iterations=3: Description of the iterations argument}
"""
# iterations will be equal to 3
python craft command_name --iterations
# iterations will be equal to 1
python craft command_name --iterations 1
    def handle(self):
        name = self.option("iterations")6/12/25, 3:02 AM Masonite Documentation