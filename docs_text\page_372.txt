=== Page 373 ===

373Just like the global controllers, some packages may require you to add a view that is
located in their package (like the new exception debug view in 1.6) so you may now add
views in different namespaces:
This will get a template that is located in the masonite package itself.
You can now group routes based on a specific string prefix. This will now look like:
which will compile down into /dashboard/user and /dashboard/user/1
The container was one of the first features coded in the 1.x release line. For Masonite 1.6
we have revisited how the container resolves objects. Before this release you had to put
all annotation classes in the back of the parameter list:def show(self):
    return view('/masonite/views/index')
from masonite.helpers.routes import get, group
ROUTES = [
    get('/home', ...)
    group('/dashboard', [
        get('/user', ...)
        get('/user/1')
    ])
]
from masonite.request import Request
def show(self, Upload, request: Request):
    passAdded Route Groups
Changed Container Resolvingroutes/web.py6/12/25, 3:02 AM Masonite Documentation