=== Page 300 ===

300All CORS settings are configured in the dedicated security configuration file 
config/security.py. The default configuration options are:
You can define paths for which you want CORS protection to be enabled. Multiple paths
can be defined in the list and wildcards (*) can be used to define the paths.
Here all requests made to auth/ and to all API routes will be protected with CORS.
The default is to protect all HTTP methods but a list of methods can be specified instead.
This will set the Access-Control-Allow-Methods header in the response.
For example CORS can be enabled for sensitive requests:CORS = {
    "paths": ["api/*"],
    "allowed_methods": ["*"],
    "allowed_origins": ["*"],
    "allowed_headers": ["*"],
    "exposed_headers": [],
    "max_age": None,
    "supports_credentials": False,
}
    "paths": ["api/*", "auth/"]
    "allowed_methods": ["POST", "PUT", "PATCH"]Configuration
Paths
Allowed Methods6/12/25, 3:02 AM Masonite Documentation