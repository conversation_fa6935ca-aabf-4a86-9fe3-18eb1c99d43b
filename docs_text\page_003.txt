=== Page 4 ===

4Be sure to join the Discord Community for help or guidance.
Masonite excels at being simple to install and get going. If you are coming from previous
versions of Masonite, the order of some of the installation steps have changed a bit.
Firstly, open a terminal and head to a directory you want to create your application in. You
might want to create it in a programming directory for example:
If you are on windows you can just create a directory and open the directory in the
Powershell.
Although this step is technically optional, it is highly recommended. You can create a
virtual environment if you don't want to install all of masonite's dependencies on your
systems Python. If you use virtual environments then create your virtual environment by
running:
or if you are on Windows:
The python command here is utilizing Python 3. Your machine may run Python 2 (typically
2.7) by default for UNIX machines. You may set an alias on your machine for Python 3 or
simply run python3 anytime you see the python command.
$ cd ~/programming
$ mkdir myapp
$ cd myapp
$ python -m venv venv
$ source venv/bin/activate
$ python -m venv venv
$ ./venv/Scripts/activate
Activating Our Virtual Environment (optional)
terminal
terminal6/12/25, 3:02 AM Masonite Documentation