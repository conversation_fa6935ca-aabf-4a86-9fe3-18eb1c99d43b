"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlerService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const repositories_1 = require("../repositories");
const models_1 = require("../models");
const child_process_1 = require("child_process");
const path = tslib_1.__importStar(require("path"));
let CrawlerService = class CrawlerService {
    constructor(crawlJobRepository, crawledContentRepository) {
        this.crawlJobRepository = crawlJobRepository;
        this.crawledContentRepository = crawledContentRepository;
        this.activeCrawlers = new Map();
        // Path to the Python crawler script
        this.crawlerScriptPath = path.join(__dirname, '../../scripts/crawler.py');
    }
    /**
     * Start a new crawl job
     */
    async startCrawl(crawlJob) {
        try {
            // Update job status to running
            await this.crawlJobRepository.updateById(crawlJob.id, {
                status: 'running',
                startedAt: new Date(),
                updatedAt: new Date(),
            });
            // Prepare crawl options
            const options = {
                maxDepth: crawlJob.maxDepth,
                maxPages: crawlJob.maxPages,
                allowedContentTypes: crawlJob.allowedContentTypes,
                excludePatterns: crawlJob.excludePatterns,
                includePatterns: crawlJob.includePatterns,
                followExternalLinks: crawlJob.followExternalLinks,
                respectRobotsTxt: crawlJob.respectRobotsTxt,
                delayBetweenRequests: crawlJob.delayBetweenRequests,
                ...crawlJob.crawlOptions,
            };
            // Start Python crawler process
            await this.spawnCrawlerProcess(crawlJob.id, crawlJob.url, options);
        }
        catch (error) {
            await this.handleCrawlError(crawlJob.id, error);
        }
    }
    /**
     * Stop a running crawl job
     */
    async stopCrawl(jobId) {
        const crawler = this.activeCrawlers.get(jobId);
        if (crawler) {
            crawler.kill('SIGTERM');
            this.activeCrawlers.delete(jobId);
            await this.crawlJobRepository.updateById(jobId, {
                status: 'cancelled',
                completedAt: new Date(),
                updatedAt: new Date(),
            });
        }
    }
    /**
     * Pause a running crawl job
     */
    async pauseCrawl(jobId) {
        const crawler = this.activeCrawlers.get(jobId);
        if (crawler) {
            crawler.kill('SIGUSR1'); // Send pause signal
            await this.crawlJobRepository.updateById(jobId, {
                status: 'paused',
                updatedAt: new Date(),
            });
        }
    }
    /**
     * Resume a paused crawl job
     */
    async resumeCrawl(jobId) {
        const crawlJob = await this.crawlJobRepository.findById(jobId);
        if (crawlJob.status === 'paused') {
            const crawler = this.activeCrawlers.get(jobId);
            if (crawler) {
                crawler.kill('SIGUSR2'); // Send resume signal
                await this.crawlJobRepository.updateById(jobId, {
                    status: 'running',
                    updatedAt: new Date(),
                });
            }
        }
    }
    /**
     * Get crawl job progress
     */
    async getCrawlProgress(jobId) {
        const crawlJob = await this.crawlJobRepository.findById(jobId);
        const contentCount = await this.crawledContentRepository.count({ crawlJobId: jobId });
        return {
            jobId,
            status: crawlJob.status,
            processedPages: crawlJob.processedPages,
            totalPages: crawlJob.totalPages,
            errorMessage: crawlJob.errorMessage,
        };
    }
    /**
     * Spawn Python crawler process
     */
    async spawnCrawlerProcess(jobId, url, options) {
        const args = [
            this.crawlerScriptPath,
            '--job-id', jobId,
            '--url', url,
            '--options', JSON.stringify(options),
            '--callback-url', `http://localhost:3002/api/crawler/callback`,
        ];
        const crawler = (0, child_process_1.spawn)('python', args, {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                PYTHONPATH: path.join(__dirname, '../../scripts'),
            },
        });
        this.activeCrawlers.set(jobId, crawler);
        // Handle crawler output
        crawler.stdout.on('data', (data) => {
            console.log(`Crawler ${jobId} stdout:`, data.toString());
            this.handleCrawlerOutput(jobId, data.toString());
        });
        crawler.stderr.on('data', (data) => {
            console.error(`Crawler ${jobId} stderr:`, data.toString());
        });
        crawler.on('close', (code) => {
            console.log(`Crawler ${jobId} exited with code ${code}`);
            this.activeCrawlers.delete(jobId);
            this.handleCrawlerExit(jobId, code || 0);
        });
        crawler.on('error', (error) => {
            console.error(`Crawler ${jobId} error:`, error);
            this.activeCrawlers.delete(jobId);
            this.handleCrawlError(jobId, error);
        });
    }
    /**
     * Handle crawler output for progress updates
     */
    async handleCrawlerOutput(jobId, output) {
        try {
            const lines = output.split('\n').filter(line => line.trim());
            for (const line of lines) {
                if (line.startsWith('PROGRESS:')) {
                    const progressData = JSON.parse(line.substring(9));
                    await this.updateCrawlProgress(jobId, progressData);
                }
                else if (line.startsWith('CONTENT:')) {
                    const contentData = JSON.parse(line.substring(8));
                    await this.saveCrawledContent(jobId, contentData);
                }
            }
        }
        catch (error) {
            console.error('Error parsing crawler output:', error);
        }
    }
    /**
     * Handle crawler process exit
     */
    async handleCrawlerExit(jobId, code) {
        const status = code === 0 ? 'completed' : 'failed';
        const errorMessage = code !== 0 ? `Crawler exited with code ${code}` : undefined;
        await this.crawlJobRepository.updateById(jobId, {
            status,
            completedAt: new Date(),
            updatedAt: new Date(),
            errorMessage,
        });
    }
    /**
     * Handle crawl errors
     */
    async handleCrawlError(jobId, error) {
        await this.crawlJobRepository.updateById(jobId, {
            status: 'failed',
            completedAt: new Date(),
            updatedAt: new Date(),
            errorMessage: error.message || 'Unknown error occurred',
        });
    }
    /**
     * Update crawl progress
     */
    async updateCrawlProgress(jobId, progressData) {
        await this.crawlJobRepository.updateProgress(jobId, progressData.processedPages, progressData.totalPages, progressData.status);
    }
    /**
     * Save crawled content
     */
    async saveCrawledContent(jobId, contentData) {
        const content = new models_1.CrawledContent({
            crawlJobId: jobId,
            url: contentData.url,
            title: contentData.title,
            content: contentData.content,
            htmlContent: contentData.htmlContent,
            markdownContent: contentData.markdownContent,
            contentType: contentData.contentType,
            depth: contentData.depth,
            contentLength: contentData.contentLength,
            statusCode: contentData.statusCode,
            status: 'completed',
            extractedLinks: contentData.extractedLinks || [],
            extractedImages: contentData.extractedImages || [],
            metadata: contentData.metadata || {},
            headers: contentData.headers || {},
            parentUrl: contentData.parentUrl,
            processingTimeMs: contentData.processingTimeMs || 0,
            crawledAt: new Date(),
        });
        await this.crawledContentRepository.create(content);
    }
    /**
     * Get active crawlers count
     */
    getActiveCrawlersCount() {
        return this.activeCrawlers.size;
    }
    /**
     * Get all active crawler job IDs
     */
    getActiveCrawlerJobIds() {
        return Array.from(this.activeCrawlers.keys());
    }
};
exports.CrawlerService = CrawlerService;
exports.CrawlerService = CrawlerService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.SINGLETON }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.CrawlJobRepository)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.CrawledContentRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.CrawlJobRepository,
        repositories_1.CrawledContentRepository])
], CrawlerService);
//# sourceMappingURL=crawler.service.js.map