=== Page 246 ===

246Sometimes you may cross a time where you need to create a new rule that isn't available
in Masonite or there is such a niche use case that you need to build a rule for.@if errors.any():
<div class="bg-yellow-400">
  <div class="bg-yellow-200 text-yellow-800 px-4 py-2">
    <ul>
      @for key, message in errors.all().items()
        <li>{{ message }}</li>
      @endfor
    </ul>
  </div>
</div>
@endif
<form method="post" action="/contact">
  {{ csrf_field }}
  <div>
    <label for="name">Name</label>
    <input type="text" name="name" placeholder="Name">
    @if errors.has('name')
    <span>{{ errors.get('name')[0] }}</span>
    @endif
  </div>
  <div>
    <label for="email">Email</label>
    <input type="email" name="email" placeholder="Email">
    @if errors.has('email')
    <span>{{ errors.get('email')[0] }}</span>
    @endif
  </div>
  <div>
    <label for="message">Message</label>
    <textarea name="message" placeholder="Message"></textarea>
    @if errors.has('message')
    <span>{{ errors.get('message')[0] }}</span>
    @endif
  </div>
  <button type="submit">Send</button>
</form>
Creating a Rule6/12/25, 3:02 AM Masonite Documentation