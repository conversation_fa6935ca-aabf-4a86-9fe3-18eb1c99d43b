=== Page 185 ===

185Notice at this point you can call any building options you want on the mailables to modify
the behavior of it before sending.
Note that you can also use the Mail facade anywhere in your code:
You can modify the behavior of the mailable by using any one of these optionsfrom masonite.mail import Mail
from app.mailables.Welcome import Welcome
class WelcomeController(Controller):
  
    def welcome(self, mail: Mail):
        mail.mailable(Welcome().to('<EMAIL>')).send()
from masonite.facades import Mail
from app.mailables.Welcome import Welcome
Mail.mailable(Welcome().to('<EMAIL>')).send()
Options Description
to('<EMAIL>')Specifies the user to send the email to.
You may also specify the users name like 
to('<PERSON> <<EMAIL>>').
from_("<EMAIL>")Specifies the address that the email should
appear it is from.
cc(["<EMAIL>"])A list of the addresses that should be "carbon
copied" onto this email
bcc(["<EMAIL>"])A list of the addresses that should be "blind
carbon copied" onto this email
subject('Subject of the Email') Specifies the subject of the email.Mail Options6/12/25, 3:02 AM Masonite Documentation