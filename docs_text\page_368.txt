=== Page 369 ===

369In previous versions, Masonite has not been able to fetch the site packages directory if
you were in a virtual environment because virtual environment directories are dynamically
named depending on who created it. We have found a way to detect the virtual
environment and the site packages directory so now there is no need to add the site
packages directory manually to the packages configuration fileCraft Auto Adds Site Packages6/12/25, 3:02 AM Masonite Documentation