=== Page 214 ===

214If your package contains migrations you can register the migration files to be published in
a project:
The package publish command will publish the migrations files into the defined project
migrations folder. With the default project settings it would be in 
databases/migrations/. Migrations file are published with a timestamp, so here it
would result in those two files: {timestamp}_create_some_table.py and 
{timestamp}_create_other_table.py.
If your package contains routes you can register them by providing your route files and
the locations to load controllers (used by your routes) from. For this you will need to call 
controllers(*locations) and then routes(*routes) inside configure() method.
If your routes are defined in super_awesome_package/routes/api.py and 
super_awesome_package/routes/web.py and the controllers files available in 
super_awesome_package/controllers you can do:
Now Masonite should be able to resolve new routes from your packages.def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .migrations("migrations/create_some_table.py", 
"migrations/create_other_table.py")
    )
def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .controllers("controllers") # before routes !
        .routes("routes/api.py", "routes/web.py")
    )Routes / Controllers
Views6/12/25, 3:02 AM Masonite Documentation