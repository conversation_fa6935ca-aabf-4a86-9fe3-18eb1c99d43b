=== Page 313 ===

313Note that exception handling is disabled by default during testing.
Enable CSRF protection during testing.
Disable CSRF protection during testing.
Note that CSRF protection is disabled by default during testing.
Add cookies that will be used in the next request. This method accepts a dictionary of
name / value pairs. Cookies dict is reset between each test.
Add headers that will be used in the next request. This method accepts a dictionary of
name / value pairs. Headers dict is reset between each test.
self.withCsrf()
self.withoutCsrf()
self.withCookies(data)
self.withHeaders(data)withCsrf
withoutCsrf
withCookies
withHeaders6/12/25, 3:02 AM Masonite Documentation