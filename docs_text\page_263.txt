=== Page 264 ===

264The accepted rule is most useful when seeing if a checkbox has been checked. When a
checkbox is submitted it usually has the value of on so this rule will check to make sure
the value is either on, 1, or yes.accepted active_domainafter_today before_today
confirmed contains date different
distinct does_not email equals
exists file greater_than image
in_range ip is_future is_list
is_in is_past isnt json
length less_than matches none
numeric one_of phone postal_code
regex required required_if required_with
string strong timezone truthy
uuid video when
"""
{
  'terms': 'on'
}
"""
validate.accepted('terms')Available Rules
Accepted
Active_domain6/12/25, 3:02 AM Masonite Documentation