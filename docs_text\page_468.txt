=== Page 469 ===

469Y<PERSON> may not have to make this change but in some previous versions of Masonite, the
craft file that came with new projects was wrong. The file is called craft and is in the
root of your project directory. If the changes with the + sign already exist then your
project is correct. If you have the code in strikethrough then make this diff change:
If you use Masonite queues, there are 3 new columns on the queue_jobs table. Please
make a migration and add these 3 columns:
First make the migration- from bootstrap.start import app
+ from masonite.wsgi import response_handler
from src.masonite.helpers import config
# ..
container = App()
- container.bind('WSGI', app)
+ container.bind('WSGI', response_handler)
container.bind('Container', container)
- from masonite import info
+ from masonite import __version__
from wsgi import container
- application = Application('Masonite Version:', info.VERSION)
+ application = Application('Masonite Version:', __version__)
$ python craft migration add_fields_to_queue_jobs_table --table 
queue_jobsCraft File
Queue Table6/12/25, 3:02 AM Masonite Documentation