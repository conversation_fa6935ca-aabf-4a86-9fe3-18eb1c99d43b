=== Page 210 ===

210If you never uploaded a package on PyPi before you will need to register. Verify your
email address.
Creating a publish token
API tokens provide an alternative way (instead of username and password) to
authenticate when uploading packages to PyPI. It is is strongly recommended to use an
API token where possible.
Go to your PyPi account and find the API tokens section. Click on Add API token,
give a significant name such as publish-token and create the token. Write down the
token key somewhere for later.
Configuring PyPi on your computer
If you never uploaded a package on PyPi before you will need to configure PyPi on your
computer. For this you will need to add a .pypirc file in your home directory. If you do
not have one then you can easily creating one using:
This will move the file to your home directory. If you are using Windows you may need to
move this file manually.
Then fill in password key with the token you created later prefixed by pypi-. With a
token starting with AgEIcHlwaS5vcmcCJGNjYjA4M... the .pypirc file would look like:
Publishing the package to PyPI
$ make pypirc
[distutils]
index-servers =
  pypi
  pypitest
[pypi]
username=__token__
password=pypi-AgEIcHlwaS5vcmcCJGNjYjA4M...6/12/25, 3:02 AM Masonite Documentation