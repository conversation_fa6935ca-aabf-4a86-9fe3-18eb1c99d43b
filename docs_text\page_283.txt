=== Page 284 ===

284Service Providers
Service Providers are the key building blocks to Masonite. The only thing they do is
register things into the Service Container, or run logic on requests. If you look inside the 
config/providers.py file, you will find a PROVIDERS list which contains all the Service
Providers involved in building the framework.
Although uncommon, You may create your own service provider and add it to your
providers list to extend Masonite, or even remove some providers if you don't need their
functionality. If you do create your own Service Provider, consider making it available on
PyPi so others can install it into their framework.
We can create a Service Provider by simply using a craft command:
This will create a new Service Provider under our 
app/providers/DashboardProvider.py. This new Service Provider will have two
simple methods, a register method and a boot method. We'll explain both in detail.
There are a few architectural examples we will walk through to get you familiar with how
Service Providers work under the hood. Let's look at a simple provider and walk through
it.$ python craft provider DashboardProviderCreating a Provider
Service Provider Execution6/12/25, 3:02 AM Masonite Documentation