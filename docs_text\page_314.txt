=== Page 315 ===

315months, years.
Set the mocked time as an offset of a given unit of time in the past. Unit can be specified
among pendulum units: seconds, minutes, hours, days (default), weeks, months,
years.
Restore the mocked time behaviour to default behaviour of pendulum. When using 
fake time helpers you should not forget to call this helper at the end.
It can be done directly in the test or in a tearDown() method.self.fakeTimeInFuture(offset, unit="days")
real_now = pendulum.now()
self.fakeTimeInFuture(1, "months")
self.assertEqual(pendulum.now().diff(real_now).in_months(), 1)
self.fakeTimeInPast(offset, unit="days")
def tearDown(self):
    super().tearDown()
    self.restoreTime()
def test_creation_date(self):
    self.fakeTimeYesterday()
    # from now on until the end of this unit test, time is mocked and 
will return yesterday timefakeTimeInPast
restoreTime6/12/25, 3:02 AM Masonite Documentation