"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthController = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const rest_1 = require("@loopback/rest");
const repository_1 = require("@loopback/repository");
const authentication_jwt_1 = require("@loopback/authentication-jwt");
const security_1 = require("@loopback/security");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
const uuid_1 = require("uuid");
let OAuthController = class OAuthController {
    constructor(jwtService, userRepository, oauthAuthCodeRepository, oauthService, response) {
        this.jwtService = jwtService;
        this.userRepository = userRepository;
        this.oauthAuthCodeRepository = oauthAuthCodeRepository;
        this.oauthService = oauthService;
        this.response = response;
    }
    async getOAuthUrl(provider) {
        try {
            console.log('🔍 Debug - Raw provider parameter received:', JSON.stringify(provider));
            console.log('🔗 Generating OAuth URL for provider:', provider);
            const supportedProviders = ['google', 'github', 'microsoft'];
            if (!supportedProviders.includes(provider)) {
                throw new rest_1.HttpErrors.BadRequest(`Unsupported OAuth provider: ${provider}`);
            }
            // Check if provider is configured
            const requiredEnvVars = {
                google: 'GOOGLE_CLIENT_ID',
                github: 'GITHUB_CLIENT_ID',
                microsoft: 'MICROSOFT_CLIENT_ID'
            };
            const envVar = requiredEnvVars[provider];
            if (!process.env[envVar]) {
                console.log(`❌ ${envVar} not configured`);
                throw new rest_1.HttpErrors.ServiceUnavailable(`${provider} OAuth is not configured. Please set ${envVar} environment variable.`);
            }
            const state = `${provider}_${Date.now()}_${Math.random().toString(36).substring(7)}`;
            const url = this.oauthService.generateOAuthUrl(provider, state);
            console.log('✅ OAuth URL generated successfully');
            return {
                url,
                provider,
                state
            };
        }
        catch (error) {
            console.error('❌ OAuth URL Generation Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError(`Failed to generate ${provider} OAuth URL`);
        }
    }
    async handleOAuthCallback(provider, request) {
        try {
            console.log('🔗 Processing OAuth callback for provider:', provider);
            // Exchange code for access token
            const accessToken = await this.oauthService.exchangeCodeForToken(provider, request.code);
            // Verify token and get user data
            let oauthUserData;
            switch (provider) {
                case 'google':
                    oauthUserData = await this.oauthService.verifyGoogleToken(accessToken);
                    break;
                case 'github':
                    oauthUserData = await this.oauthService.verifyGitHubToken(accessToken);
                    break;
                case 'microsoft':
                    oauthUserData = await this.oauthService.verifyMicrosoftToken(accessToken);
                    break;
                default:
                    throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
            }
            console.log('✅ OAuth user data retrieved:', {
                email: oauthUserData.email,
                name: `${oauthUserData.firstName} ${oauthUserData.lastName}`
            }); // Find or create user
            const existingUser = await this.userRepository.findOne({
                where: { email: oauthUserData.email }
            });
            const isNewUser = !existingUser;
            console.log(`👤 OAuth User Status: ${isNewUser ? 'Creating new user' : 'Existing user found'}`);
            const user = await this.oauthService.findOrCreateOAuthUser(provider, oauthUserData);
            if (isNewUser) {
                console.log('✅ New OAuth user created successfully');
            }
            else {
                console.log('✅ Existing OAuth user authenticated successfully');
            } // Generate JWT token
            const userProfile = {
                [security_1.securityId]: user.id.toString(),
                name: `${user.firstName} ${user.lastName}`,
                id: user.id,
                email: user.email,
                roles: user.roles || ['user'],
            };
            const token = await this.jwtService.generateToken(userProfile);
            console.log('✅ OAuth login successful');
            return {
                token,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatarUrl: user.avatarUrl,
                    emailVerified: user.emailVerified,
                    roles: user.roles,
                },
                isNewUser
            };
        }
        catch (error) {
            console.error('❌ OAuth Callback Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('OAuth authentication failed');
        }
    }
    async handleOAuthRedirect(code, state, error) {
        try {
            const frontendUrl = process.env.CORS_ORIGIN || 'http://localhost:4200';
            if (error) {
                // OAuth error occurred
                console.error('❌ OAuth Error:', error);
                const errorUrl = `${frontendUrl}/auth/oauth-error?error=${encodeURIComponent(error)}`;
                this.response.redirect(302, errorUrl);
                return;
            }
            if (!code || !state) {
                const errorUrl = `${frontendUrl}/auth/oauth-error?error=${encodeURIComponent('Missing authorization code or state parameter')}`;
                this.response.redirect(302, errorUrl);
                return;
            } // Extract provider from state
            console.log('🔍 Debug - Raw state parameter:', state);
            const provider = state.split('_')[0];
            console.log('🔍 Debug - Extracted provider:', provider);
            console.log('🔗 Processing OAuth redirect for provider:', provider);
            // Exchange code for access token
            const accessToken = await this.oauthService.exchangeCodeForToken(provider, code);
            // Verify token and get user data
            let oauthUserData;
            switch (provider) {
                case 'google':
                    oauthUserData = await this.oauthService.verifyGoogleToken(accessToken);
                    break;
                case 'github':
                    oauthUserData = await this.oauthService.verifyGitHubToken(accessToken);
                    break;
                case 'microsoft':
                    oauthUserData = await this.oauthService.verifyMicrosoftToken(accessToken);
                    break;
                default:
                    const errorUrl = `${frontendUrl}/auth/oauth-error?error=${encodeURIComponent('Unsupported OAuth provider')}`;
                    this.response.redirect(302, errorUrl);
                    return;
            }
            console.log('✅ OAuth user data retrieved:', {
                email: oauthUserData.email,
                name: `${oauthUserData.firstName} ${oauthUserData.lastName}`
            });
            // Find or create user
            const existingUser = await this.userRepository.findOne({
                where: { email: oauthUserData.email }
            });
            const isNewUser = !existingUser;
            const user = await this.oauthService.findOrCreateOAuthUser(provider, oauthUserData);
            // Generate one-time authorization code instead of JWT token
            const authCode = (0, uuid_1.v4)();
            const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes expiry
            // Store the authorization code
            await this.oauthAuthCodeRepository.create({
                code: authCode,
                userId: user.id.toString(),
                provider,
                isNewUser,
                expiresAt,
                used: false
            });
            console.log('✅ OAuth authorization code generated - redirecting to frontend');
            // Redirect to frontend with one-time authorization code
            const successUrl = `${frontendUrl}/auth/oauth-success?code=${encodeURIComponent(authCode)}&provider=${provider}`;
            this.response.redirect(302, successUrl);
            return;
        }
        catch (error) {
            console.error('❌ OAuth Callback Error:', error);
            const frontendUrl = process.env.CORS_ORIGIN || 'http://localhost:4200';
            const errorUrl = `${frontendUrl}/auth/oauth-error?error=${encodeURIComponent(error.message || 'OAuth authentication failed')}`;
            this.response.redirect(302, errorUrl);
            return;
        }
    }
    async exchangeAuthorizationCode(request) {
        try {
            console.log('🔄 Processing authorization code exchange');
            // Clean up expired codes first
            await this.oauthAuthCodeRepository.cleanupExpiredCodes();
            // Find and use the authorization code (marks it as used)
            const authCode = await this.oauthAuthCodeRepository.findAndUseCode(request.code);
            if (!authCode) {
                throw new rest_1.HttpErrors.BadRequest('Invalid or expired authorization code');
            }
            console.log('✅ Valid authorization code found for user:', authCode.userId);
            // Get user data
            const user = await this.userRepository.findById(authCode.userId);
            if (!user) {
                throw new rest_1.HttpErrors.NotFound('User not found');
            }
            // Generate JWT token
            const userProfile = {
                [security_1.securityId]: user.id.toString(),
                name: `${user.firstName} ${user.lastName}`,
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                emailVerified: user.emailVerified,
                roles: user.roles || ['user'],
            };
            const token = await this.jwtService.generateToken(userProfile);
            console.log('✅ JWT token generated successfully for OAuth user');
            return {
                token,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatarUrl: user.avatarUrl,
                    emailVerified: user.emailVerified,
                    roles: user.roles || ['user'],
                },
                isNewUser: authCode.isNewUser,
                provider: authCode.provider
            };
        }
        catch (error) {
            console.error('❌ Authorization Code Exchange Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Token exchange failed');
        }
    }
    async getAvailableProviders() {
        const providers = [
            {
                name: 'google',
                enabled: true,
                configured: !!process.env.GOOGLE_CLIENT_ID && !!process.env.GOOGLE_CLIENT_SECRET
            },
            {
                name: 'github',
                enabled: true,
                configured: !!process.env.GITHUB_CLIENT_ID && !!process.env.GITHUB_CLIENT_SECRET
            },
            {
                name: 'microsoft',
                enabled: true,
                configured: !!process.env.MICROSOFT_CLIENT_ID && !!process.env.MICROSOFT_CLIENT_SECRET
            }
        ];
        return { providers };
    }
};
exports.OAuthController = OAuthController;
tslib_1.__decorate([
    (0, rest_1.get)('/auth/oauth/{provider}/url'),
    (0, rest_1.response)(200, {
        description: 'Get OAuth authorization URL',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        url: { type: 'string' },
                        provider: { type: 'string' },
                        state: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "getOAuthUrl", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/oauth/{provider}/callback'),
    (0, rest_1.response)(200, {
        description: 'Handle OAuth callback',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: { type: 'object' },
                        isNewUser: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__param(1, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                        state: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "handleOAuthCallback", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/oauth/callback'),
    (0, rest_1.response)(302, {
        description: 'Handle OAuth callback redirect',
    }),
    tslib_1.__param(0, rest_1.param.query.string('code')),
    tslib_1.__param(1, rest_1.param.query.string('state')),
    tslib_1.__param(2, rest_1.param.query.string('error')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, String, String]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "handleOAuthRedirect", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/oauth/exchange-token'),
    (0, rest_1.response)(200, {
        description: 'Exchange authorization code for JWT token',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                email: { type: 'string' },
                                firstName: { type: 'string' },
                                lastName: { type: 'string' },
                                avatarUrl: { type: 'string' },
                                emailVerified: { type: 'boolean' },
                                roles: { type: 'array', items: { type: 'string' } },
                            },
                        },
                        isNewUser: { type: 'boolean' },
                        provider: { type: 'string' }
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string', description: 'One-time authorization code' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "exchangeAuthorizationCode", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/oauth/providers'),
    (0, rest_1.response)(200, {
        description: 'Get available OAuth providers',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        providers: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    name: { type: 'string' },
                                    enabled: { type: 'boolean' },
                                    configured: { type: 'boolean' },
                                }
                            }
                        },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "getAvailableProviders", null);
exports.OAuthController = OAuthController = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(authentication_jwt_1.TokenServiceBindings.TOKEN_SERVICE)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(2, (0, repository_1.repository)(repositories_1.OAuthAuthorizationCodeRepository)),
    tslib_1.__param(3, (0, core_1.inject)('services.OAuthService')),
    tslib_1.__param(4, (0, core_1.inject)(rest_1.RestBindings.Http.RESPONSE)),
    tslib_1.__metadata("design:paramtypes", [Object, repositories_1.UserRepository,
        repositories_1.OAuthAuthorizationCodeRepository,
        services_1.OAuthService, Object])
], OAuthController);
//# sourceMappingURL=oauth.controller.js.map