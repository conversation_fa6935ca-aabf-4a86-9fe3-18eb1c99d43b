import { UserService } from '@loopback/authentication';
import { UserProfile } from '@loopback/security';
import { User } from '../models';
import { UserRepository } from '../repositories';
export interface Credentials {
    email: string;
    password: string;
}
export declare class MyUserService implements UserService<User, Credentials> {
    userRepository: UserRepository;
    constructor(userRepository: UserRepository);
    verifyCredentials(credentials: Credentials): Promise<User>;
    convertToUserProfile(user: User): UserProfile;
    private handleFailedLogin;
    private handleSuccessfulLogin;
}
