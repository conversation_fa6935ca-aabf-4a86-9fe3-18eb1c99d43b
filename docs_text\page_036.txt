=== Page 37 ===

37Most methods will require some dependency or parameters. You must specify them like
this:
And if your dependencies are objects it should give the path to the module:def some_function(self):
    """This is a function that does x action. 
    Then give an exmaple of when to use it 
    """
    ... code ...
def route(self, route, output):
    """Load the route into the class. This also looks for the 
controller and attaches it to the route.
    Arguments:
        route {string} -- This is a URI to attach to the route 
(/dashboard/user).
        output {string|object} -- Controller to attach to the route.
    Returns:
        self
    """
def __init__(self, request: Request, csrf: Csrf, view: View):
    """Initialize the CSRF Middleware
    Arguments:
        request {masonite.request.Request} -- The normal Masonite 
request class.
        csrf {masonite.auth.Csrf} -- CSRF auth class.
        view {masonite.view.View} -- The normal Masonite view class.
    Returns:
        self
    """
    passMethods and Functions with Dependencies6/12/25, 3:02 AM Masonite Documentation