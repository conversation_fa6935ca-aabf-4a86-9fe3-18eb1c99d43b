"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlJobRepository = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const datasources_1 = require("../datasources");
const models_1 = require("../models");
let CrawlJobRepository = class CrawlJobRepository extends repository_1.DefaultCrudRepository {
    constructor(dataSource, userRepositoryGetter, crawledContentRepositoryGetter, generatedDocumentRepositoryGetter) {
        super(models_1.CrawlJob, dataSource);
        this.userRepositoryGetter = userRepositoryGetter;
        this.crawledContentRepositoryGetter = crawledContentRepositoryGetter;
        this.generatedDocumentRepositoryGetter = generatedDocumentRepositoryGetter;
        this.generatedDocuments = this.createHasManyRepositoryFactoryFor('generatedDocuments', generatedDocumentRepositoryGetter);
        this.registerInclusionResolver('generatedDocuments', this.generatedDocuments.inclusionResolver);
        this.crawledContents = this.createHasManyRepositoryFactoryFor('crawledContents', crawledContentRepositoryGetter);
        this.registerInclusionResolver('crawledContents', this.crawledContents.inclusionResolver);
        this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter);
        this.registerInclusionResolver('user', this.user.inclusionResolver);
    }
    /**
     * Find crawl jobs by user ID
     */
    async findByUserId(userId) {
        return this.find({
            where: { userId },
            order: ['createdAt DESC'],
            include: ['crawledContents', 'generatedDocuments'],
        });
    }
    /**
     * Find active crawl jobs (running or pending)
     */
    async findActiveCrawlJobs() {
        return this.find({
            where: {
                status: { inq: ['pending', 'running', 'paused'] },
            },
            order: ['createdAt ASC'],
        });
    }
    /**
     * Find crawl jobs by status
     */
    async findByStatus(status) {
        return this.find({
            where: { status },
            order: ['createdAt DESC'],
        });
    }
    /**
     * Update crawl job progress
     */
    async updateProgress(id, processedPages, totalPages, status) {
        const progressPercentage = totalPages > 0 ? Math.round((processedPages / totalPages) * 100) : 0;
        const updateData = {
            processedPages,
            totalPages,
            progressPercentage,
            updatedAt: new Date(),
        };
        if (status) {
            updateData.status = status;
            if (status === 'running' && !updateData.startedAt) {
                updateData.startedAt = new Date();
            }
            else if (status === 'completed' || status === 'failed') {
                updateData.completedAt = new Date();
            }
        }
        await this.updateById(id, updateData);
    }
    /**
     * Get crawl job statistics for a user
     */
    async getUserCrawlStatistics(userId) {
        const jobs = await this.find({ where: { userId } });
        const stats = {
            totalJobs: jobs.length,
            completedJobs: jobs.filter(job => job.status === 'completed').length,
            failedJobs: jobs.filter(job => job.status === 'failed').length,
            runningJobs: jobs.filter(job => job.status === 'running').length,
            pendingJobs: jobs.filter(job => job.status === 'pending').length,
            totalPagesProcessed: jobs.reduce((sum, job) => sum + job.processedPages, 0),
            totalPagesCrawled: jobs.reduce((sum, job) => sum + job.totalPages, 0),
        };
        return stats;
    }
    /**
     * Clean up old completed crawl jobs
     */
    async cleanupOldJobs(daysOld = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);
        const oldJobs = await this.find({
            where: {
                status: { inq: ['completed', 'failed', 'cancelled'] },
                completedAt: { lt: cutoffDate },
            },
        });
        // Delete related content and documents first
        for (const job of oldJobs) {
            await this.crawledContents(job.id).delete();
            await this.generatedDocuments(job.id).delete();
        }
        // Delete the jobs
        await this.deleteAll({
            status: { inq: ['completed', 'failed', 'cancelled'] },
            completedAt: { lt: cutoffDate },
        });
        return oldJobs.length;
    }
};
exports.CrawlJobRepository = CrawlJobRepository;
exports.CrawlJobRepository = CrawlJobRepository = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('datasources.db')),
    tslib_1.__param(1, repository_1.repository.getter('UserRepository')),
    tslib_1.__param(2, repository_1.repository.getter('CrawledContentRepository')),
    tslib_1.__param(3, repository_1.repository.getter('GeneratedDocumentRepository')),
    tslib_1.__metadata("design:paramtypes", [datasources_1.DbDataSource, Function, Function, Function])
], CrawlJobRepository);
//# sourceMappingURL=crawl-job.repository.js.map