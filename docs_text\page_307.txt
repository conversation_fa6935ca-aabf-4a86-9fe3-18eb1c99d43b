=== Page 308 ===

308That's it! You're ready to start testing. Read on to learn how to build your test cases and
run them.
You can run tests by calling
This will automatically discover your tests following pytest automatic tests discovery.
You can also run a specific test class
Or a specific test method
Finally you can re-run the last failed tests automaticallyfrom masonite.tests import TestCase
class SomeFeatureTest(TestCase):
    def setUp(self):
        super().setUp()
    def test_something(self):
        self.assertTrue(True)
$ python -m pytest
$ python -m pytest tests/unit/test_my_feature.py
$ python -m pytest 
tests/unit/test_my_feature.py::MyFeatureTest::test_feature_is_working
$ python -m pytest --last-failedRunning Tests
Building Tests6/12/25, 3:02 AM Masonite Documentation