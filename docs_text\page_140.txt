=== Page 141 ===

141First you need to remove Broadcast.routes() from your routes and add your own
route
Then you need to create a custom controller to implement your logic
Presence channels work exactly the same as private channels except you can see who
else is inside this channel. This is great for chatroom type applications.
For Presence channels, the user also has to be authenticated.# routes/web.py
from masonite.routes import Route
ROUTES = [
    Route.post("/pusher/user-auth", "BroadcastController@authorize")
    #..
]
# app/controllers/BroadcastController.py
from masonite.controllers import Controller
from masonite.request import Request
from masonite.broadcasting import Broadcast
from masonite.helpers import optional
class BroadcastController(Controller):
    def authorize(self, request: Request, broadcast: Broadcast):
        channel_name = request.input("channel_name")
        _, user_id = channel_name.split("-")
        if int(user_id) == optional(request.user()).id:
            return broadcast.driver("pusher").authorize(
                channel_name, request.input("socket_id")
            )
        else:
            return False
Presence Channels6/12/25, 3:02 AM Masonite Documentation