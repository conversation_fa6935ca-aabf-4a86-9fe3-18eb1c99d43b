=== Page 13 ===

13In order to register these users, we will need a database. By default, Masonite uses
SQLite. If you want to use a different database you can change the options that start with 
DB_ in your .env file. For running MySQL or Postgres you will need to have those
databases setup already.
We have already run the migration command before, which was:
If you want to use MySQL, open up the .env file in the root of your project and change
the DB_DATABASE to mysql. Also, feel free to change the DB_DATABASE name to
something else.
Once you have set the correct credentials, we can go ahead and migrate the database.
Out of the box, Masonite has a migration for a users table which will be the foundation of
our user. You can edit this user migration before migrating but the default configuration
will suit most needs just fine and you can always add or remove columns at a later date.terminal
$ python craft migrate
.env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=masonite
DB_USERNAME=root
DB_PASSWORD=
terminalDatabase Setup
Migrating6/12/25, 3:02 AM Masonite Documentation