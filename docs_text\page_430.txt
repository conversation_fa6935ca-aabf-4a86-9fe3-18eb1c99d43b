=== Page 431 ===

431to:
and change the logic in bootstrap/start.py to:
Notice here we split the providers list when the server first boots up into two lists which
significantly lowers the overhead of each request.
This change should significantly boost speed performances as providers no longer have to
be located via pydoc. You should see an immediate decrease in the time it takes for the
application to serve a request. Rough time estimates say that this change should increase
the request times by about 5x as fast.for provider in container.make('Application').PROVIDERS:
    locate(provider)().load_app(container).register()
for provider in container.make('Application').PROVIDERS:
    located_provider = locate(provider)().load_app(container)
    if located_provider.wsgi is False:
        container.resolve(locate(provider)().load_app(container).boot)
 for provider in container.make('ProvidersConfig').PROVIDERS:
    located_provider = provider()
    located_provder.load_app(container).register()
    if located_provider.wsgi:
        container.make('WSGIProviders').append(located_provider)
     else:
        container.resolve(located_provider.boot)
        container.make('Providers').append(located_provider)
for provider in container.make('WSGIProviders'):
    container.resolve(located_provider.boot)
Duplicate Class Nameswsgi.py
wsgi.py
bootstrap/start.py6/12/25, 3:02 AM Masonite Documentation