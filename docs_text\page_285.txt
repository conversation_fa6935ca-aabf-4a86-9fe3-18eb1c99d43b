=== Page 286 ===

286This will be exactly the same as above. Notice that the boot method is resolved by
the container.
Once you create your own service provider, it will need to be registered in the PROVIDERS
list. This is likely in your config/providers.py file:
Once registered it will take your register and boot method into account when the
framework boots up or registers.from masonite.provider import ServiceProvider
from app.User import User
class UserModelProvider(ServiceProvider):
    ''' Binds the User model into the Service Container '''
    def __init__(self, application):
        self.application = application
    def register(self):
        self.application.bind('User', User)
    def boot(self, user: User):
        print(user)
PROVIDERS=[
    #..
    UserModelProvider,
]Registering the Service Provider6/12/25, 3:02 AM Masonite Documentation