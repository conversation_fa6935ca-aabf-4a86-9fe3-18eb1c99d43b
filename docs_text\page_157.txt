=== Page 158 ===

158Positional (mandatory) arguments are defined without dashes (- or --).
Here is how to define a positional argument called name with a description:
Inside the command, positional arguments can be retrieved with 
self.argument(arg_name)
Optional arguments are defined with dashes and can be used in any order in the
command call. An optional argument --force can have a short name --f.
Here is how to define two optional arguments iterations and force with a
description:
Notice how we provided the short version for the force argument but not for the 
iterations arguments
Now the command can be used like this:
If the optional argument is requiring a value you should add the = suffix:"""
    {name : Description of the name argument}
"""
    def handle(self):
        name = self.argument("name")
"""
command_name
    {--iterations : Description of the iterations argument}
    {--f|force : Description of the force argument}
"""
python craft command_name --f --iterations
python craft command_name --iterations --forceOptional Arguments6/12/25, 3:02 AM Masonite Documentation