=== Page 384 ===

384you won't have to worry about this change. It is still possible to resolve parameters by
passing that keyword argument to your container to activate that feature:
This should help with upgrading from 2.0 to 2.1 until you have refactored your
application. Then you should deactivate this keyword argument so you can be in line with
future 2.x releases.
You can choose to keep it activated if that is how you want to create applications but it
won't be officially supported by packages, future releases or in the documentation.
All middleware are now classes:
this is different from the previous string based middleware
Previously when getting an incoming JSON response, we had to get the values via the
payload input like so:container = App(resolve_parameters=True)
HTTP_MIDDLEWARE = [
    LoadUserMiddleware,
    CsrfMiddleware,
    ResponseMiddleware,
]
HTTP_MIDDLEWARE = [
    'app.http.middleware.LoadUserMiddleware.LoadUserMiddleware',
    'app.http.middleware.CsrfMiddleware.CsrfMiddleware',
    
'app.http.middleware.JsonResponseMiddleware.JsonResponseMiddleware',
]Class Middleware
Removed the Payload Input6/12/25, 3:02 AM Masonite Documentation