"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const core_1 = require("@loopback/core");
const authentication_1 = require("@loopback/authentication");
const authentication_jwt_1 = require("@loopback/authentication-jwt");
const security_1 = require("@loopback/security");
const bcryptjs_1 = require("bcryptjs");
const models_1 = require("../models");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let AuthController = class AuthController {
    constructor(jwtService, user, userRepository, securityService, emailService, smsService, recoveryCodeService, twofaDisableService, accountDeletionService) {
        this.jwtService = jwtService;
        this.user = user;
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
        this.recoveryCodeService = recoveryCodeService;
        this.twofaDisableService = twofaDisableService;
        this.accountDeletionService = accountDeletionService;
    }
    async signUp(newUserRequest) {
        console.log('🔍 Signup started for email:', newUserRequest.email);
        console.log('🔍 Request data:', {
            email: newUserRequest.email,
            firstName: newUserRequest.firstName,
            lastName: newUserRequest.lastName,
            phone: newUserRequest.phone,
            hasPassword: !!newUserRequest.password
        });
        // Check for preserved data from previous account deletion
        let preservedDataInfo = null;
        if (this.accountDeletionService) {
            try {
                preservedDataInfo = await this.accountDeletionService.checkPreservedData(newUserRequest.email);
                if (preservedDataInfo.hasPreservedData) {
                    console.log('🔍 Found preserved data for email:', newUserRequest.email);
                    console.log('📊 Preserved data summary:', preservedDataInfo.preservedDataSummary);
                }
            }
            catch (error) {
                console.log('⚠️ Error checking preserved data:', error.message);
            }
        }
        // Check if email is disposable (optional - warn but allow)
        try {
            if (this.securityService) {
                const isDisposable = await this.securityService.isDisposableEmail(newUserRequest.email);
                if (isDisposable) {
                    console.log(`⚠️ Disposable email detected: ${newUserRequest.email}`);
                    // Allow registration but log the warning
                }
            }
        }
        catch (error) {
            console.log('⚠️ Email validation service unavailable, skipping check');
        }
        console.log('🔍 Step 1: Email validation completed');
        // Validate password strength
        console.log('🔍 Step 2: Starting password validation');
        if (this.securityService) {
            const passwordValidation = await this.securityService.validatePasswordStrength(newUserRequest.password);
            if (!passwordValidation.isValid) {
                throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
            }
            console.log('🔍 Step 2: Password validation passed');
        }
        else {
            // Basic password validation if service is not available
            if (newUserRequest.password.length < 8) {
                throw new rest_1.HttpErrors.BadRequest('Password must be at least 8 characters long');
            }
            console.log('🔍 Step 2: Basic password validation passed');
        } // Check if user already exists
        console.log('🔍 Step 3: Checking if user exists');
        const foundUser = await this.userRepository.findOne({
            where: { email: newUserRequest.email },
        });
        if (foundUser) {
            console.log('❌ Step 3: User already exists');
            // Special case: Check if this is an inconsistent state where user exists but has preserved data
            if (preservedDataInfo && preservedDataInfo.hasPreservedData) {
                console.log('🔍 Detected inconsistent state: User exists but has preserved data');
                console.log('🔍 Found user ID:', foundUser.id);
                console.log('🔍 Preserved data original user ID:', preservedDataInfo.deletionRecord?.originalUserId);
                if (foundUser.id === preservedDataInfo.deletionRecord?.originalUserId) {
                    throw new rest_1.HttpErrors.BadRequest('Account deletion was incomplete. Your account was not properly deleted during the deletion process. ' +
                        'Please contact support to resolve this issue.');
                }
                else {
                    throw new rest_1.HttpErrors.BadRequest('A user account exists with this email, but it\'s different from your previously deleted account. ' +
                        'This suggests an incomplete account deletion process. Please contact support.');
                }
            }
            throw new rest_1.HttpErrors.Conflict('Email already exists');
        }
        console.log('🔍 Step 3: User does not exist, proceeding');
        // Hash password
        let password;
        if (this.securityService) {
            password = await this.securityService.hashPassword(newUserRequest.password);
        }
        else {
            // Fallback to bcrypt directly
            const bcrypt = require('bcryptjs');
            password = await bcrypt.hash(newUserRequest.password, 12);
        }
        // Generate email verification token (optional)
        let emailVerificationToken;
        let emailVerificationExpires;
        try {
            if (this.securityService) {
                emailVerificationToken = await this.securityService.generateEmailVerificationToken();
                emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
            }
        }
        catch (error) {
            console.log('⚠️ Email verification token generation failed, proceeding without it');
        } // Create user
        const savedUser = await this.userRepository.create({
            email: newUserRequest.email,
            firstName: newUserRequest.firstName,
            lastName: newUserRequest.lastName,
            phone: newUserRequest.phone,
            password: password,
            emailVerificationToken,
            emailVerificationExpires,
            emailVerified: false, // Require email verification before login
            roles: ['user'],
        });
        // Send verification email (optional)
        try {
            if (emailVerificationToken && this.emailService) {
                await this.emailService.sendVerificationEmail(savedUser.email, emailVerificationToken);
                console.log(`📧 Verification email sent to ${savedUser.email}`);
            }
        }
        catch (error) {
            console.error('Failed to send verification email:', error);
            // Don't fail registration if email sending fails
        }
        return {
            message: preservedDataInfo?.hasPreservedData
                ? 'User registered successfully. You have preserved data available for restoration.'
                : 'User registered successfully. You can now log in.',
            userId: savedUser.id,
            hasPreservedData: preservedDataInfo?.hasPreservedData || false,
            preservedDataSummary: preservedDataInfo?.preservedDataSummary,
        };
    }
    async login(credentials) {
        console.log('🔍 Login attempt for:', credentials.email);
        console.log('🔍 Credentials received:', {
            email: credentials.email,
            hasPassword: !!credentials.password,
            hasTwoFactorToken: !!credentials.twoFactorToken,
            hasRecoveryCode: !!credentials.recoveryCode,
            recoveryCode: credentials.recoveryCode ? 'PROVIDED' : 'NOT PROVIDED'
        });
        // Verify credentials manually
        const user = await this.verifyCredentials(credentials); // Check if email is verified - enforce verification
        if (!user.emailVerified) {
            console.log(`❌ User ${user.email} attempting login with unverified email`);
            throw new rest_1.HttpErrors.Unauthorized('Please verify your email before logging in. Check your inbox for a verification link.');
        } // Check if 2FA is enabled
        if (user.twoFactorEnabled) {
            if (!credentials.twoFactorToken && !credentials.recoveryCode) {
                return {
                    token: '',
                    user,
                    requiresTwoFactor: true,
                };
            }
            let isValid2FA = false; // Check recovery code first (if provided)
            if (credentials.recoveryCode) {
                console.log('🔄 Attempting recovery code verification for user:', user.email);
                if (this.recoveryCodeService) {
                    try {
                        isValid2FA = await this.recoveryCodeService.validateAndConsumeRecoveryCode(user.id, credentials.recoveryCode);
                        if (isValid2FA) {
                            console.log('✅ Recovery code validation successful for user:', user.email);
                            // Check if all recovery codes are now used after this validation
                            try {
                                const allCodesUsed = await this.recoveryCodeService.areAllCodesUsed(user.id);
                                if (allCodesUsed) {
                                    console.log('⚠️ All recovery codes have been used for user:', user.email);
                                    // This information could be used by the frontend to show disable 2FA option
                                }
                            }
                            catch (error) {
                                console.log('⚠️ Could not check recovery codes status:', error);
                            }
                        }
                        else {
                            console.log('❌ Recovery code validation failed for user:', user.email);
                            // Check if all codes are already used to provide helpful error message
                            try {
                                const allCodesUsed = await this.recoveryCodeService.areAllCodesUsed(user.id);
                                if (allCodesUsed) {
                                    console.log('⚠️ All recovery codes are exhausted for user:', user.email);
                                    throw new rest_1.HttpErrors.Unauthorized('All recovery codes have been used. Please use your authenticator app or request to disable 2FA via email.');
                                }
                            }
                            catch (checkError) {
                                console.log('⚠️ Could not check recovery codes status:', checkError);
                            }
                        }
                    }
                    catch (error) {
                        console.error('❌ Recovery code validation error:', error);
                        if (error instanceof rest_1.HttpErrors.HttpError) {
                            throw error;
                        }
                        throw new rest_1.HttpErrors.Unauthorized('Invalid recovery code');
                    }
                }
                else {
                    throw new rest_1.HttpErrors.Unauthorized('Recovery code service unavailable');
                }
            }
            // Check TOTP token (if provided and recovery code wasn't valid)
            else if (credentials.twoFactorToken) {
                console.log('🔄 Attempting TOTP verification for user:', user.email);
                if (this.securityService) {
                    isValid2FA = await this.securityService.verifyTwoFactorToken(user.id, credentials.twoFactorToken);
                    if (isValid2FA) {
                        console.log('✅ TOTP verification successful for user:', user.email);
                    }
                    else {
                        console.log('❌ TOTP verification failed for user:', user.email);
                    }
                }
                else {
                    throw new rest_1.HttpErrors.Unauthorized('Two-factor authentication service unavailable');
                }
            }
            if (!isValid2FA) {
                console.log('❌ 2FA verification failed for user:', user.email);
                throw new rest_1.HttpErrors.Unauthorized('Invalid two-factor authentication token or recovery code');
            }
        }
        // Convert a User object into a UserProfile object (reduced set of properties)
        const userProfile = this.convertToUserProfile(user);
        // Create a JSON Web Token based on the user profile
        const token = await this.jwtService.generateToken(userProfile);
        return { token, user };
    }
    async verifyEmail(request) {
        console.log('🔍 Verify email endpoint called with token:', request.token?.substring(0, 20) + '...');
        console.log('🔍 Token length:', request.token?.length);
        console.log('🔍 Full token (for debugging):', request.token);
        const user = await this.userRepository.findOne({
            where: {
                emailVerificationToken: request.token,
                emailVerificationExpires: { gt: new Date() },
            },
        });
        console.log('🔍 User found with token:', user ? 'YES' : 'NO');
        if (user) {
            console.log('🔍 User email:', user.email);
            console.log('🔍 Token expires:', user.emailVerificationExpires);
            console.log('🔍 Current time:', new Date());
        }
        else {
            // Let's also check if there's a user with this token but expired
            const expiredUser = await this.userRepository.findOne({
                where: {
                    emailVerificationToken: request.token,
                },
            });
            if (expiredUser) {
                console.log('❌ Token found but expired. Expires:', expiredUser.emailVerificationExpires);
                console.log('❌ Current time:', new Date());
            }
            else {
                console.log('❌ No user found with this token at all');
                // Let's check if there are any users with verification tokens
                const usersWithTokens = await this.userRepository.find({
                    where: {
                        emailVerificationToken: { neq: null }
                    },
                    fields: ['email', 'emailVerificationToken', 'emailVerificationExpires']
                });
                console.log('🔍 Users with verification tokens:', usersWithTokens.length);
                usersWithTokens.forEach((u, i) => {
                    console.log(`🔍 User ${i + 1}: ${u.email}, token: ${u.emailVerificationToken?.substring(0, 20)}..., expires: ${u.emailVerificationExpires}`);
                });
            }
        }
        if (!user) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired verification token');
        }
        await this.userRepository.updateById(user.id, {
            emailVerified: true,
            emailVerificationToken: undefined,
            emailVerificationExpires: undefined,
            updatedAt: new Date(),
        }); // Send verification confirmation email
        try {
            if (user?.email && this.emailService) {
                await this.emailService.sendVerificationConfirmationEmail(user.email, user.firstName || 'User');
            }
        }
        catch (emailError) {
            console.error('⚠️ Failed to send verification confirmation email:', emailError);
            // Don't fail the verification if email sending fails
        }
        return {
            message: 'Email verified successfully',
            user: {
                email: user?.email || '',
                firstName: user?.firstName || 'User',
                id: user?.id || ''
            }
        };
    }
    async resendVerification(request) {
        const user = await this.userRepository.findOne({
            where: { email: request.email },
        });
        if (!user) {
            // Don't reveal if email exists or not
            return { message: 'If the email exists and is unverified, a verification link has been sent' };
        }
        if (user.emailVerified) {
            return { message: 'Email is already verified' };
        }
        // Generate new verification token
        let emailVerificationToken;
        let emailVerificationExpires;
        if (this.securityService) {
            emailVerificationToken = await this.securityService.generateEmailVerificationToken();
            emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        }
        else {
            throw new rest_1.HttpErrors.ServiceUnavailable('Security service is not available');
        }
        await this.userRepository.updateById(user.id, {
            emailVerificationToken,
            emailVerificationExpires,
            updatedAt: new Date(),
        });
        // Send verification email
        try {
            if (this.emailService) {
                await this.emailService.sendVerificationEmail(user.email, emailVerificationToken);
                console.log(`📧 Verification email resent to ${user.email}`);
            }
        }
        catch (error) {
            console.error('Failed to resend verification email:', error);
            throw new rest_1.HttpErrors.InternalServerError('Failed to send verification email');
        }
        return { message: 'If the email exists and is unverified, a verification link has been sent' };
    }
    async forgotPassword(request) {
        if (!request.email && !request.phone) {
            throw new rest_1.HttpErrors.BadRequest('Either email or phone is required');
        }
        let user = null;
        if (request.email) {
            user = await this.userRepository.findOne({
                where: { email: request.email },
            });
        }
        else if (request.phone) {
            user = await this.userRepository.findOne({
                where: { phone: request.phone },
            });
        }
        if (!user) {
            // Don't reveal if email/phone exists or not
            return { message: 'If the email/phone exists, a password reset link has been sent' };
        }
        if (!this.securityService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Security service is not available');
        }
        const resetToken = await this.securityService.generatePasswordResetToken();
        const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
        await this.userRepository.updateById(user.id, {
            passwordResetToken: resetToken,
            passwordResetExpires: resetExpires,
            updatedAt: new Date(),
        });
        try {
            if (this.emailService) {
                await this.emailService.sendPasswordResetEmail(user.email, resetToken);
            }
        }
        catch (error) {
            console.error('Failed to send password reset email:', error);
        }
        return { message: 'If the email exists, a password reset link has been sent' };
    }
    async resetPassword(request) {
        const user = await this.userRepository.findOne({
            where: {
                passwordResetToken: request.token,
                passwordResetExpires: { gt: new Date() },
            },
        });
        if (!user) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired reset token');
        }
        // Validate password strength
        if (!this.securityService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Security service is not available');
        }
        const passwordValidation = await this.securityService.validatePasswordStrength(request.password);
        if (!passwordValidation.isValid) {
            throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
        // Hash new password
        const hashedPassword = await this.securityService.hashPassword(request.password); // Update password, clear lockout state, and clear reset token in one operation
        await this.userRepository.updateById(user.id, { password: hashedPassword,
            loginAttempts: 0, // Reset failed login attempts
            lockUntil: undefined, // Clear account lockout
            passwordResetToken: undefined, // Clear reset token
            passwordResetExpires: undefined,
            updatedAt: new Date(),
        });
        return { message: 'Password reset successfully' };
    }
    async getProfile() {
        const userId = this.user[security_1.securityId];
        console.log('🔍 Profile request for user:', userId);
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Return user data without sensitive fields
        const { password, passwordResetToken, passwordResetExpires, emailVerificationToken, emailVerificationExpires, twoFactorSecret, ...safeUserData } = user;
        return safeUserData;
    }
    async getCurrentUser() {
        const userId = this.user[security_1.securityId];
        console.log('🔍 Current user request for user:', userId);
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Return user data without sensitive fields
        const { password, passwordResetToken, passwordResetExpires, emailVerificationToken, emailVerificationExpires, twoFactorSecret, ...safeUserData } = user;
        return safeUserData;
    }
    async updateProfile(profileData) {
        const userId = this.user[security_1.securityId];
        console.log('🔍 Profile update request for user:', userId);
        console.log('🔍 Update data:', profileData);
        // Get current user
        const currentUser = await this.userRepository.findById(userId);
        if (!currentUser) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // If email is being changed, check if it's already taken
        if (profileData.email && profileData.email !== currentUser.email) {
            const existingUser = await this.userRepository.findOne({
                where: { email: profileData.email },
            });
            if (existingUser) {
                throw new rest_1.HttpErrors.BadRequest('Email address is already in use');
            }
            // If email changed, mark as unverified and generate new verification token
            if (this.securityService) {
                try {
                    const emailVerificationToken = await this.securityService.generateEmailVerificationToken();
                    const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
                    profileData = {
                        ...profileData,
                        emailVerified: false,
                        emailVerificationToken,
                        emailVerificationExpires,
                    };
                    // Send verification email for new email
                    if (this.emailService) {
                        await this.emailService.sendVerificationEmail(profileData.email, emailVerificationToken);
                        console.log(`📧 Verification email sent to new address: ${profileData.email}`);
                    }
                }
                catch (error) {
                    console.warn('⚠️ Failed to generate email verification token or send email:', error);
                }
            }
        }
        // Update user profile
        await this.userRepository.updateById(userId, {
            ...profileData,
            updatedAt: new Date(),
        });
        // Get updated user data
        const updatedUser = await this.userRepository.findById(userId);
        const { password, passwordResetToken, passwordResetExpires, emailVerificationToken, emailVerificationExpires, ...safeUser } = updatedUser;
        console.log('✅ Profile updated successfully for user:', userId);
        const message = profileData.email && profileData.email !== currentUser.email
            ? 'Profile updated successfully! Please check your new email address to verify it.'
            : 'Profile updated successfully!';
        return {
            message,
            user: safeUser,
        };
    }
    async changePassword(request) {
        const userId = this.user[security_1.securityId];
        console.log('🔍 Password change request for user:', userId);
        // Get current user
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Check if account is locked
        if (user.lockUntil && user.lockUntil > new Date()) {
            const lockTimeRemaining = Math.ceil((user.lockUntil.getTime() - Date.now()) / (1000 * 60));
            throw new rest_1.HttpErrors.Unauthorized(`Account is locked. Please try again in ${lockTimeRemaining} minutes.`);
        }
        // For OAuth users or users without a current password, skip current password verification
        const isOAuthUser = !!(user.oauthProvider && (user.googleId || user.githubId || user.microsoftId));
        const hasCurrentPassword = !!user.password;
        if (!isOAuthUser && hasCurrentPassword) {
            // Regular users must provide current password
            if (!request.currentPassword) {
                throw new rest_1.HttpErrors.BadRequest('Current password is required');
            } // Verify current password
            const passwordMatched = await (0, bcryptjs_1.compare)(request.currentPassword, user.password);
            if (!passwordMatched) {
                // Increment failed attempts and potentially lock account
                await this.handleFailedLogin(user);
                throw new rest_1.HttpErrors.Unauthorized('Current password is incorrect');
            }
        }
        // Validate new password strength
        if (this.securityService) {
            try {
                const passwordValidation = await this.securityService.validatePasswordStrength(request.newPassword);
                if (!passwordValidation.isValid) {
                    throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
                }
            }
            catch (error) {
                // Basic password validation if service is not available
                if (request.newPassword.length < 8) {
                    throw new rest_1.HttpErrors.BadRequest('Password must be at least 8 characters long');
                }
            }
        }
        else {
            // Basic password validation if service is not available
            if (request.newPassword.length < 8) {
                throw new rest_1.HttpErrors.BadRequest('Password must be at least 8 characters long');
            }
        }
        // If 2FA is enabled, verify the token
        if (user.twoFactorEnabled && user.twoFactorSecret) {
            if (!request.twoFactorToken) {
                throw new rest_1.HttpErrors.BadRequest('Two-factor authentication token is required');
            }
            if (this.securityService) {
                const isValidToken = await this.securityService.verifyTwoFactorToken(user.twoFactorSecret, request.twoFactorToken);
                if (!isValidToken) {
                    throw new rest_1.HttpErrors.Unauthorized('Invalid two-factor authentication token');
                }
            }
        }
        // Hash the new password
        let hashedPassword;
        if (this.securityService) {
            hashedPassword = await this.securityService.hashPassword(request.newPassword);
        }
        else {
            // Fallback hashing if service is not available
            const bcrypt = require('bcryptjs');
            const saltRounds = 12;
            hashedPassword = await bcrypt.hash(request.newPassword, saltRounds);
        }
        // Update password and reset any lockout state
        await this.userRepository.updateById(userId, {
            password: hashedPassword,
            loginAttempts: 0,
            lockUntil: undefined,
            updatedAt: new Date(),
        });
        console.log('✅ Password changed successfully for user:', userId);
        const message = isOAuthUser || !hasCurrentPassword
            ? 'Password set successfully! You can now log in using your email and password.'
            : 'Password changed successfully!';
        return { message };
    }
    // Helper methods for authentication
    async verifyCredentials(credentials) {
        const invalidCredentialsError = 'Invalid email or password.';
        const foundUser = await this.userRepository.findOne({
            where: { email: credentials.email },
        });
        if (!foundUser) {
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        // Check if user is active
        if (!foundUser.isActive) {
            throw new rest_1.HttpErrors.Unauthorized('Account is deactivated.');
        } // Check if account is locked
        if (foundUser.lockUntil && foundUser.lockUntil > new Date()) {
            const lockTimeRemaining = Math.ceil((foundUser.lockUntil.getTime() - Date.now()) / (1000 * 60));
            throw new rest_1.HttpErrors.Unauthorized(`Account is temporarily locked due to multiple failed login attempts. ` +
                `Please try again in ${lockTimeRemaining} minutes. ` +
                `To unlock immediately: use "Forgot Password" to reset your password, ` +
                `login with OTP/SMS verification, or contact support for assistance.`);
        }
        if (!foundUser.password) {
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        const passwordMatched = await (0, bcryptjs_1.compare)(credentials.password, foundUser.password);
        if (!passwordMatched) {
            // Increment login attempts
            await this.handleFailedLogin(foundUser);
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        // Reset login attempts on successful login
        await this.handleSuccessfulLogin(foundUser);
        return foundUser;
    }
    convertToUserProfile(user) {
        return {
            [security_1.securityId]: user.id.toString(),
            name: `${user.firstName} ${user.lastName}`,
            id: user.id,
            email: user.email,
            roles: user.roles,
        };
    }
    async handleFailedLogin(user) {
        const maxAttempts = 5;
        const lockTime = 30 * 60 * 1000; // 30 minutes
        const attempts = (user.loginAttempts || 0) + 1;
        const updateData = {
            loginAttempts: attempts,
            updatedAt: new Date(),
        };
        if (attempts >= maxAttempts) {
            updateData.lockUntil = new Date(Date.now() + lockTime);
        }
        await this.userRepository.updateById(user.id, updateData);
    }
    async handleSuccessfulLogin(user) {
        await this.userRepository.updateById(user.id, {
            loginAttempts: 0,
            lockUntil: undefined,
            lastLoginAt: new Date(),
            updatedAt: new Date(),
        });
    }
    // Test endpoint to help debug password reset flow
    async getResetTokenForTesting(email) {
        // Only allow in development
        if (process.env.NODE_ENV === 'production') {
            throw new rest_1.HttpErrors.NotFound('Endpoint not available in production');
        }
        const user = await this.userRepository.findOne({
            where: { email: email.toLowerCase() },
        });
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        return {
            token: user.passwordResetToken || null,
            email: user.email,
            expires: user.passwordResetExpires?.toISOString() || null,
        };
    }
    // Test endpoint to check user lockout status
    async getUserStatusForTesting(email) {
        // Only allow in development
        if (process.env.NODE_ENV === 'production') {
            throw new rest_1.HttpErrors.NotFound('Endpoint not available in production');
        }
        const user = await this.userRepository.findOne({
            where: { email: email.toLowerCase() },
        });
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        const isLocked = user.lockUntil && user.lockUntil > new Date();
        return {
            email: user.email,
            loginAttempts: user.loginAttempts || 0,
            lockUntil: user.lockUntil?.toISOString() || null,
            isLocked: !!isLocked,
        };
    }
    // 2FA Disable Flow Endpoints
    async requestDisable2FA(request) {
        if (!this.twofaDisableService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('2FA disable service is not available');
        }
        console.log(`🔐 2FA disable request received for email: ${request.email}, reason: ${request.reason || 'not specified'}`);
        try {
            // Optional: Check if all recovery codes are used (for additional validation)
            let allCodesUsed = false;
            try {
                if (this.recoveryCodeService) {
                    const user = await this.userRepository.findOne({
                        where: { email: request.email.toLowerCase().trim() }
                    });
                    if (user) {
                        allCodesUsed = await this.recoveryCodeService.areAllCodesUsed(user.id);
                    }
                }
            }
            catch (error) {
                console.log('⚠️ Could not check recovery code status, proceeding with request');
            }
            await this.twofaDisableService.requestDisable2FA(request.email, request.reason);
            return {
                message: 'If an account exists with 2FA enabled, a disable confirmation email has been sent. The link will expire in 1 hour.',
                allCodesUsed,
            };
        }
        catch (error) {
            console.error('❌ Failed to process 2FA disable request:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to process 2FA disable request');
        }
    }
    async confirmDisable2FA(request) {
        if (!this.twofaDisableService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('2FA disable service is not available');
        }
        console.log(`🔐 Processing 2FA disable confirmation with token: ${request.token.substring(0, 8)}...`);
        try {
            const result = await this.twofaDisableService.processDisable2FA(request.token);
            return result;
        }
        catch (error) {
            console.error('❌ Failed to confirm 2FA disable:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to process 2FA disable confirmation');
        }
    }
    async getDisable2FAStatus(email) {
        // Only allow in development for testing
        if (process.env.NODE_ENV === 'production') {
            throw new rest_1.HttpErrors.NotFound('Endpoint not available in production');
        }
        if (!this.twofaDisableService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('2FA disable service is not available');
        }
        try {
            const user = await this.userRepository.findOne({
                where: { email: email.toLowerCase() }
            });
            if (!user) {
                return { hasPendingRequest: false, email };
            }
            const hasPendingRequest = await this.twofaDisableService.hasPendingDisableRequest(user.id);
            return {
                hasPendingRequest,
                email: user.email,
            };
        }
        catch (error) {
            console.error('❌ Failed to check 2FA disable status:', error);
            throw new rest_1.HttpErrors.InternalServerError('Failed to check 2FA disable status');
        }
    }
};
exports.AuthController = AuthController;
tslib_1.__decorate([
    (0, rest_1.post)('/auth/signup'),
    (0, rest_1.response)(200, {
        description: 'User registration',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        userId: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'firstName', 'lastName', 'password'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        firstName: { type: 'string', minLength: 2, maxLength: 50 },
                        lastName: { type: 'string', minLength: 2, maxLength: 50 },
                        phone: { type: 'string' },
                        password: { type: 'string', minLength: 8 },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "signUp", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/login'),
    (0, rest_1.response)(200, {
        description: 'Token',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: (0, rest_1.getModelSchemaRef)(models_1.User, { exclude: ['password'] }),
                        requiresTwoFactor: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'password'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        password: { type: 'string', minLength: 8 },
                        twoFactorToken: { type: 'string' },
                        recoveryCode: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/verify-email'),
    (0, rest_1.response)(200, {
        description: 'Email verification',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        user: {
                            type: 'object',
                            properties: {
                                email: { type: 'string' },
                                firstName: { type: 'string' },
                                id: { type: 'string' }
                            }
                        }
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "verifyEmail", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/resend-verification'),
    (0, rest_1.response)(200, {
        description: 'Resend email verification',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "resendVerification", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/forgot-password'),
    (0, rest_1.response)(200, {
        description: 'Password reset request',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        email: { type: 'string', format: 'email' },
                        phone: { type: 'string' },
                    },
                    anyOf: [
                        { required: ['email'] },
                        { required: ['phone'] }
                    ],
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/reset-password'),
    (0, rest_1.response)(200, {
        description: 'Password reset',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token', 'password'],
                    properties: {
                        token: { type: 'string' },
                        password: { type: 'string', minLength: 8 },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/profile'),
    (0, authentication_1.authenticate)('jwt'),
    (0, rest_1.response)(200, {
        description: 'Get user profile',
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.User, { exclude: ['password', 'passwordResetToken', 'passwordResetExpires', 'emailVerificationToken', 'emailVerificationExpires', 'twoFactorSecret'] }),
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/me'),
    (0, authentication_1.authenticate)('jwt'),
    (0, rest_1.response)(200, {
        description: 'Get current user data (alias for /auth/profile)',
        content: {
            'application/json': {
                schema: (0, rest_1.getModelSchemaRef)(models_1.User, { exclude: ['password', 'passwordResetToken', 'passwordResetExpires', 'emailVerificationToken', 'emailVerificationExpires', 'twoFactorSecret'] }),
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "getCurrentUser", null);
tslib_1.__decorate([
    (0, rest_1.patch)('/auth/profile'),
    (0, authentication_1.authenticate)('jwt'),
    (0, rest_1.response)(200, {
        description: 'Update user profile',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        user: (0, rest_1.getModelSchemaRef)(models_1.User, { exclude: ['password', 'passwordResetToken', 'passwordResetExpires', 'emailVerificationToken', 'emailVerificationExpires'] }),
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        firstName: { type: 'string', minLength: 2, maxLength: 50 },
                        lastName: { type: 'string', minLength: 2, maxLength: 50 },
                        email: { type: 'string', format: 'email' },
                        phone: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "updateProfile", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/change-password'),
    (0, authentication_1.authenticate)('jwt'),
    (0, rest_1.response)(200, {
        description: 'Change user password',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        currentPassword: { type: 'string' },
                        newPassword: { type: 'string', minLength: 8 },
                        twoFactorToken: { type: 'string', minLength: 6, maxLength: 6 },
                    },
                    required: ['newPassword'],
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
tslib_1.__decorate([
    (0, rest_1.get)('/test/get-reset-token/{email}'),
    (0, rest_1.response)(200, {
        description: 'Get reset token for testing (development only)',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        email: { type: 'string' },
                        expires: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('email')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "getResetTokenForTesting", null);
tslib_1.__decorate([
    (0, rest_1.get)('/test/user-status/{email}'),
    (0, rest_1.response)(200, {
        description: 'Get user lockout status for testing (development only)',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        email: { type: 'string' },
                        loginAttempts: { type: 'number' },
                        lockUntil: { type: 'string' },
                        isLocked: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('email')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "getUserStatusForTesting", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/request-disable-2fa'),
    (0, rest_1.response)(200, {
        description: 'Request to disable 2FA via email (for when recovery codes are exhausted)',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        allCodesUsed: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        reason: { type: 'string', enum: ['recovery_codes_exhausted', 'lost_device', 'other'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "requestDisable2FA", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/confirm-disable-2fa'),
    (0, rest_1.response)(200, {
        description: 'Confirm and process 2FA disable via token from email',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        message: { type: 'string' },
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                email: { type: 'string' },
                                firstName: { type: 'string' },
                                lastName: { type: 'string' },
                            },
                        },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string', minLength: 32 },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "confirmDisable2FA", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/disable-2fa-status/{email}'),
    (0, rest_1.response)(200, {
        description: 'Check if user has pending 2FA disable request (development/testing only)',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        hasPendingRequest: { type: 'boolean' },
                        email: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('email')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "getDisable2FAStatus", null);
exports.AuthController = AuthController = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(authentication_jwt_1.TokenServiceBindings.TOKEN_SERVICE)),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER, { optional: true })),
    tslib_1.__param(2, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(3, (0, core_1.inject)('services.SecurityService', { optional: true })),
    tslib_1.__param(4, (0, core_1.inject)('services.EmailService', { optional: true })),
    tslib_1.__param(5, (0, core_1.inject)('services.SmsService', { optional: true })),
    tslib_1.__param(6, (0, core_1.inject)('services.RecoveryCodeService', { optional: true })),
    tslib_1.__param(7, (0, core_1.inject)('services.TwoFactorDisableService', { optional: true })),
    tslib_1.__param(8, (0, core_1.inject)('services.AccountDeletionService', { optional: true })),
    tslib_1.__metadata("design:paramtypes", [Object, Object, repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService,
        services_1.RecoveryCodeService,
        services_1.TwoFactorDisableService,
        services_1.AccountDeletionService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map