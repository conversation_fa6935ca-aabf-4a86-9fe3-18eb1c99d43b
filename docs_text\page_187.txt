=== Page 188 ===

188Notifications
Masonite has a simple yet powerful notification feature which is used to send
notifications from your application. Here is a brief overview of what you can do with
notifications:
•Send E-mail, Slack and SMS notifications
•Store notifications in a database so they may be displayed in your web interface.
•Queue notifications
•Broadcast notifications
To create and send a notification with Masonite, you must first build a Notification
class. This class will act as the settings for your notification such as the delivery
channels and the content of the notification for those different channels (mail, slack,
sms, ...).
The first step of building a notification is running the command:
This will create your notification and it will look something like this:$ python craft notification Welcome
class Welcome(Notification, Mailable):
    def to_mail(self, notifiable):
        return (
            self.to(notifiable.email)
            .subject("Welcome to our site!")
            .from_("<EMAIL>")
            .text(f"Hello {notifiable.name}")
        )
    def via(self, notifiable):
        return ["mail"]Creating a Notification6/12/25, 3:02 AM Masonite Documentation