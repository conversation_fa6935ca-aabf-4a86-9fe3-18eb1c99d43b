=== Page 218 ===

218Queues and Jobs
Masonite ships with a powerful queue system. This feature is useful for running those
tasks that take a while like sending emails, processing videos, creating invoices, updating
records and anything else you don't need you users to wait for or jobs that need .
First, jobs are creating with the logic required to make the job run. The jobs are then
"pushed" onto the queue where they will run later using "queue workers". You can specify
as many queue workers as your server can run.
In addition to running jobs, some drivers allow you to monitor any jobs that fail. Job data
will save into the database where they can be monitored and reran if needed.
You can easily modify the behavior of your queue system by changing the queue
configuration:
The available queue drivers are: async, database and amqp.
A full queue configuration will look like this:Configuration6/12/25, 3:02 AM Masonite Documentation