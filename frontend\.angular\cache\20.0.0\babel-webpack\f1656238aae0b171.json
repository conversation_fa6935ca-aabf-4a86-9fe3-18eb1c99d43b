{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nlet ResetPasswordComponent = class ResetPasswordComponent {\n  constructor(fb, authService, router, route, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.token = null;\n    this.isTokenValid = true;\n  }\n  ngOnInit() {\n    // Get token from URL query parameters\n    this.token = this.route.snapshot.queryParamMap.get('token');\n    if (!this.token) {\n      this.isTokenValid = false;\n      this.snackBar.open('Invalid reset link. Please request a new password reset.', 'Close', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(group) {\n    const password = group.get('password');\n    const confirmPassword = group.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        mismatch: true\n      });\n      return {\n        mismatch: true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.resetPasswordForm.valid && !this.isLoading && this.token) {\n      this.isLoading = true;\n      const password = this.resetPasswordForm.get('password')?.value;\n      this.authService.resetPassword({\n        token: this.token,\n        password: password,\n        confirmPassword: password\n      }).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Password reset successfully! You can now login with your new password.', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Redirect to login page after successful reset\n          setTimeout(() => {\n            this.router.navigate(['/auth/login']);\n          }, 2000);\n        },\n        error: error => {\n          this.isLoading = false;\n          const errorMessage = error?.error?.message || 'Failed to reset password. Please try again.';\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          // If token is invalid, redirect to forgot password\n          if (errorMessage.includes('Invalid or expired')) {\n            setTimeout(() => {\n              this.router.navigate(['/auth/forgot-password']);\n            }, 3000);\n          }\n        }\n      });\n    }\n  }\n  goBackToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  requestNewReset() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n};\nResetPasswordComponent = __decorate([Component({\n  selector: 'app-reset-password',\n  templateUrl: './reset-password.component.html',\n  styleUrls: ['./reset-password.component.scss'],\n  standalone: false\n})], ResetPasswordComponent);\nexport { ResetPasswordComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}