=== Page 106 ===

106•Accessing mail.from_email will return the FROM_EMAIL value
•Accessing mail.drivers.smtp.port will return the port value for smtp driver.
To read a configuration value one can use the Config facade:
or the config helper:
Setting configuration values is achieved through projet configuration files.
However, you can override on the fly a configuration value with the Config facade:
This should be done sparingly as this could have unexpected side effects depending atfrom masonite.facades import Config
Config.get("mail.from_email")
# a default value can be provided
Config.get("database.mysql.port", 3306)
from masonite.configuration import config
config("mail.from_email")
# a default value can be provided
config("database.mysql.port", 3306)
Config.set("mail.from_email", "<EMAIL>")
Getting Value
Setting Value
Overriding Value6/12/25, 3:02 AM Masonite Documentation