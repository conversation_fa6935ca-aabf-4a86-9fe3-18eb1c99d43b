=== Page 180 ===

180Y<PERSON> can also generate just a path:
It accepts a dictionary to add query string parameters when building the route url:
The compact helper is a shortcut helper when you want to compile a dictionary from
variables.
There are times when you will have instances like this:
Notice we repeated the users and articles key the same as the variables name. In
this case we can use the compact helper to clean the code up a bit:url.route("users.profile", {"id": 1}) #== 
http://masonite.app/users/1/profile/
url.route("users.profile", {"id": 1}, absolute=False) #== 
/users/1/profile/
url.route("user.profile", {"id": 1}, query_params={"preview": 1})
#== http://masonite.app/users/1/profile/?preview=1
from masonite.view import View
def show(self, view: View):
  users = User.all()
  articles = Article.all()
  return view.render('some.view', {"users": users, "articles": 
articles})Compact6/12/25, 3:02 AM Masonite Documentation