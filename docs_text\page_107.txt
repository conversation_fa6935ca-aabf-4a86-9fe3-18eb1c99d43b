=== Page 108 ===

108Error Handling
Masonite error handling is based on the ExceptionHandler class which is responsible
for handling all exceptions thrown by your application.
All exceptions are handled by the ExceptionHandler class which is bound to the Service
Container through exception_handler key.
This handler has a logic to decide how to handle exceptions depending on the exception
type, the environment type, the request accepted content type and the configured
exception handlers.
This handler has by default one driver Exceptionite which is responsible of handling
errors in development by providing a lot of context to help debug your application.
When Debug mode is enabled all exceptions are rendered through Exceptionite HTML
debug page.
When disabled, the default errors/500.html, errors/404.html, errors/403.html
error pages are rendered depending on error type.
Never deploy an application in production with debug mode enabled ! This could lead to
expose some sensitive configuration data and environment variables to the end user.
When an exception is raised it will be caught by the ExceptionHandler. Then the following
cycle will happen:
Global Exception Handler
Debug Mode
Lifecycle6/12/25, 3:02 AM Masonite Documentation