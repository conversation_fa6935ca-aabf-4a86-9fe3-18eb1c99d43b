=== Page 409 ===

409This is a very small change but will save a lot of time when upgrading your application.
Now anytime you resolve the Auth class you will get an instance of a Guard class
instead. The use of both classes are identical so they should just work as is.
(This swap may be removed in Masonite 2.4+)
All the authentication stuff in the previous improvements have been abstracted to their
own provider so you will need to a add a new provider to your providers list.
The Auth class now contains a new method which returns a list of routes. This cleans
up the web.py file nicely when scaffolding.
The container can not longer have modules bound to it. These should instead be
imported.
Added a few new assertion methods to help chaining methods and keeping tests short
and fast. These include assertHasHeader and assertNotHasHeader.Swapped the Auth class for a Guard class
Added A New AuthenticationProvider
Auth Scaffolding Now Imports Auth Class
Removed The Ability For The Container To Hold
Modules
Added Several New Assertions6/12/25, 3:02 AM Masonite Documentation