=== Page 57 ===

57Controllers
Controllers are a place where most of your business logic will be. Controllers are where
you put the responses you see in the web browser. Responses can be dictionaries, lists,
views or any class that can render a response.
You may use a craft command to create a new basic controller or simply make a new
controller manually. Controllers are classes with methods that are mapped to a route.
Your route may look something like this:
In this case this route will call the WelcomeController classes show method.
To create a basic controller via a craft command simply run:
This will create a new controller class to get you setup quickly. This controller class will
look like a basic class like this:
You may start building your controller out and adding the responses you need.Route.get('/', 'WelcomeController@show')
$ python craft controller Welcome
from masonite.controllers import Controller
from masonite.views import View
class WelcomeController(Controller):
    def show(self, view: View):
        return view.render("")Introduction6/12/25, 3:02 AM Masonite Documentation