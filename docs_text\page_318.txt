=== Page 319 ===

319As you have noticed, Masonite has exception handling which it uses to display useful
information during development.
This is an issue during testing because we wan't to be able to see more useful testing
related issues. Because of this, testing will disable Masonite's default exceptions
handling and you will see more useful exceptions during testing. If you want to use
Masonite's built in exceptions handling then you can enable it by running:
You can also disable exceptions handling again by using:
Masonite provides a variety of assertions methods to inspect and verify
request/response logic when testing your application. Those assertions are available on
the HTTPTestResponse returned by get, post, put, patch, or delete.
•assertContains
•assertNotContains
•assertContainsInOrder
•assertNoContent
•assertIsNamed
•assertIsNotNameddef setUp(self):
    super().setUp()
    self.withExceptionHandling()
def test_something(self):
    self.withExceptionHandling()
    self.get("/").assertUnauthorized()
    self.withoutExceptionHandling()
    self.get("/")Exceptions handling
Available Assertions6/12/25, 3:02 AM Masonite Documentation