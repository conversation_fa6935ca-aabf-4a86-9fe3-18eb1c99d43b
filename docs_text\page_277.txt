=== Page 278 ===

278•123-456-7890
•(123)456-7890
Every country has their own postal code formats. We added regular expressions for over
130 countries which you can specify by using a comma separated string of country
codes:
Please look up the "alpha-2 code" for available country formats.
Sometimes you want to do more complex validations on some fields. This rule allows to
validate against a regular expression directly. In the following example we check that 
username value is a valid user name (without special characters and between 3 and 16
characters).
Used to make sure the value is actually available in the dictionary and not null. This will
add errors if the key is not present. To check only the presence of the value in the"""
{
  'zip': '123456'
}
"""
validate.postal_code('zip', "US,IN,GB")
"""
{
  'username': 'masonite_user_1'
}
"""
validate.regex('username', pattern='^[a-z0-9_-]{3,16}$'))Postal Code
Regex
Required6/12/25, 3:02 AM Masonite Documentation