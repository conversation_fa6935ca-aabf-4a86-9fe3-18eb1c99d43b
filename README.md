# Website Crawler & Document Generator

A professional-grade website crawling and document extraction system built with Angular frontend and Python/LoopBack backend. Extract content from websites and generate documents in multiple formats (PDF, DOCX, Markdown, HTML, TXT) with flexible organization options.

## 🚀 Features

### Website Crawling
- **Configurable Depth**: Control how deep to crawl (1-10 levels)
- **Content Type Filtering**: Support for HTML, PDF, plain text, JSON, CSS, JavaScript
- **Pattern Matching**: Include/exclude URLs using regex patterns
- **External Link Control**: Choose whether to follow external links
- **Robots.txt Respect**: Optional robots.txt compliance
- **Rate Limiting**: Configurable delays between requests
- **Real-time Progress**: Live progress monitoring with statistics

### Document Generation
- **Multiple Formats**: PDF, DOCX, Markdown, HTML, Plain Text
- **Organization Options**: 
  - Single file with all content
  - Separate files per page
  - Grouped folders by category
- **Customization**: Include/exclude images, table of contents, custom styling
- **Content Selection**: Granular control over which pages to include
- **Download Management**: Secure downloads with access tokens

### User Interface
- **Tabbed Interface**: Intuitive workflow from crawling to document generation
- **Real-time Updates**: Live progress monitoring and status updates
- **Content Management**: Search, filter, and select content easily
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Accessibility**: WCAG compliant with keyboard navigation support

### Security & Performance
- **User Authentication**: Secure JWT-based authentication
- **Data Isolation**: Users can only access their own data
- **Rate Limiting**: Prevents abuse and server overload
- **Optimized Database**: Indexed queries for fast performance
- **Async Processing**: Non-blocking operations for better UX

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Angular UI    │    │  LoopBack API   │    │  PostgreSQL DB  │
│                 │    │                 │    │                 │
│ • Crawler Form  │◄──►│ • Controllers   │◄──►│ • crawl_job     │
│ • Progress View │    │ • Services      │    │ • crawled_content│
│ • Content Mgmt  │    │ • Repositories  │    │ • generated_doc │
│ • Doc Generator │    │ • Models        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Python Scripts │
                       │                 │
                       │ • Web Crawler   │
                       │ • Doc Generator │
                       │ • Progress Mon. │
                       └─────────────────┘
```

## 🛠️ Technology Stack

### Frontend
- **Angular 17+**: Modern web framework with TypeScript
- **Angular Material**: UI components and theming
- **RxJS**: Reactive programming for real-time updates
- **Responsive CSS**: Mobile-first design approach

### Backend
- **LoopBack 4**: Node.js REST API framework
- **TypeScript**: Type-safe backend development
- **PostgreSQL**: Robust relational database
- **JWT Authentication**: Secure token-based auth

### Python Integration
- **BeautifulSoup**: HTML parsing and content extraction
- **Requests**: HTTP client for web crawling
- **ReportLab**: PDF generation
- **python-docx**: Word document generation
- **Markdownify**: HTML to Markdown conversion

## 📋 Prerequisites

- **Node.js**: 18+ or 20+
- **Python**: 3.8+
- **PostgreSQL**: 12+
- **Conda**: For Python environment management

## 🚀 Quick Start

### 1. Clone Repository
```bash
git clone <repository-url>
cd website-crawler
```

### 2. Setup Python Environment
```bash
conda activate masonite-secure-env
pip install -r loopback-backend/scripts/requirements.txt
```

### 3. Setup Database
```bash
cd loopback-backend
npm install
npm run db:migrate:crawler
```

### 4. Configure Environment
Create `.env` file in `loopback-backend/`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=website_crawler
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret
STORAGE_PATH=./storage
```

### 5. Start Backend
```bash
cd loopback-backend
npm run dev
```

### 6. Start Frontend
```bash
cd frontend
npm install
ng serve
```

### 7. Access Application
Open http://localhost:4200 in your browser

## 📖 Usage Guide

### Creating a Crawl Job

1. **Navigate to Crawler**: Go to `/crawler` in the application
2. **Enter URL**: Provide the website URL to crawl
3. **Configure Options**:
   - Set maximum depth (1-10 levels)
   - Set maximum pages (1-10,000 pages)
   - Choose content types to crawl
   - Add include/exclude patterns
   - Configure crawling behavior
4. **Start Crawling**: Click "Start Crawling" to begin

### Managing Content

1. **View Progress**: Monitor real-time crawling progress
2. **Browse Content**: View extracted content with search and filters
3. **Select Content**: Choose which pages to include in documents
4. **Preview Content**: See content previews before generation

### Generating Documents

1. **Choose Format**: Select PDF, DOCX, Markdown, HTML, or TXT
2. **Set Organization**: Single file, separate files, or grouped folders
3. **Configure Options**: Include images, table of contents, styling
4. **Generate**: Create and download your document

## 🔧 Configuration

### Crawl Options
```typescript
{
  maxDepth: 2,                    // Crawling depth (1-10)
  maxPages: 100,                  // Maximum pages (1-10,000)
  allowedContentTypes: ['text/html'], // Content types to crawl
  excludePatterns: [],            // Regex patterns to exclude
  includePatterns: [],            // Regex patterns to include
  followExternalLinks: true,      // Follow external links
  respectRobotsTxt: false,        // Respect robots.txt
  delayBetweenRequests: 1000      // Delay in milliseconds
}
```

### Document Options
```typescript
{
  format: 'pdf',                  // pdf, docx, markdown, html, txt
  organizationType: 'single_file', // single_file, separate_files, grouped_folders
  includeImages: false,           // Include images in document
  includeToc: true,               // Include table of contents
  destinationFolder: '',          // Custom destination folder
  customStyles: {},               // Custom styling options
  metadata: {}                    // Document metadata
}
```

## 🔒 Security Features

- **Authentication**: JWT-based user authentication
- **Authorization**: Role-based access control
- **Data Isolation**: Users can only access their own data
- **Input Validation**: Comprehensive input sanitization
- **Rate Limiting**: Configurable request rate limits
- **Secure Downloads**: Token-based file access
- **SQL Injection Protection**: Parameterized queries

## 📊 API Endpoints

### Crawler API
- `POST /crawler/jobs` - Create crawl job
- `GET /crawler/jobs` - List crawl jobs
- `GET /crawler/jobs/{id}` - Get crawl job details
- `POST /crawler/jobs/{id}/start` - Start crawling
- `POST /crawler/jobs/{id}/stop` - Stop crawling
- `GET /crawler/jobs/{id}/progress` - Get progress
- `GET /crawler/jobs/{id}/content` - Get crawled content

### Document Generator API
- `POST /document-generator/generate` - Generate document
- `GET /document-generator/documents` - List documents
- `GET /document-generator/documents/{id}/download` - Download document
- `POST /document-generator/content/select` - Update content selection

## 🧪 Testing

### Run Backend Tests
```bash
cd loopback-backend
npm test
```

### Run Frontend Tests
```bash
cd frontend
ng test
```

### Run E2E Tests
```bash
cd frontend
ng e2e
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Manual Deployment
1. Build frontend: `ng build --prod`
2. Build backend: `npm run build`
3. Setup database and run migrations
4. Configure environment variables
5. Start services with process manager (PM2)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [PROJECT_STATUS_AND_UPDATES_GUIDE.md](PROJECT_STATUS_AND_UPDATES_GUIDE.md)
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions for help and ideas

## 🎯 Roadmap

- [ ] Scheduled crawling with cron-like scheduling
- [ ] Content deduplication and fingerprinting
- [ ] Advanced content extraction rules
- [ ] Webhook notifications for crawl completion
- [ ] Bulk operations for multiple crawl jobs
- [ ] Content preview with syntax highlighting
- [ ] Export to cloud storage (AWS S3, Google Drive)
- [ ] API rate limiting per user
- [ ] Advanced analytics and reporting
