=== Page 442 ===

442In 2.0 you had to fetch incoming JSON payloads like this:
So now all instances of the above can be used normally:
CSRF middleware now lives in core and allows you to override some methods or interact
with the middleware with class attributes:
Replace your current CSRF Middleware with this new one:
If you made changes to the middleware to prevent middleware from being ran on every
request you can now set that as a class attribute:request.input('payload')['id']
request.input('id')
""" CSRF Middleware """
from masonite.middleware import CsrfMiddleware as Middleware
class CsrfMiddleware(Middleware):
    """ Verify CSRF Token Middleware """
    exempt = []
""" CSRF Middleware """
from masonite.middleware import CsrfMiddleware as Middleware
class CsrfMiddleware(Middleware):
    """ Verify CSRF Token Middleware """
    exempt = []
    every_request = FalseMoved CSRF Middleware into core6/12/25, 3:02 AM Masonite Documentation