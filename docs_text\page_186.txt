=== Page 187 ===

187If you are using the view() option in your mailable then you will need to set the
application on the mailable:
You can change the driver which sends the email by using the driver argument in the 
send() method:from app.mailables.Welcome import Welcome
class WelcomeController(Controller):
  
    def welcome(self):
        return Welcome()
from app.mailables.Welcome import Welcome
from wsgi import application
class WelcomeController(Controller):
  
    def welcome(self):
        return Welcome().set_application(application)
mail.send(Welcome().to('<EMAIL>'), driver="smtp")Changing Drivers6/12/25, 3:02 AM Masonite Documentation