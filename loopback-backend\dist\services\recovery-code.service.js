"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecoveryCodeService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const crypto = tslib_1.__importStar(require("crypto"));
const bcryptjs_1 = require("bcryptjs");
const repositories_1 = require("../repositories");
/**
 * Modular Recovery Code Service
 * Handles secure generation, storage, and validation of 2FA backup codes
 *
 * Features:
 * - Secure code generation with cryptographic randomness
 * - Hashed storage (never store plaintext codes)
 * - Single-use codes with automatic invalidation
 * - Uses individual fields instead of arrays for PostgreSQL compatibility
 */
let RecoveryCodeService = class RecoveryCodeService {
    constructor(userRepository) {
        this.userRepository = userRepository;
        this.SALT_ROUNDS = 12;
        this.CODE_LENGTH = 8;
        this.TOTAL_CODES = 3; // Reduced to 3 codes for simplicity
        this.MAX_ATTEMPTS_PER_HOUR = 5;
    }
    /**
     * Generate new recovery codes for a user
     * @param userId - User ID to generate codes for
     * @returns Array of plaintext codes (only time they're in plaintext)
     */
    async generateRecoveryCodes(userId) {
        try {
            console.log(`🔑 Generating ${this.TOTAL_CODES} recovery codes for user: ${userId}`);
            // Get current user
            const user = await this.userRepository.findById(userId);
            console.log(`🔍 User found: ${user.email}, current backupCodesRemaining: ${user.backupCodesRemaining}`);
            // Generate cryptographically secure codes
            const codes = [];
            const hashedCodes = [];
            for (let i = 0; i < this.TOTAL_CODES; i++) {
                // Generate secure random code
                const code = this.generateSecureCode();
                codes.push(code);
                // Hash the code for storage
                const hashedCode = await (0, bcryptjs_1.hash)(code, this.SALT_ROUNDS);
                hashedCodes.push(hashedCode);
            }
            console.log(`🔍 About to update user with ${hashedCodes.length} hashed codes`);
            console.log(`🔍 Setting backupCodesRemaining to: ${this.TOTAL_CODES}`);
            // Store hashed codes in individual fields (much simpler than arrays)
            await this.userRepository.updateById(userId, {
                backupCode1: hashedCodes[0],
                backupCode2: hashedCodes[1],
                backupCode3: hashedCodes[2],
                backupCodesRemaining: this.TOTAL_CODES,
                backupCodesGeneratedAt: new Date(),
                updatedAt: new Date(),
            });
            console.log(`✅ Successfully stored ${this.TOTAL_CODES} recovery codes`);
            // Verify the update worked
            const updatedUser = await this.userRepository.findById(userId);
            console.log(`🔍 Final verification - backupCodesRemaining: ${updatedUser.backupCodesRemaining}`);
            console.log(`🔍 Final verification - backupCode1 exists: ${!!updatedUser.backupCode1}`);
            console.log(`✅ Generated ${this.TOTAL_CODES} recovery codes for user: ${userId}`);
            return codes; // Return plaintext codes only once
        }
        catch (error) {
            console.error(`❌ Error generating recovery codes:`, error);
            throw new rest_1.HttpErrors.InternalServerError('Failed to generate recovery codes');
        }
    }
    /**
     * Validate and use a recovery code
     * @param userId - User ID
     * @param inputCode - Code provided by user
     * @returns True if code is valid and has been consumed
     */
    async validateAndConsumeRecoveryCode(userId, inputCode) {
        try {
            console.log(`🔍 Validating recovery code for user: ${userId}`);
            // Sanitize input
            const cleanCode = inputCode.trim().toUpperCase();
            // Check code length
            if (cleanCode.length !== this.CODE_LENGTH) {
                console.log(`❌ Invalid recovery code length: ${cleanCode.length}, expected: ${this.CODE_LENGTH}`);
                return false;
            }
            // Get user
            const user = await this.userRepository.findById(userId);
            if (!user) {
                console.log(`❌ User not found: ${userId}`);
                return false;
            }
            // Check if user has any recovery codes remaining
            if (!user.backupCodesRemaining || user.backupCodesRemaining <= 0) {
                console.log('❌ No recovery codes remaining');
                throw new rest_1.HttpErrors.BadRequest('No recovery codes remaining. Please contact support.');
            }
            // Check each available code
            const codeFields = ['backupCode1', 'backupCode2', 'backupCode3'];
            let matchedField = null;
            for (const field of codeFields) {
                const hashedCode = user[field];
                if (hashedCode) {
                    const isMatch = await (0, bcryptjs_1.compare)(cleanCode, hashedCode);
                    if (isMatch) {
                        matchedField = field;
                        break;
                    }
                }
            }
            if (!matchedField) {
                console.log('❌ Recovery code does not match any stored codes');
                return false;
            }
            // Remove the used code and update remaining count
            const updateData = {
                [matchedField]: undefined, // Clear the used code
                backupCodesRemaining: user.backupCodesRemaining - 1,
                updatedAt: new Date(),
            };
            await this.userRepository.updateById(userId, updateData);
            console.log(`✅ Recovery code validated and consumed for user: ${userId}`);
            console.log(`📊 Remaining codes: ${user.backupCodesRemaining - 1}`);
            return true;
        }
        catch (error) {
            console.error(`❌ Recovery code validation failed for user: ${userId}`, error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            return false;
        }
    }
    /**
     * Clear all recovery codes for a user
     */
    async clearRecoveryCodes(userId) {
        await this.userRepository.updateById(userId, {
            backupCode1: undefined,
            backupCode2: undefined,
            backupCode3: undefined,
            backupCodesRemaining: 0,
            backupCodesGeneratedAt: undefined,
            updatedAt: new Date(),
        });
    }
    /**
     * Check if all recovery codes have been used
     * @param userId - User ID to check
     * @returns true if all codes are used (no remaining codes)
     */
    async areAllCodesUsed(userId) {
        try {
            const user = await this.userRepository.findById(userId);
            // Check if all individual code fields are empty/undefined
            const hasCode1 = user.backupCode1 && user.backupCode1.trim() !== '';
            const hasCode2 = user.backupCode2 && user.backupCode2.trim() !== '';
            const hasCode3 = user.backupCode3 && user.backupCode3.trim() !== '';
            // Also check the counter field as backup
            const remainingCount = user.backupCodesRemaining || 0;
            const allCodesUsed = !hasCode1 && !hasCode2 && !hasCode3 && remainingCount === 0;
            console.log(`🔍 Recovery codes status for user ${userId}:`, {
                hasCode1, hasCode2, hasCode3, remainingCount, allCodesUsed
            });
            return allCodesUsed;
        }
        catch (error) {
            console.error(`❌ Failed to check recovery codes status for user: ${userId}`, error);
            return false;
        }
    }
    /**
     * Generate a cryptographically secure recovery code
     */
    generateSecureCode() {
        // Generate 4 bytes of random data and convert to hex (8 characters)
        const bytes = crypto.randomBytes(4);
        return bytes.toString('hex').toUpperCase();
    }
};
exports.RecoveryCodeService = RecoveryCodeService;
exports.RecoveryCodeService = RecoveryCodeService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository])
], RecoveryCodeService);
//# sourceMappingURL=recovery-code.service.js.map