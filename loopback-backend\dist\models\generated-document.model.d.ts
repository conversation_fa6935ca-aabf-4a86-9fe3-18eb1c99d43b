import { Entity } from '@loopback/repository';
import { CrawlJob } from './crawl-job.model';
import { User } from './user.model';
export declare class GeneratedDocument extends Entity {
    id: string;
    filename: string;
    format: string;
    status: string;
    organizationType: string;
    selectedContentIds: string[];
    generationOptions: object;
    filePath?: string;
    fileSize: number;
    downloadUrl?: string;
    destinationFolder?: string;
    metadata: object;
    errorMessage?: string;
    progressPercentage: number;
    totalPages: number;
    processedPages: number;
    generationTimeMs: number;
    expiresAt?: Date;
    isPublic: boolean;
    accessToken?: string;
    downloadCount: number;
    lastDownloadedAt?: Date;
    startedAt?: Date;
    completedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    crawlJobId: string;
    userId: string;
    constructor(data?: Partial<GeneratedDocument>);
}
export interface GeneratedDocumentRelations {
    crawlJob?: CrawlJob;
    user?: User;
}
export type GeneratedDocumentWithRelations = GeneratedDocument & GeneratedDocumentRelations;
