"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountDeletionPublicController = exports.AccountDeletionController = void 0;
const tslib_1 = require("tslib");
const authentication_1 = require("@loopback/authentication");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let AccountDeletionController = class AccountDeletionController {
    constructor(currentUserProfile, userRepository, deletionRepository, accountDeletionService) {
        this.currentUserProfile = currentUserProfile;
        this.userRepository = userRepository;
        this.deletionRepository = deletionRepository;
        this.accountDeletionService = accountDeletionService;
    }
    async requestAccountDeletion(preferences) {
        const userId = this.currentUserProfile[security_1.securityId];
        console.log('🗑️ Account deletion requested for user:', userId);
        console.log('🔧 Deletion preferences:', preferences);
        return this.accountDeletionService.requestAccountDeletion(userId, preferences);
    }
    async getDeletionStatus() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        const deletionRecord = await this.deletionRepository.findByEmail(user.email);
        return {
            hasPendingDeletion: !!deletionRecord && deletionRecord.deletionStatus === 'pending_confirmation',
            deletionRecord: deletionRecord || undefined,
        };
    }
    async cancelDeletion() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        const deletionRecord = await this.deletionRepository.findOne({
            where: {
                email: user.email,
                deletionStatus: 'pending_confirmation',
            },
        });
        if (!deletionRecord) {
            throw new rest_1.HttpErrors.NotFound('No pending deletion request found');
        }
        // Delete the pending deletion record
        await this.deletionRepository.deleteById(deletionRecord.id);
        return {
            message: 'Account deletion request has been cancelled successfully',
        };
    }
    async exportUserData() {
        const userId = this.currentUserProfile[security_1.securityId];
        console.log('📤 Exporting data for user:', userId);
        return this.accountDeletionService.exportUserData(userId);
    }
    async requestDataExport() {
        const userId = this.currentUserProfile[security_1.securityId];
        console.log('📧 Requesting data export via email for user:', userId);
        return this.accountDeletionService.requestDataExport(userId);
    }
    async cleanupExpiredRecords() {
        console.log('🧹 Admin cleanup request received');
        return this.accountDeletionService.cleanupExpiredDeletionRecords();
    }
};
exports.AccountDeletionController = AccountDeletionController;
tslib_1.__decorate([
    (0, rest_1.post)('/account/request-deletion'),
    (0, rest_1.response)(200, {
        description: 'Request account deletion with preferences',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        deletionId: { type: 'string' },
                        confirmationRequired: { type: 'boolean' },
                        confirmationToken: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        preservePaymentData: { type: 'boolean', default: false },
                        preserveTransactionHistory: { type: 'boolean', default: false },
                        preserveProfileData: { type: 'boolean', default: false },
                        preserveSecurityLogs: { type: 'boolean', default: false },
                        customRetentionPeriod: { type: 'number', default: 30 },
                        reason: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionController.prototype, "requestAccountDeletion", null);
tslib_1.__decorate([
    (0, rest_1.get)('/account/deletion-status'),
    (0, rest_1.response)(200, {
        description: 'Get account deletion status',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        hasPendingDeletion: { type: 'boolean' },
                        deletionRecord: { type: 'object' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionController.prototype, "getDeletionStatus", null);
tslib_1.__decorate([
    (0, rest_1.post)('/account/cancel-deletion'),
    (0, rest_1.response)(200, {
        description: 'Cancel pending account deletion',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionController.prototype, "cancelDeletion", null);
tslib_1.__decorate([
    (0, rest_1.get)('/account/export-data'),
    (0, rest_1.response)(200, {
        description: 'Export user data as downloadable file',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                },
            },
            'application/octet-stream': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionController.prototype, "exportUserData", null);
tslib_1.__decorate([
    (0, rest_1.post)('/account/request-export'),
    (0, rest_1.response)(200, {
        description: 'Request data export via email',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionController.prototype, "requestDataExport", null);
tslib_1.__decorate([
    (0, rest_1.post)('/account/cleanup-expired'),
    (0, rest_1.response)(200, {
        description: 'Clean up expired deletion records',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        cleanedCount: { type: 'number' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionController.prototype, "cleanupExpiredRecords", null);
exports.AccountDeletionController = AccountDeletionController = tslib_1.__decorate([
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(2, (0, repository_1.repository)(repositories_1.AccountDeletionRecordRepository)),
    tslib_1.__param(3, (0, core_1.inject)('services.AccountDeletionService')),
    tslib_1.__metadata("design:paramtypes", [Object, repositories_1.UserRepository,
        repositories_1.AccountDeletionRecordRepository,
        services_1.AccountDeletionService])
], AccountDeletionController);
// Public controller for confirmation (no authentication required)
let AccountDeletionPublicController = class AccountDeletionPublicController {
    constructor(deletionRepository, accountDeletionService) {
        this.deletionRepository = deletionRepository;
        this.accountDeletionService = accountDeletionService;
    }
    async confirmDeletion(request) {
        console.log('🔍 Confirming account deletion with token:', request.token.substring(0, 10) + '...');
        return this.accountDeletionService.confirmAccountDeletion(request.token);
    }
    async checkPreservedData(email) {
        console.log('🔍 Checking preserved data for email:', email);
        return this.accountDeletionService.checkPreservedData(email);
    }
    async restoreData(request) {
        console.log('🔄 Restoring data for user:', request.userId);
        console.log('📧 Email:', request.email);
        console.log('⚙️ Restore options:', {
            restorePaymentData: request.restorePaymentData,
            restoreTransactionHistory: request.restoreTransactionHistory,
            restoreProfileData: request.restoreProfileData,
            restoreSecurityLogs: request.restoreSecurityLogs,
        });
        return this.accountDeletionService.restoreUserData(request.userId, request.email, {
            restorePaymentData: request.restorePaymentData,
            restoreTransactionHistory: request.restoreTransactionHistory,
            restoreProfileData: request.restoreProfileData,
            restoreSecurityLogs: request.restoreSecurityLogs,
        });
    }
    async deletePreservedData(request) {
        console.log('🗑️ Permanently deleting preserved data for email:', request.email);
        return this.accountDeletionService.deletePreservedData(request.email);
    }
};
exports.AccountDeletionPublicController = AccountDeletionPublicController;
tslib_1.__decorate([
    (0, rest_1.post)('/account/confirm-deletion'),
    (0, rest_1.response)(200, {
        description: 'Confirm account deletion with token',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        deletionId: { type: 'string' },
                        preservedDataSummary: { type: 'object' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionPublicController.prototype, "confirmDeletion", null);
tslib_1.__decorate([
    (0, rest_1.get)('/account/check-preserved-data/{email}'),
    authentication_1.authenticate.skip(),
    (0, rest_1.response)(200, {
        description: 'Check if user has preserved data for restoration',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        hasPreservedData: { type: 'boolean' },
                        deletionRecord: { type: 'object' },
                        preservedDataSummary: { type: 'object' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('email')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionPublicController.prototype, "checkPreservedData", null);
tslib_1.__decorate([
    (0, rest_1.post)('/account/restore-data'),
    authentication_1.authenticate.skip(),
    (0, rest_1.response)(200, {
        description: 'Restore preserved data during signup',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        restoredData: { type: 'object' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['userId', 'email'],
                    properties: {
                        userId: { type: 'string' },
                        email: { type: 'string' },
                        restorePaymentData: { type: 'boolean', default: false },
                        restoreTransactionHistory: { type: 'boolean', default: false },
                        restoreProfileData: { type: 'boolean', default: false },
                        restoreSecurityLogs: { type: 'boolean', default: false },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionPublicController.prototype, "restoreData", null);
tslib_1.__decorate([
    (0, rest_1.del)('/account/delete-preserved-data'),
    (0, rest_1.response)(200, {
        description: 'Permanently delete all preserved data',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email'],
                    properties: {
                        email: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AccountDeletionPublicController.prototype, "deletePreservedData", null);
exports.AccountDeletionPublicController = AccountDeletionPublicController = tslib_1.__decorate([
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.AccountDeletionRecordRepository)),
    tslib_1.__param(1, (0, core_1.inject)('services.AccountDeletionService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.AccountDeletionRecordRepository,
        services_1.AccountDeletionService])
], AccountDeletionPublicController);
//# sourceMappingURL=account-deletion.controller.js.map