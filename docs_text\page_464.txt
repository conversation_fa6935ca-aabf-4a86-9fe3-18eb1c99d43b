=== Page 465 ===

465Masonite 2.3 to 3.0
This guide is designed to give you as much information as possible to upgrade your
application from Masonite 2.3 to Masonite 3.0.
We will go through each new breaking change in Masonite and code examples on how to
upgrade the code to use Masonite 3. If there is any code breaking during the upgrade,
please go to our Slack channel and let us know so we can add more information to this
documentation.
This document will be broken down into 2 parts, upgrading from Orator to Masonite and
upgrading from Masonite 2.3 to 3.0.
Before you go through this document it is highly recommended that you read Whats New
in Masonite 3.
Note that there are some large changes from 2.3 to 3.0. Depending on the size of your
project it might make more sense to rebuild your project and port your code to a new
Masonite 3 app. This guide is for changing your existing projects.
We need to uninstall some packages and install some others
First uninstall masonite and orator and install masonite 3:
$ pip uninstall masonite
$ pip uninstall orator
$ pip install masonite==3.0Masonite 2.3 to 3.0
Upgrading Masonite
Requirement Changes6/12/25, 3:02 AM Masonite Documentation