=== Page 203 ===

203Masonite ships with a handful of notification channels, but you may want to write your
own drivers to deliver notifications via other channels. Masonite makes this process
simple.
Two methods need to be implemented in order to create a notification driver: send and 
queue.
get_data() method will be available and will return the data defined in the 
to_voice() method of the notification.
As any drivers it should be registered, through a custom provider for example:from masonite.notification.drivers import BaseDriver
class VoiceDriver(BaseDriver):
    def send(self, notifiable, notification):
        """Specify here how to send the notification with this 
driver."""
        data = self.get_data("voice", notifiable, notification)
        # do something
from masonite.providers import Provider
class VoiceNotificationProvider(Provider):
    def register(self):
        self.application.make("notification").add_driver("voice", 
VoiceDriver(self.application))Adding a new driver
Creating the driver
Registering the driver6/12/25, 3:02 AM Masonite Documentation