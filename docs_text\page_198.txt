=== Page 199 ===

199The global sms_from number can be overriden inside the notification class:
You should define the related route_notification_for_vonage method on your
notifiable to return a phone number or a list of phone numbers to send the notification to.
To send a SMS notification without having a notifiable entity you must use the route
method
Notifications can be stored in your application database when sent to Notifiable
entities. The notification is stored in a notifications table. This table will contain
information such as the notification type as well as a JSON data structure that describes
the notification.
To store a notification in the database you should define a to_database method on the
notification class to specify how to build the notification content that will be persisted.Sms().text("Welcome unicode message!").set_unicode()
Sms().text("Welcome!").sms_from("+123 456 789")
class User(Model, Notifiable):
    def route_notification_for_vonage(self):
        return self.phone
        # or return [self.mobile_phone, self.land_phone]
notification.route("vonage", "+***********").notify(Welcome())Routing to notifiable
Routing to anonymous
Saving notifications6/12/25, 3:02 AM Masonite Documentation