import { DefaultCrudRepository } from '@loopback/repository';
import { DbDataSource } from '../datasources';
import { PreservedUserData, PreservedUserDataRelations } from '../models';
export declare class PreservedUserDataRepository extends DefaultCrudRepository<PreservedUserData, typeof PreservedUserData.prototype.id, PreservedUserDataRelations> {
    constructor(dataSource: DbDataSource);
    /**
     * Find preserved data by deletion record ID
     */
    findByDeletionRecordId(deletionRecordId: string): Promise<PreservedUserData[]>;
    /**
     * Find preserved data by type for a deletion record
     */
    findByDeletionRecordAndType(deletionRecordId: string, dataType: string): Promise<PreservedUserData[]>;
    /**
     * Find expired preserved data for cleanup
     */
    findExpiredData(): Promise<PreservedUserData[]>;
    /**
     * Get data size summary for a deletion record
     */
    getDataSizeSummary(deletionRecordId: string): Promise<{
        totalSize: number;
        dataTypes: Record<string, number>;
    }>;
}
