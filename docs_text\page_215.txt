=== Page 216 ===

216The package publish command will publish the assets into the defined project resources
folder. With the default project settings it would be in 
resources/vendor/super_awesome/.
If your project contains commands you will want to register it when your package is
installed in a project so that we can run python craft my_package_command. For this
you will need to call commands(*command_class) inside configure() method.
Now when you run python craft you should see the two registered commands.
When using PackageProvider class to create your package service provider, you will be
able to publish all package resources defined below in a project. You just need to run the
command package:publish with the name of the package (declared inside 
configure() method). With our example it would be:def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .assets("assets")
    )
from ..commands import MyPackageCommand, AnO<PERSON>Command
def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .commands(MyPackageCommand, AnOtherCommand)
    )
$ python craft package:publish super_awesomeCommands
Publishing Resources6/12/25, 3:02 AM Masonite Documentation