import {Entity, model, property, belongsTo} from '@loopback/repository';
import {CrawlJob} from './crawl-job.model';
import {User} from './user.model';

@model({
  settings: {
    strict: true,
    indexes: {
      crawlJobIdIndex: {
        keys: {
          crawlJobId: 1,
        },
      },
      userIdIndex: {
        keys: {
          userId: 1,
        },
      },
      formatIndex: {
        keys: {
          format: 1,
        },
      },
      statusIndex: {
        keys: {
          status: 1,
        },
      },
      createdAtIndex: {
        keys: {
          createdAt: -1,
        },
      },
    },
    postgresql: {
      schema: 'public',
      table: 'generated_document'
    }
  },
})
export class GeneratedDocument extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 1,
      maxLength: 255,
    },
  })
  filename: string;

  @property({
    type: 'string',
    required: true,
    default: 'pdf',
    jsonSchema: {
      enum: ['pdf', 'docx', 'markdown', 'html', 'txt'],
    },
  })
  format: string;

  @property({
    type: 'string',
    required: true,
    default: 'pending',
    jsonSchema: {
      enum: ['pending', 'generating', 'completed', 'failed'],
    },
  })
  status: string;

  @property({
    type: 'string',
    required: true,
    default: 'single_file',
    jsonSchema: {
      enum: ['single_file', 'separate_files', 'grouped_folders'],
    },
  })
  organizationType: string;

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  selectedContentIds: string[];

  @property({
    type: 'object',
    default: {},
  })
  generationOptions: object;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'file_path'
    }
  })
  filePath?: string;

  @property({
    type: 'number',
    default: 0,
    postgresql: {
      columnName: 'file_size'
    }
  })
  fileSize: number;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'download_url'
    }
  })
  downloadUrl?: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'destination_folder'
    }
  })
  destinationFolder?: string;

  @property({
    type: 'object',
    default: {},
  })
  metadata: object;

  @property({
    type: 'string',
  })
  errorMessage?: string;

  @property({
    type: 'number',
    default: 0,
    jsonSchema: {
      minimum: 0,
      maximum: 100,
    },
  })
  progressPercentage: number;

  @property({
    type: 'number',
    default: 0,
  })
  totalPages: number;

  @property({
    type: 'number',
    default: 0,
  })
  processedPages: number;

  @property({
    type: 'number',
    default: 0,
  })
  generationTimeMs: number;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'expires_at'
    }
  })
  expiresAt?: Date;

  @property({
    type: 'boolean',
    default: false,
    postgresql: {
      columnName: 'is_public'
    }
  })
  isPublic: boolean;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'access_token'
    }
  })
  accessToken?: string;

  @property({
    type: 'number',
    default: 0,
    postgresql: {
      columnName: 'download_count'
    }
  })
  downloadCount: number;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'last_downloaded_at'
    }
  })
  lastDownloadedAt?: Date;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'started_at'
    }
  })
  startedAt?: Date;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'completed_at'
    }
  })
  completedAt?: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'created_at'
    }
  })
  createdAt: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'updated_at'
    }
  })
  updatedAt: Date;

  @belongsTo(() => CrawlJob, {}, {
    postgresql: {
      columnName: 'crawl_job_id'
    }
  })
  crawlJobId: string;

  @belongsTo(() => User, {}, {
    postgresql: {
      columnName: 'user_id'
    }
  })
  userId: string;

  constructor(data?: Partial<GeneratedDocument>) {
    super(data);
  }
}

export interface GeneratedDocumentRelations {
  crawlJob?: CrawlJob;
  user?: User;
}

export type GeneratedDocumentWithRelations = GeneratedDocument & GeneratedDocumentRelations;
