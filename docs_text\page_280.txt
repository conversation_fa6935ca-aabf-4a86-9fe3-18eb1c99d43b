=== Page 281 ===

281Used to make sure a value is a truthy value. This is anything that would pass in a simple if
statement.
Used to check that a value is a valid UUID. The UUID version (according to RFC 4122)
standard can optionally be verified (1,3,4 or 5). The default version 4."""
{
  'timezone': 'America/New_York'
}
"""
validate.timezone('timezone')
"""
{
  'active': 1,
  'email': '<EMAIL>'
}
"""
validate.truthy('active')
"""
{
  'doc_id': 'c1d38bb1-139e-4099-8a20-61a2a0c9b996'
}
"""
# check value is a valid UUID4
validate.uuid('doc_id')
# check value is a valid UUID3
validate.uuid('doc_id', 3)  # or '3'Truthy
Uuid
Video6/12/25, 3:02 AM Masonite Documentation