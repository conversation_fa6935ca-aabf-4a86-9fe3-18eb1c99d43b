=== Page 351 ===

351The next part you'll need is a way to add data to your collector. This can be done in any
method you want your developers to use. These are your external API's and how you want
developers interacting with your collector. You can name these methods whatever you
want and can be as complex as you need them to be.
This method could be as simple or as complex as you need. Some of Masonite
Debugbar's collectors use special classes to keep all the information.
Next you need a collect and an html method to finalize the collector. First the collect
method should just return a dictionary like this:class YourCollector:
    def __init__(self, name="Your Collector", 
description="Description"):
        self.messages = []
        self.name = name
        self.description = description
    def restart(self):
        self.messages = []
        return self
    def add(self, key, value):
        self.messages.append({key: value})Collector Methods
Collector Rendering6/12/25, 3:02 AM Masonite Documentation