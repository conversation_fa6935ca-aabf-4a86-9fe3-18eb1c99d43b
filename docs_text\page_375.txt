=== Page 376 ===

376Masonite 2.0
Masonite 2 brings an incredible new release to the Masonite family. This release brings a
lot of new features to Masonite to include new status codes, database seeding, built in
cron scheduling, controller constructor resolving, auto-reloading server, a few new
internal ways that Masonite handles things, speed improvements to some code elements
and so much more. We think developers will be extremely happy with this release.
Upgrading from Masonite 1.6 to Masonite 2.0 shouldn't take very long. On an average
sized project, this upgrade should take around 30 minutes. We'll walk you through the
changes you have to make to your current project and explain the reasoning behind it.
Checkout the Upgrade Guide for Masonite 1.6 to 2.0
Controller constructors are now resolved by the container so this removed some
redundancy within your code and any duplicated auto resolving can now be directly in
your constructor:
Read more in the Controllers documentation.
from masonite.request import Request
class YourController:
    def __init__(self, request: Request):
        self.request = Request
    def show(self):
        print(self.request) # <class masonite.request.Request>
Controller Constructors
Tinker Command6/12/25, 3:02 AM Masonite Documentation