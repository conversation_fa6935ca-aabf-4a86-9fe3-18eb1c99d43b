{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.tokenKey = environment.security.jwtTokenKey;\n      this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n      this.currentUser = this.currentUserSubject.asObservable();\n    }\n    get currentUserValue() {\n      return this.currentUserSubject.value;\n    }\n    get isAuthenticated() {\n      return !!this.getToken() && !!this.currentUserValue;\n    }\n    get isEmailVerified() {\n      return this.currentUserValue?.emailVerified || false;\n    }\n    get isTwoFactorEnabled() {\n      return this.currentUserValue?.twoFactorEnabled || false;\n    }\n    register(userData) {\n      return this.http.post(`${environment.apiUrl}/auth/signup`, userData).pipe(catchError(this.handleError));\n    }\n    login(credentials) {\n      return this.http.post(`${environment.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n        if (response.token && !response.requiresTwoFactor) {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n        }\n      }), catchError(this.handleError));\n    }\n    logout() {\n      this.removeToken();\n      this.currentUserSubject.next(null);\n      this.router.navigate(['/auth/login']);\n    }\n    verifyEmail(token) {\n      return this.http.post(`${environment.apiUrl}/auth/verify-email`, {\n        token\n      }).pipe(catchError(this.handleError));\n    }\n    forgotPassword(email) {\n      return this.http.post(`${environment.apiUrl}/auth/forgot-password`, {\n        email\n      }).pipe(catchError(this.handleError));\n    }\n    resetPassword(resetData) {\n      return this.http.post(`${environment.apiUrl}/auth/reset-password`, resetData).pipe(catchError(this.handleError));\n    }\n    sendOTP(request) {\n      const endpoint = request.identifier.includes('@') ? 'send-email' : 'send-sms';\n      return this.http.post(`${environment.apiUrl}/otp/${endpoint}`, request).pipe(catchError(this.handleError));\n    }\n    verifyOTP(verification) {\n      return this.http.post(`${environment.apiUrl}/otp/verify`, verification).pipe(catchError(this.handleError));\n    }\n    loginWithOTP(identifier, code) {\n      return this.http.post(`${environment.apiUrl}/otp/login`, {\n        identifier,\n        code\n      }).pipe(tap(response => {\n        if (response.token) {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n        }\n      }), catchError(this.handleError));\n    }\n    refreshUserData() {\n      return this.http.get(`${environment.apiUrl}/auth/me`).pipe(tap(user => {\n        this.currentUserSubject.next(user);\n      }), catchError(this.handleError));\n    }\n    getToken() {\n      if (typeof window !== 'undefined') {\n        return localStorage.getItem(this.tokenKey);\n      }\n      return null;\n    }\n    setToken(token) {\n      if (typeof window !== 'undefined') {\n        localStorage.setItem(this.tokenKey, token);\n      }\n    }\n    removeToken() {\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem(this.tokenKey);\n      }\n    }\n    getAuthHeaders() {\n      const token = this.getToken();\n      return new HttpHeaders({\n        'Content-Type': 'application/json',\n        'Authorization': token ? `Bearer ${token}` : ''\n      });\n    }\n    getUserFromStorage() {\n      if (typeof window !== 'undefined') {\n        const token = this.getToken();\n        if (token) {\n          try {\n            // Decode JWT token to get user info (basic implementation)\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.user || null;\n          } catch (error) {\n            console.error('Error parsing token:', error);\n            this.removeToken();\n          }\n        }\n      }\n      return null;\n    }\n    handleError(error) {\n      let errorMessage = 'An error occurred';\n      if (error.error instanceof ErrorEvent) {\n        // Client-side error\n        errorMessage = error.error.message;\n      } else {\n        // Server-side error\n        errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n      }\n      console.error('Auth Service Error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    // Security utilities\n    isTokenExpired() {\n      const token = this.getToken();\n      if (!token) return true;\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const expiry = payload.exp * 1000; // Convert to milliseconds\n        return Date.now() > expiry;\n      } catch (error) {\n        return true;\n      }\n    }\n    autoLogout() {\n      const token = this.getToken();\n      if (token && this.isTokenExpired()) {\n        this.logout();\n      }\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}