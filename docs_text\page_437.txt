=== Page 438 ===

438This is likely the biggest change in 2.0. Before 2.1 you were able to fetch by key when
resolving by doing something like:
We have removed this by default and now you much explicitly import your classes in
order to interact with the container resolving:
If you truly do not like this change you can modify your container on a per project basis by
adding this to your container constructor in wsgi.py:
Just know this is not recommended and Masonite may or may not remove this feature
entirely at some point in the future.
Previously we were able to do something like this:import app.http.middleware.DashboardMiddleware import 
DashboardMiddleware
HTTP_MIDDLEWARE = [
    DashboardMiddleware,
]
def show(self, Request):
    Request.input(..)
from masonite.request import Request
def show(self, request: Request):
    request.input(..)
container = App(resolve_parameters=True)Auto resolving parameters has been removed
Resolving Mail, Queues and Broadcasts6/12/25, 3:02 AM Masonite Documentation