=== Page 146 ===

146On the frontend we need to listen to private-admins channel and subscribe to 
AdminUserAlert events to display an alert box with the message.
You're ready to start broadcasting events in your app !from masonite.facades import Broadcast
from app.broadcasts import AdminUserAlert
Broadcast.channel(AdminUserAlert("Some dependencies are outdated !", 
level="warning"))
<html lang="en">
<head>
  <title>Document</title>
  <script src="https://js.pusher.com/7.0/pusher.min.js"></script>
</head>
<body>
  <script>
    const pusher = new Pusher("478b45309560f3456211", {
      cluster: "eu"
    });
    const channel = pusher.subscribe('private-admins');
    channel.bind('AdminUserAlert', (data) => {
      alert(`[${data.level.toUpperCase()}] ${data.message}`)
    })
  </script>
</body>
</html>6/12/25, 3:02 AM Masonite Documentation