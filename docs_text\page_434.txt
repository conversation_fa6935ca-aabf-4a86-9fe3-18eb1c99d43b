=== Page 435 ===

435There is a new status code provider which adds support for adding custom status codes
and rendering better default status code pages such as 400 and 500 error pages. This
should be added right above the StartResponseProvider:
The .env got a small upgrade and in order to make the APP_DEBUG variable consistent, it
should be set to either True or False. Previously this was set to something like true
or false.
Masonite 2 also removed the APP_LOG_LEVEL environment variable completely.PROVIDERS = [
    # Framework Providers
    ...
    'masonite.providers.RouteProvider',
    'masonite.providers.RedirectionProvider',
    # New provider here above StartResponseProvider
    'masonite.providers.StatusCodeProvider',
    'masonite.providers.StartResponseProvider',
    'masonite.providers.WhitenoiseProvider',
    ...
]
APP_DEBUG=True
# or
APP_DEBUG=FalseStatusCodeProvider
.env Fileconfig/application.py
.env6/12/25, 3:02 AM Masonite Documentation