=== Page 292 ===

292Another powerful feature of the container is it can actually return instances of classes
you annotate. For example, all Upload drivers inherit from the UploadContract which
simply acts as an interface for all Upload drivers. Many programming paradigms say
that developers should code to an interface instead of an implementation so Masonite
allows instances of classes to be returned for this specific use case.
Take this example:
Notice that we passed in a contract instead of the upload class. Masonite went into the
container and fetched a class with the instance of the contract.
The service container can also be used outside of the flow of Masonite. Masonite takes
in a function or class method, and resolves it's dependencies by finding them in the
service container and injecting them for you.
Because of this, you can resolve any of your own classes or functions.# elsewhere...
class MyService:
    def __init__(self, some_other_dependency: SomeOtherClass):
        pass
    
    def do_something(self):
        pass
# in a controller...
def show(self, request: Request, service: MyService):
    request.user()
    service.do_something()
from masonite.contracts import UploadContract
def show(self, upload: UploadContract)
    upload # <class masonite.drivers.UploadDiskDriver>Resolving Instances
Resolving your own code6/12/25, 3:02 AM Masonite Documentation