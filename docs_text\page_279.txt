=== Page 280 ===

280Used to make sure the value is a string
The strong rule is used to make sure a string has a certain amount of characters required
to be considered a "strong" string.
This is really useful for passwords when you want to make sure a password has at least 8
characters, have at least 2 uppercase letters and at least 2 special characters.
You can also validate that a value passed in a valid timezone"""
{
  'age': 25,
  'email': '<EMAIL>'
}
"""
validate.string('email')
"""
{
  'email': '<EMAIL>'
  'password': 'SeCreT!!'
}
"""
validate.strong('password', length=8, special=2, uppercase=3)String
Strong
Timezone6/12/25, 3:02 AM Masonite Documentation