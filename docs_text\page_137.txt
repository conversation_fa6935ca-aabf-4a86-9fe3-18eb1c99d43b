=== Page 138 ===

138In this section we will use the client pusher instance configured earlier.
To listen for events on client-side you must first subscribe to the channel the events are
emitted on
Then you can listen for events
Different channel types are included in Masonite.
Inside the event class you can specify a Public channel. These channels allow anyone
with a connection to listen to events on this channel:const channel = pusher.subscribe("my-channel");
channel.bind("my-event", (data) => {
  // Method to be dispatched when event is received
});
from masonite.broadcasting import CanBroadcast
from masonite.broadcasting import Channel
class UserAdded(CanBroadcast):
    def broadcast_on(self):
        return Channel("channel_name")Listening For Events
Channel Types
Public Channels
Private Channels6/12/25, 3:02 AM Masonite Documentation