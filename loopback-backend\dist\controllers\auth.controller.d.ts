import { TokenService } from '@loopback/authentication';
import { UserProfile } from '@loopback/security';
import { User as UserModel } from '../models';
import { UserRepository as UserRepo } from '../repositories';
import { SecurityService, EmailService, SmsService, RecoveryCodeService, TwoFactorDisableService, AccountDeletionService } from '../services';
export declare class AuthController {
    jwtService: TokenService;
    user: UserProfile;
    protected userRepository: UserRepo;
    securityService?: SecurityService | undefined;
    emailService?: EmailService | undefined;
    smsService?: SmsService | undefined;
    recoveryCodeService?: RecoveryCodeService | undefined;
    twofaDisableService?: TwoFactorDisableService | undefined;
    accountDeletionService?: AccountDeletionService | undefined;
    constructor(jwtService: TokenService, user: UserProfile, userRepository: UserRepo, securityService?: SecurityService | undefined, emailService?: EmailService | undefined, smsService?: SmsService | undefined, recoveryCodeService?: RecoveryCodeService | undefined, twofaDisableService?: TwoFactorDisableService | undefined, accountDeletionService?: AccountDeletionService | undefined);
    signUp(newUserRequest: Omit<UserModel, 'id'> & {
        password: string;
    }): Promise<{
        message: string;
        userId: string;
        hasPreservedData?: boolean;
        preservedDataSummary?: object;
    }>;
    login(credentials: {
        email: string;
        password: string;
        twoFactorToken?: string;
        recoveryCode?: string;
    }): Promise<{
        token: string;
        user: UserModel;
        requiresTwoFactor?: boolean;
    }>;
    verifyEmail(request: {
        token: string;
    }): Promise<{
        message: string;
        user: {
            email: string;
            firstName: string;
            id: string;
        };
    }>;
    resendVerification(request: {
        email: string;
    }): Promise<{
        message: string;
    }>;
    forgotPassword(request: {
        email?: string;
        phone?: string;
    }): Promise<{
        message: string;
    }>;
    resetPassword(request: {
        token: string;
        password: string;
    }): Promise<{
        message: string;
    }>;
    getProfile(): Promise<Partial<UserModel>>;
    getCurrentUser(): Promise<Partial<UserModel>>;
    updateProfile(profileData: {
        firstName?: string;
        lastName?: string;
        email?: string;
        phone?: string;
    }): Promise<{
        message: string;
        user: Partial<UserModel>;
    }>;
    changePassword(request: {
        currentPassword?: string;
        newPassword: string;
        twoFactorToken?: string;
    }): Promise<{
        message: string;
    }>;
    private verifyCredentials;
    private convertToUserProfile;
    private handleFailedLogin;
    private handleSuccessfulLogin;
    getResetTokenForTesting(email: string): Promise<{
        token: string | null;
        email: string;
        expires: string | null;
    }>;
    getUserStatusForTesting(email: string): Promise<{
        email: string;
        loginAttempts: number;
        lockUntil: string | null;
        isLocked: boolean;
    }>;
    requestDisable2FA(request: {
        email: string;
        reason?: string;
    }): Promise<{
        message: string;
        allCodesUsed?: boolean;
    }>;
    confirmDisable2FA(request: {
        token: string;
    }): Promise<{
        success: boolean;
        message: string;
        user?: any;
    }>;
    getDisable2FAStatus(email: string): Promise<{
        hasPendingRequest: boolean;
        email: string;
    }>;
}
