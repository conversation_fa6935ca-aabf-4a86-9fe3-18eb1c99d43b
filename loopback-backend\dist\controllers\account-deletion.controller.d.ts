import { UserProfile } from '@loopback/security';
import { UserRepository, AccountDeletionRecordRepository } from '../repositories';
import { AccountDeletionService } from '../services';
export declare class AccountDeletionController {
    currentUserProfile: UserProfile;
    userRepository: UserRepository;
    deletionRepository: AccountDeletionRecordRepository;
    accountDeletionService: AccountDeletionService;
    constructor(currentUserProfile: UserProfile, userRepository: UserRepository, deletionRepository: AccountDeletionRecordRepository, accountDeletionService: AccountDeletionService);
    requestAccountDeletion(preferences: {
        preservePaymentData: boolean;
        preserveTransactionHistory: boolean;
        preserveProfileData: boolean;
        preserveSecurityLogs: boolean;
        customRetentionPeriod?: number;
        reason?: string;
    }): Promise<{
        message: string;
        deletionId: string;
        confirmationRequired: boolean;
        confirmationToken?: string;
    }>;
    getDeletionStatus(): Promise<{
        hasPendingDeletion: boolean;
        deletionRecord?: object;
    }>;
    cancelDeletion(): Promise<{
        message: string;
    }>;
    exportUserData(): Promise<object>;
    requestDataExport(): Promise<{
        message: string;
    }>;
    cleanupExpiredRecords(): Promise<{
        message: string;
        cleanedCount: number;
    }>;
}
export declare class AccountDeletionPublicController {
    deletionRepository: AccountDeletionRecordRepository;
    accountDeletionService: AccountDeletionService;
    constructor(deletionRepository: AccountDeletionRecordRepository, accountDeletionService: AccountDeletionService);
    confirmDeletion(request: {
        token: string;
    }): Promise<{
        message: string;
        deletionId: string;
        preservedDataSummary?: object;
    }>;
    checkPreservedData(email: string): Promise<{
        hasPreservedData: boolean;
        deletionRecord?: object;
        preservedDataSummary?: object;
    }>;
    restoreData(request: {
        userId: string;
        email: string;
        restorePaymentData: boolean;
        restoreTransactionHistory: boolean;
        restoreProfileData: boolean;
        restoreSecurityLogs: boolean;
    }): Promise<{
        message: string;
        restoredData: object;
    }>;
    deletePreservedData(request: {
        email: string;
    }): Promise<{
        message: string;
    }>;
}
