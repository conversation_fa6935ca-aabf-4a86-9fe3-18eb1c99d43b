=== Page 215 ===

215If your package contains views you can register them by providing folders containing your
views. For this you will need to call views(*folders, publish=False) inside 
configure() method. The views will be namespaced after your package name:
For example if your package contains an admin folder located at 
super_awesome_package/admin/ containing a index.html view you can do:
Views will be available in controllers under a namespace. Use 
your_package_name:template to reference your view:
If you want to allow users to publish the view file into their own project so they can tweak
them you should add publish=True argument. The package publish command will
publish the views files into the defined project views folder. With the default project
settings it would be in templates/vendor/super_awesome/admin/index.html.
If your project contains assets (such as JS, CSS or images files) you can register them to
be published in the project by calling assets(*paths) inside configure() method.
For example if your package contains an assets folder located at 
super_awesome_package/assets/ containing some asset files and folders you can do:def configure(self):
    (
        self.root("super_awesome_package")
        .name("super_awesome")
        .views("admin")
    )
class ProjectController(Controller):
    def index(self, view: View):
        return view.render("super_awesome:admin.index")
Assets6/12/25, 3:02 AM Masonite Documentation