{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction ResetPasswordComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.passwordStrength, \"%\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.passwordStrengthText);\n  }\n}\nfunction ResetPasswordComponent_mat_spinner_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction ResetPasswordComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Reset Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ResetPasswordComponent {\n  constructor(fb, authService, router, route, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.token = null;\n    this.isTokenValid = true;\n  }\n  ngOnInit() {\n    // Get token from URL query parameters\n    this.token = this.route.snapshot.queryParamMap.get('token');\n    if (!this.token) {\n      this.isTokenValid = false;\n      this.snackBar.open('Invalid reset link. Please request a new password reset.', 'Close', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(group) {\n    const password = group.get('password');\n    const confirmPassword = group.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        mismatch: true\n      });\n      return {\n        mismatch: true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.resetPasswordForm.valid && !this.isLoading && this.token) {\n      this.isLoading = true;\n      const password = this.resetPasswordForm.get('password')?.value;\n      this.authService.resetPassword({\n        token: this.token,\n        password: password,\n        confirmPassword: password\n      }).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Password reset successfully! You can now login with your new password.', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Redirect to login page after successful reset\n          setTimeout(() => {\n            this.router.navigate(['/auth/login']);\n          }, 2000);\n        },\n        error: error => {\n          this.isLoading = false;\n          const errorMessage = error?.error?.message || 'Failed to reset password. Please try again.';\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          // If token is invalid, redirect to forgot password\n          if (errorMessage.includes('Invalid or expired')) {\n            setTimeout(() => {\n              this.router.navigate(['/auth/forgot-password']);\n            }, 3000);\n          }\n        }\n      });\n    }\n  }\n  goBackToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  requestNewReset() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n  static #_ = this.ɵfac = function ResetPasswordComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResetPasswordComponent,\n    selectors: [[\"app-reset-password\"]],\n    decls: 38,\n    vars: 11,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"password-strength\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"back-to-login\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"password-strength\"], [1, \"strength-bar\"], [1, \"strength-fill\", 3, \"ngClass\"], [1, \"strength-text\", 3, \"ngClass\"], [\"diameter\", \"20\"]],\n    template: function ResetPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n        i0.ɵɵtext(5, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Reset Password \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n        i0.ɵɵtext(8, \" Enter your new password below \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordComponent_Template_form_ngSubmit_10_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(11, \"mat-form-field\", 3)(12, \"mat-label\");\n        i0.ɵɵtext(13, \"New Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(14, \"input\", 4);\n        i0.ɵɵelementStart(15, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_15_listener() {\n          return ctx.hideNewPassword = !ctx.hideNewPassword;\n        });\n        i0.ɵɵelementStart(16, \"mat-icon\");\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"mat-error\");\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(20, ResetPasswordComponent_div_20_Template, 5, 5, \"div\", 6);\n        i0.ɵɵelementStart(21, \"mat-form-field\", 3)(22, \"mat-label\");\n        i0.ɵɵtext(23, \"Confirm New Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(24, \"input\", 7);\n        i0.ɵɵelementStart(25, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_25_listener() {\n          return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n        });\n        i0.ɵɵelementStart(26, \"mat-icon\");\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"mat-error\");\n        i0.ɵɵtext(29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"button\", 8);\n        i0.ɵɵtemplate(31, ResetPasswordComponent_mat_spinner_31_Template, 1, 0, \"mat-spinner\", 9)(32, ResetPasswordComponent_span_32_Template, 2, 0, \"span\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 11)(34, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_34_listener() {\n          return ctx.goToLogin();\n        });\n        i0.ɵɵelementStart(35, \"mat-icon\");\n        i0.ɵɵtext(36, \"arrow_back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(37, \" Back to Login \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"formGroup\", ctx.resetPasswordForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"type\", ctx.hideNewPassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hideNewPassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.resetPasswordForm.get(\"password\")) == null ? null : tmp_4_0.value);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"confirmPassword\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatProgressSpinner],\n    styles: [\".auth-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  max-width: 450px;\\n  width: 100%;\\n  padding: 20px;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.auth-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n}\\n\\n.auth-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.auth-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin-top: 8px;\\n  line-height: 1.4;\\n}\\n\\n.form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n.password-strength[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  margin-top: -8px;\\n}\\n\\n.strength-bar[_ngcontent-%COMP%] {\\n  height: 4px;\\n  background-color: #e0e0e0;\\n  border-radius: 2px;\\n  overflow: hidden;\\n  margin-bottom: 4px;\\n}\\n\\n.strength-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 0.3s ease, background-color 0.3s ease;\\n  border-radius: 2px;\\n}\\n\\n.strength-fill.warn[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n}\\n\\n.strength-fill.accent[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n}\\n\\n.strength-fill.primary[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n}\\n\\n.strength-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n}\\n\\n.strength-text.warn[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.strength-text.accent[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n.strength-text.primary[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-bottom: 16px;\\n  border-radius: 8px;\\n}\\n\\n.back-to-login[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n}\\n\\n@media (max-width: 480px) {\\n  .auth-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .auth-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵstyleProp", "ctx_r0", "passwordStrength", "ɵɵproperty", "getPasswordStrengthColor", "ɵɵtextInterpolate", "passwordStrengthText", "ResetPasswordComponent", "constructor", "fb", "authService", "router", "route", "snackBar", "isLoading", "hidePassword", "hideConfirmPassword", "token", "isTokenValid", "ngOnInit", "snapshot", "queryParamMap", "get", "open", "duration", "panelClass", "resetPasswordForm", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "confirmPassword", "validators", "passwordMatchValidator", "value", "setErrors", "mismatch", "onSubmit", "valid", "resetPassword", "subscribe", "next", "response", "setTimeout", "navigate", "error", "errorMessage", "message", "includes", "goBackToLogin", "requestNewReset", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "_2", "selectors", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ResetPasswordComponent_Template_form_ngSubmit_10_listener", "ResetPasswordComponent_Template_button_click_15_listener", "hideNewPassword", "ɵɵtemplate", "ResetPasswordComponent_div_20_Template", "ResetPasswordComponent_Template_button_click_25_listener", "ResetPasswordComponent_mat_spinner_31_Template", "ResetPasswordComponent_span_32_Template", "ResetPasswordComponent_Template_button_click_34_listener", "goToLogin", "getFieldError", "tmp_4_0", "loading"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\reset-password\\reset-password.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\reset-password\\reset-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-reset-password',\r\n  templateUrl: './reset-password.component.html',\r\n  styleUrls: ['./reset-password.component.scss']\r\n})\r\nexport class ResetPasswordComponent implements OnInit {\r\n  resetPasswordForm!: FormGroup;\r\n  isLoading = false;\r\n  hidePassword = true;\r\n  hideConfirmPassword = true;\r\n  token: string | null = null;\r\n  isTokenValid = true;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Get token from URL query parameters\r\n    this.token = this.route.snapshot.queryParamMap.get('token');\r\n    \r\n    if (!this.token) {\r\n      this.isTokenValid = false;\r\n      this.snackBar.open(\r\n        'Invalid reset link. Please request a new password reset.',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['error-snackbar'] }\r\n      );\r\n    }\r\n\r\n    this.resetPasswordForm = this.fb.group({\r\n      password: ['', [\r\n        Validators.required,\r\n        Validators.minLength(8),\r\n        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)\r\n      ]],\r\n      confirmPassword: ['', [Validators.required]]\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  passwordMatchValidator(group: FormGroup) {\r\n    const password = group.get('password');\r\n    const confirmPassword = group.get('confirmPassword');\r\n    \r\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\r\n      confirmPassword.setErrors({ mismatch: true });\r\n      return { mismatch: true };\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.resetPasswordForm.valid && !this.isLoading && this.token) {\r\n      this.isLoading = true;\r\n      const password = this.resetPasswordForm.get('password')?.value;      this.authService.resetPassword({\r\n        token: this.token,\r\n        password: password,\r\n        confirmPassword: password\r\n      }).subscribe({\r\n        next: (response) => {\r\n          this.isLoading = false;\r\n          this.snackBar.open(\r\n            'Password reset successfully! You can now login with your new password.',\r\n            'Close',\r\n            { duration: 5000, panelClass: ['success-snackbar'] }\r\n          );\r\n          \r\n          // Redirect to login page after successful reset\r\n          setTimeout(() => {\r\n            this.router.navigate(['/auth/login']);\r\n          }, 2000);\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          const errorMessage = error?.error?.message || 'Failed to reset password. Please try again.';\r\n          this.snackBar.open(\r\n            errorMessage,\r\n            'Close',\r\n            { duration: 5000, panelClass: ['error-snackbar'] }\r\n          );\r\n          \r\n          // If token is invalid, redirect to forgot password\r\n          if (errorMessage.includes('Invalid or expired')) {\r\n            setTimeout(() => {\r\n              this.router.navigate(['/auth/forgot-password']);\r\n            }, 3000);\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  goBackToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  requestNewReset(): void {\r\n    this.router.navigate(['/auth/forgot-password']);\r\n  }\r\n}", "<div class=\"auth-container\">\r\n  <mat-card class=\"auth-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>security</mat-icon>\r\n        Reset Password\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Enter your new password below\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <form [formGroup]=\"resetPasswordForm\" (ngSubmit)=\"onSubmit()\">\r\n        <!-- New Password -->\r\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\r\n          <mat-label>New Password</mat-label>\r\n          <input matInput [type]=\"hideNewPassword ? 'password' : 'text'\" \r\n                 formControlName=\"password\" autocomplete=\"new-password\">\r\n          <button mat-icon-button matSuffix (click)=\"hideNewPassword = !hideNewPassword\" type=\"button\">\r\n            <mat-icon>{{ hideNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n          </button>\r\n          <mat-error>{{ getFieldError('password') }}</mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Password Strength Indicator -->\r\n        <div *ngIf=\"resetPasswordForm.get('password')?.value\" class=\"password-strength\">\r\n          <div class=\"strength-bar\">\r\n            <div class=\"strength-fill\" [style.width.%]=\"passwordStrength\" [ngClass]=\"getPasswordStrengthColor()\"></div>\r\n          </div>\r\n          <span class=\"strength-text\" [ngClass]=\"getPasswordStrengthColor()\">{{ passwordStrengthText }}</span>\r\n        </div>\r\n\r\n        <!-- Confirm Password -->\r\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\r\n          <mat-label>Confirm New Password</mat-label>\r\n          <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\" \r\n                 formControlName=\"confirmPassword\" autocomplete=\"new-password\">\r\n          <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\r\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n          </button>\r\n          <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\r\n        </mat-form-field>\r\n\r\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\r\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\r\n          <span *ngIf=\"!loading\">Reset Password</span>\r\n        </button>\r\n      </form>\r\n\r\n      <div class=\"back-to-login\">\r\n        <button mat-button color=\"primary\" (click)=\"goToLogin()\">\r\n          <mat-icon>arrow_back</mat-icon>\r\n          Back to Login\r\n        </button>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;IC0BzDC,EADF,CAAAC,cAAA,cAAgF,cACpD;IACxBD,EAAA,CAAAE,SAAA,cAA2G;IAC7GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmE;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAC/FJ,EAD+F,CAAAG,YAAA,EAAO,EAChG;;;;IAHyBH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,WAAA,UAAAC,MAAA,CAAAC,gBAAA,MAAkC;IAACR,EAAA,CAAAS,UAAA,YAAAF,MAAA,CAAAG,wBAAA,GAAsC;IAE1EV,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAS,UAAA,YAAAF,MAAA,CAAAG,wBAAA,GAAsC;IAACV,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAW,iBAAA,CAAAJ,MAAA,CAAAK,oBAAA,CAA0B;;;;;IAe7FZ,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAI,MAAA,qBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;ADnCtD,OAAM,MAAOU,sBAAsB;EAQjCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAXlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,YAAY,GAAG,IAAI;EAQhB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,KAAK,GAAG,IAAI,CAACL,KAAK,CAACQ,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC;IAE3D,IAAI,CAAC,IAAI,CAACL,KAAK,EAAE;MACf,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACL,QAAQ,CAACU,IAAI,CAChB,0DAA0D,EAC1D,OAAO,EACP;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE,CAAC,gBAAgB;MAAC,CAAE,CACnD;IACH;IAEA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbnC,UAAU,CAACoC,QAAQ,EACnBpC,UAAU,CAACqC,SAAS,CAAC,CAAC,CAAC,EACvBrC,UAAU,CAACsC,OAAO,CAAC,iEAAiE,CAAC,CACtF,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAACoC,QAAQ,CAAC;KAC5C,EAAE;MAAEI,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAA,sBAAsBA,CAACP,KAAgB;IACrC,MAAMC,QAAQ,GAAGD,KAAK,CAACL,GAAG,CAAC,UAAU,CAAC;IACtC,MAAMU,eAAe,GAAGL,KAAK,CAACL,GAAG,CAAC,iBAAiB,CAAC;IAEpD,IAAIM,QAAQ,IAAII,eAAe,IAAIJ,QAAQ,CAACO,KAAK,KAAKH,eAAe,CAACG,KAAK,EAAE;MAC3EH,eAAe,CAACI,SAAS,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC7C,OAAO;QAAEA,QAAQ,EAAE;MAAI,CAAE;IAC3B;IAEA,OAAO,IAAI;EACb;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,iBAAiB,CAACa,KAAK,IAAI,CAAC,IAAI,CAACzB,SAAS,IAAI,IAAI,CAACG,KAAK,EAAE;MACjE,IAAI,CAACH,SAAS,GAAG,IAAI;MACrB,MAAMc,QAAQ,GAAG,IAAI,CAACF,iBAAiB,CAACJ,GAAG,CAAC,UAAU,CAAC,EAAEa,KAAK;MAAO,IAAI,CAACzB,WAAW,CAAC8B,aAAa,CAAC;QAClGvB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBW,QAAQ,EAAEA,QAAQ;QAClBI,eAAe,EAAEJ;OAClB,CAAC,CAACa,SAAS,CAAC;QACXC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC7B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,QAAQ,CAACU,IAAI,CAChB,wEAAwE,EACxE,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,kBAAkB;UAAC,CAAE,CACrD;UAED;UACAmB,UAAU,CAAC,MAAK;YACd,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;UACvC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChC,SAAS,GAAG,KAAK;UACtB,MAAMiC,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEE,OAAO,IAAI,6CAA6C;UAC3F,IAAI,CAACnC,QAAQ,CAACU,IAAI,CAChBwB,YAAY,EACZ,OAAO,EACP;YAAEvB,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,gBAAgB;UAAC,CAAE,CACnD;UAED;UACA,IAAIsB,YAAY,CAACE,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YAC/CL,UAAU,CAAC,MAAK;cACd,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;YACjD,CAAC,EAAE,IAAI,CAAC;UACV;QACF;OACD,CAAC;IACJ;EACF;EAEAK,aAAaA,CAAA;IACX,IAAI,CAACvC,MAAM,CAACkC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAM,eAAeA,CAAA;IACb,IAAI,CAACxC,MAAM,CAACkC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAAC,QAAAO,CAAA,G;qCAlGU7C,sBAAsB,EAAAb,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjE,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAlE,EAAA,CAAA2D,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBxD,sBAAsB;IAAAyD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP3B5E,EAJR,CAAAC,cAAA,aAA4B,kBACE,sBACT,qBACC,eACJ;QAAAD,EAAA,CAAAI,MAAA,eAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAI,MAAA,uBACF;QAAAJ,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,wBAAmB;QACjBD,EAAA,CAAAI,MAAA,sCACF;QACFJ,EADE,CAAAG,YAAA,EAAoB,EACJ;QAGhBH,EADF,CAAAC,cAAA,uBAAkB,eAC8C;QAAxBD,EAAA,CAAA8E,UAAA,sBAAAC,0DAAA;UAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;QAAA,EAAC;QAGzD5C,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,oBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACnCH,EAAA,CAAAE,SAAA,gBAC8D;QAC9DF,EAAA,CAAAC,cAAA,iBAA6F;QAA3DD,EAAA,CAAA8E,UAAA,mBAAAE,yDAAA;UAAA,OAAAH,GAAA,CAAAI,eAAA,IAAAJ,GAAA,CAAAI,eAAA;QAAA,EAA4C;QAC5EjF,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,IAAuD;QACnEJ,EADmE,CAAAG,YAAA,EAAW,EACrE;QACTH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA+B;QAC5CJ,EAD4C,CAAAG,YAAA,EAAY,EACvC;QAGjBH,EAAA,CAAAkF,UAAA,KAAAC,sCAAA,iBAAgF;QAS9EnF,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,4BAAoB;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAC3CH,EAAA,CAAAE,SAAA,gBACqE;QACrEF,EAAA,CAAAC,cAAA,iBAAqG;QAAnED,EAAA,CAAA8E,UAAA,mBAAAM,yDAAA;UAAA,OAAAP,GAAA,CAAAvD,mBAAA,IAAAuD,GAAA,CAAAvD,mBAAA;QAAA,EAAoD;QACpFtB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,IAA2D;QACvEJ,EADuE,CAAAG,YAAA,EAAW,EACzE;QACTH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAAsC;QACnDJ,EADmD,CAAAG,YAAA,EAAY,EAC9C;QAEjBH,EAAA,CAAAC,cAAA,iBAAmG;QAEjGD,EADA,CAAAkF,UAAA,KAAAG,8CAAA,yBAA2C,KAAAC,uCAAA,mBACpB;QAE3BtF,EADE,CAAAG,YAAA,EAAS,EACJ;QAGLH,EADF,CAAAC,cAAA,eAA2B,kBACgC;QAAtBD,EAAA,CAAA8E,UAAA,mBAAAS,yDAAA;UAAA,OAASV,GAAA,CAAAW,SAAA,EAAW;QAAA,EAAC;QACtDxF,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,kBAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAI,MAAA,uBACF;QAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;QA7CMH,EAAA,CAAAK,SAAA,IAA+B;QAA/BL,EAAA,CAAAS,UAAA,cAAAoE,GAAA,CAAA7C,iBAAA,CAA+B;QAIjBhC,EAAA,CAAAK,SAAA,GAA8C;QAA9CL,EAAA,CAAAS,UAAA,SAAAoE,GAAA,CAAAI,eAAA,uBAA8C;QAGlDjF,EAAA,CAAAK,SAAA,GAAuD;QAAvDL,EAAA,CAAAW,iBAAA,CAAAkE,GAAA,CAAAI,eAAA,mCAAuD;QAExDjF,EAAA,CAAAK,SAAA,GAA+B;QAA/BL,EAAA,CAAAW,iBAAA,CAAAkE,GAAA,CAAAY,aAAA,aAA+B;QAItCzF,EAAA,CAAAK,SAAA,EAA8C;QAA9CL,EAAA,CAAAS,UAAA,UAAAiF,OAAA,GAAAb,GAAA,CAAA7C,iBAAA,CAAAJ,GAAA,+BAAA8D,OAAA,CAAAjD,KAAA,CAA8C;QAUlCzC,EAAA,CAAAK,SAAA,GAAkD;QAAlDL,EAAA,CAAAS,UAAA,SAAAoE,GAAA,CAAAvD,mBAAA,uBAAkD;QAGtDtB,EAAA,CAAAK,SAAA,GAA2D;QAA3DL,EAAA,CAAAW,iBAAA,CAAAkE,GAAA,CAAAvD,mBAAA,mCAA2D;QAE5DtB,EAAA,CAAAK,SAAA,GAAsC;QAAtCL,EAAA,CAAAW,iBAAA,CAAAkE,GAAA,CAAAY,aAAA,oBAAsC;QAG2BzF,EAAA,CAAAK,SAAA,EAAoB;QAApBL,EAAA,CAAAS,UAAA,aAAAoE,GAAA,CAAAc,OAAA,CAAoB;QAClF3F,EAAA,CAAAK,SAAA,EAAa;QAAbL,EAAA,CAAAS,UAAA,SAAAoE,GAAA,CAAAc,OAAA,CAAa;QACpB3F,EAAA,CAAAK,SAAA,EAAc;QAAdL,EAAA,CAAAS,UAAA,UAAAoE,GAAA,CAAAc,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}