{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"./services/loading.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/divider\";\nconst _c0 = a0 => ({\n  \"with-navbar\": a0\n});\nconst _c1 = a0 => ({\n  \"verified\": a0\n});\nfunction AppComponent_mat_toolbar_0_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" 2FA Enabled \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_mat_toolbar_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-toolbar\", 5)(1, \"span\", 6)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 7);\n    i0.ɵɵelementStart(6, \"button\", 8)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 9)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Payments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 10)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 11)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-menu\", null, 0)(23, \"div\", 12)(24, \"div\", 13);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 14);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 15)(29, \"span\", 16)(30, \"mat-icon\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, AppComponent_mat_toolbar_0_span_33_Template, 4, 0, \"span\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"mat-divider\");\n    i0.ɵɵelementStart(35, \"button\", 18)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_toolbar_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(40, \"mat-icon\");\n    i0.ɵɵtext(41, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const userMenu_r3 = i0.ɵɵreference(22);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser && ctx_r1.currentUser.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.isEmailVerified()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isEmailVerified() ? \"verified\" : \"warning\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isEmailVerified() ? \"Verified\" : \"Unverified\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTwoFactorEnabled());\n  }\n}\nfunction AppComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"mat-spinner\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Please verify your email address to access all features.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵtext(6, \"Verify Now\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, loadingService, router) {\n    this.authService = authService;\n    this.loadingService = loadingService;\n    this.router = router;\n    this.title = 'SecureApp';\n    this.currentUser = null;\n    this.loading$ = this.loadingService.loading$;\n    this.showNavbar = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.authService.currentUser.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUser = user;\n    });\n    // Auto logout on token expiration\n    setInterval(() => {\n      this.authService.autoLogout();\n    }, 60000); // Check every minute\n    // Show/hide navbar based on route\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(event => {\n      this.showNavbar = !event.url.startsWith('/auth');\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return `${this.currentUser.firstName} ${this.currentUser.lastName}`;\n    }\n    return '';\n  }\n  isEmailVerified() {\n    return this.authService.isEmailVerified;\n  }\n  isTwoFactorEnabled() {\n    return this.authService.isTwoFactorEnabled;\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.LoadingService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    standalone: false,\n    decls: 6,\n    vars: 8,\n    consts: [[\"userMenu\", \"matMenu\"], [\"color\", \"primary\", \"class\", \"navbar\", 4, \"ngIf\"], [3, \"ngClass\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"security-notification\", 4, \"ngIf\"], [\"color\", \"primary\", 1, \"navbar\"], [1, \"app-title\"], [1, \"spacer\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/payment\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/profile\", \"routerLinkActive\", \"active\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-email\"], [1, \"user-status\"], [1, \"status-badge\", 3, \"ngClass\"], [\"class\", \"status-badge verified\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"status-badge\", \"verified\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"], [1, \"security-notification\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/profile\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AppComponent_mat_toolbar_0_Template, 43, 10, \"mat-toolbar\", 1);\n        i0.ɵɵelementStart(1, \"main\", 2);\n        i0.ɵɵelement(2, \"router-outlet\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, AppComponent_div_3_Template, 2, 0, \"div\", 3);\n        i0.ɵɵpipe(4, \"async\");\n        i0.ɵɵtemplate(5, AppComponent_div_5_Template, 7, 0, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavbar && ctx.currentUser);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c0, ctx.showNavbar && ctx.currentUser));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 4, ctx.loading$));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentUser && !ctx.isEmailVerified());\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgIf, i3.RouterOutlet, i3.RouterLink, i3.RouterLinkActive, i5.MatToolbar, i6.MatButton, i6.MatIconButton, i7.MatIcon, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatProgressSpinner, i10.MatDivider, i4.AsyncPipe],\n    styles: [\".navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.app-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1.25rem;\\n  font-weight: 500;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin: 0 0.25rem;\\n}\\n.navbar[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\nmain[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n}\\nmain.with-navbar[_ngcontent-%COMP%] {\\n  padding-top: 64px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  min-width: 250px;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1rem;\\n  margin-bottom: 0.25rem;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.875rem;\\n  margin-bottom: 0.75rem;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.user-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  background: #ffebee;\\n  color: #f44336;\\n}\\n.user-info[_ngcontent-%COMP%]   .status-badge.verified[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #4caf50;\\n}\\n.user-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n\\n.security-notification[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 64px;\\n  left: 0;\\n  right: 0;\\n  background: #ff9800;\\n  color: white;\\n  padding: 0.75rem 1rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  z-index: 999;\\n}\\n.security-notification[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.security-notification[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.security-notification[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .navbar[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    padding: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "Subject", "takeUntil", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "AppComponent_mat_toolbar_0_span_33_Template", "ɵɵlistener", "AppComponent_mat_toolbar_0_Template_button_click_39_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵadvance", "ɵɵtextInterpolate1", "title", "ɵɵproperty", "userMenu_r3", "ɵɵtextInterpolate", "getUserDisplayName", "currentUser", "email", "ɵɵpureFunction1", "_c1", "isEmailVerified", "isTwoFactorEnabled", "AppComponent", "constructor", "authService", "loadingService", "router", "loading$", "showNavbar", "destroy$", "ngOnInit", "pipe", "subscribe", "user", "setInterval", "autoLogout", "events", "event", "url", "startsWith", "ngOnDestroy", "next", "complete", "firstName", "lastName", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "LoadingService", "i3", "Router", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_mat_toolbar_0_Template", "AppComponent_div_3_Template", "AppComponent_div_5_Template", "_c0", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter } from 'rxjs/operators';\nimport { AuthService } from './services/auth.service';\nimport { LoadingService } from './services/loading.service';\nimport { User } from './models/user.model';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  standalone: false\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  title = 'SecureApp';\n  currentUser: User | null = null;\n  loading$ = this.loadingService.loading$;\n  showNavbar = false;\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private authService: AuthService,\n    private loadingService: LoadingService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        this.currentUser = user;\n      });\n\n    // Auto logout on token expiration\n    setInterval(() => {\n      this.authService.autoLogout();\n    }, 60000); // Check every minute\n\n    // Show/hide navbar based on route\n    this.router.events\n      .pipe(\n        filter(event => event instanceof NavigationEnd),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((event: NavigationEnd) => {\n        this.showNavbar = !event.url.startsWith('/auth');\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  getUserDisplayName(): string {\n    if (this.currentUser) {\n      return `${this.currentUser.firstName} ${this.currentUser.lastName}`;\n    }\n    return '';\n  }\n\n  isEmailVerified(): boolean {\n    return this.authService.isEmailVerified;\n  }\n\n  isTwoFactorEnabled(): boolean {\n    return this.authService.isTwoFactorEnabled;\n  }\n}\n", "<!-- Navigation Bar -->\n<mat-toolbar *ngIf=\"showNavbar && currentUser\" color=\"primary\" class=\"navbar\">\n  <span class=\"app-title\">\n    <mat-icon>security</mat-icon>\n    {{ title }}\n  </span>\n  \n  <span class=\"spacer\"></span>\n  \n  <!-- Navigation Links -->\n  <button mat-button routerLink=\"/dashboard\" routerLinkActive=\"active\">\n    <mat-icon>dashboard</mat-icon>\n    Dashboard\n  </button>\n  \n  <button mat-button routerLink=\"/payment\" routerLinkActive=\"active\">\n    <mat-icon>payment</mat-icon>\n    Payments\n  </button>\n  \n  <button mat-button routerLink=\"/profile\" routerLinkActive=\"active\">\n    <mat-icon>person</mat-icon>\n    Profile\n  </button>\n  \n  <!-- User Menu -->\n  <button mat-icon-button [matMenuTriggerFor]=\"userMenu\">\n    <mat-icon>account_circle</mat-icon>\n  </button>\n  \n  <mat-menu #userMenu=\"matMenu\">\n    <div class=\"user-info\">\n      <div class=\"user-name\">{{ getUserDisplayName() }}</div>\n      <div class=\"user-email\">{{ currentUser && currentUser.email }}</div>\n      <div class=\"user-status\">\n        <span class=\"status-badge\" [ngClass]=\"{ 'verified': isEmailVerified() }\">\n          <mat-icon>{{ isEmailVerified() ? 'verified' : 'warning' }}</mat-icon>\n          {{ isEmailVerified() ? 'Verified' : 'Unverified' }}\n        </span>\n        <span *ngIf=\"isTwoFactorEnabled()\" class=\"status-badge verified\">\n          <mat-icon>security</mat-icon>\n          2FA Enabled\n        </span>\n      </div>\n    </div>\n    \n    <mat-divider></mat-divider>\n    \n    <button mat-menu-item routerLink=\"/profile\">\n      <mat-icon>settings</mat-icon>\n      Settings\n    </button>\n    \n    <button mat-menu-item (click)=\"logout()\">\n      <mat-icon>logout</mat-icon>\n      Logout\n    </button>\n  </mat-menu>\n</mat-toolbar>\n\n<!-- Main Content -->\n<main [ngClass]=\"{ 'with-navbar': showNavbar && currentUser }\">\n  <router-outlet></router-outlet>\n</main>\n\n<!-- Loading Overlay -->\n<div *ngIf=\"loading$ | async\" class=\"loading-overlay\">\n  <mat-spinner diameter=\"50\"></mat-spinner>\n</div>\n\n<!-- Security Notifications -->\n<div *ngIf=\"currentUser && !isEmailVerified()\" class=\"security-notification\">\n  <mat-icon>warning</mat-icon>\n  <span>Please verify your email address to access all features.</span>\n  <button mat-button color=\"accent\" routerLink=\"/profile\">Verify Now</button>\n</div>\n"], "mappings": "AACA,SAAiBA,aAAa,QAAQ,iBAAiB;AACvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;ICqCxCC,EADF,CAAAC,cAAA,eAAiE,eACrD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAvCXH,EAFJ,CAAAC,cAAA,qBAA8E,cACpD,eACZ;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEPH,EAAA,CAAAI,SAAA,cAA4B;IAI1BJ,EADF,CAAAC,cAAA,gBAAqE,eACzD;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,iBAAmE,gBACvD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,kBAAmE,gBACvD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAIPH,EADF,CAAAC,cAAA,kBAAuD,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EAC5B;IAILH,EAFJ,CAAAC,cAAA,yBAA8B,eACL,eACE;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGhEH,EAFJ,CAAAC,cAAA,eAAyB,gBACkD,gBAC7D;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrEH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAK,UAAA,KAAAC,2CAAA,mBAAiE;IAKrEN,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAI,SAAA,mBAA2B;IAGzBJ,EADF,CAAAC,cAAA,kBAA4C,gBAChC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAO,UAAA,mBAAAC,6DAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IACtCd,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,gBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACA,EACC;;;;;IAtDVH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAL,MAAA,CAAAM,KAAA,MACF;IAqBwBjB,EAAA,CAAAe,SAAA,IAA8B;IAA9Bf,EAAA,CAAAkB,UAAA,sBAAAC,WAAA,CAA8B;IAM3BnB,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAU,kBAAA,GAA0B;IACzBrB,EAAA,CAAAe,SAAA,GAAsC;IAAtCf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAW,WAAA,IAAAX,MAAA,CAAAW,WAAA,CAAAC,KAAA,CAAsC;IAEjCvB,EAAA,CAAAe,SAAA,GAA6C;IAA7Cf,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAd,MAAA,CAAAe,eAAA,IAA6C;IAC5D1B,EAAA,CAAAe,SAAA,GAAgD;IAAhDf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAe,eAAA,4BAAgD;IAC1D1B,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAL,MAAA,CAAAe,eAAA,oCACF;IACO1B,EAAA,CAAAe,SAAA,EAA0B;IAA1Bf,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAgB,kBAAA,GAA0B;;;;;IA2BzC3B,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAI,SAAA,sBAAyC;IAC3CJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAIJH,EADF,CAAAC,cAAA,cAA6E,eACjE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,+DAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACpEF,EADoE,CAAAG,YAAA,EAAS,EACvE;;;AD7DN,OAAM,MAAOyB,YAAY;EAQvBC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAf,KAAK,GAAG,WAAW;IACnB,KAAAK,WAAW,GAAgB,IAAI;IAC/B,KAAAW,QAAQ,GAAG,IAAI,CAACF,cAAc,CAACE,QAAQ;IACvC,KAAAC,UAAU,GAAG,KAAK;IAEV,KAAAC,QAAQ,GAAG,IAAItC,OAAO,EAAQ;EAMnC;EAEHuC,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,WAAW,CAACR,WAAW,CACzBe,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAACjB,WAAW,GAAGiB,IAAI;IACzB,CAAC,CAAC;IAEJ;IACAC,WAAW,CAAC,MAAK;MACf,IAAI,CAACV,WAAW,CAACW,UAAU,EAAE;IAC/B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA,IAAI,CAACT,MAAM,CAACU,MAAM,CACfL,IAAI,CACHtC,MAAM,CAAC4C,KAAK,IAAIA,KAAK,YAAY/C,aAAa,CAAC,EAC/CE,SAAS,CAAC,IAAI,CAACqC,QAAQ,CAAC,CACzB,CACAG,SAAS,CAAEK,KAAoB,IAAI;MAClC,IAAI,CAACT,UAAU,GAAG,CAACS,KAAK,CAACC,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC;IAClD,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,QAAQ,CAACY,IAAI,EAAE;IACpB,IAAI,CAACZ,QAAQ,CAACa,QAAQ,EAAE;EAC1B;EAEAlC,MAAMA,CAAA;IACJ,IAAI,CAACgB,WAAW,CAAChB,MAAM,EAAE;EAC3B;EAEAO,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACC,WAAW,EAAE;MACpB,OAAO,GAAG,IAAI,CAACA,WAAW,CAAC2B,SAAS,IAAI,IAAI,CAAC3B,WAAW,CAAC4B,QAAQ,EAAE;IACrE;IACA,OAAO,EAAE;EACX;EAEAxB,eAAeA,CAAA;IACb,OAAO,IAAI,CAACI,WAAW,CAACJ,eAAe;EACzC;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACG,WAAW,CAACH,kBAAkB;EAC5C;EAAC,QAAAwB,CAAA,G;qCA5DUvB,YAAY,EAAA5B,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxD,EAAA,CAAAoD,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZ/B,YAAY;IAAAgC,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbzBnE,EAAA,CAAAK,UAAA,IAAAgE,mCAAA,2BAA8E;QA4D9ErE,EAAA,CAAAC,cAAA,cAA+D;QAC7DD,EAAA,CAAAI,SAAA,oBAA+B;QACjCJ,EAAA,CAAAG,YAAA,EAAO;QAGPH,EAAA,CAAAK,UAAA,IAAAiE,2BAAA,iBAAsD;;QAKtDtE,EAAA,CAAAK,UAAA,IAAAkE,2BAAA,iBAA6E;;;QAtE/DvE,EAAA,CAAAkB,UAAA,SAAAkD,GAAA,CAAAlC,UAAA,IAAAkC,GAAA,CAAA9C,WAAA,CAA+B;QA4DvCtB,EAAA,CAAAe,SAAA,EAAwD;QAAxDf,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwB,eAAA,IAAAgD,GAAA,EAAAJ,GAAA,CAAAlC,UAAA,IAAAkC,GAAA,CAAA9C,WAAA,EAAwD;QAKxDtB,EAAA,CAAAe,SAAA,GAAsB;QAAtBf,EAAA,CAAAkB,UAAA,SAAAlB,EAAA,CAAAyE,WAAA,OAAAL,GAAA,CAAAnC,QAAA,EAAsB;QAKtBjC,EAAA,CAAAe,SAAA,GAAuC;QAAvCf,EAAA,CAAAkB,UAAA,SAAAkD,GAAA,CAAA9C,WAAA,KAAA8C,GAAA,CAAA1C,eAAA,GAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}