=== Page 439 ===

439Since we never actually created a class from this and you were not able to explicitly
resolve this, we utilized the new container swapping in order to swap a class out for this
container binding.
All instances above should be changed to:
Don't forgot to also do your boot methods on your Service Providers as well:
As well as all your middleware and custom code:
You may have classes you binded personally to the container like this:def show(self, Mail):
    Mail.to(..)
from masonite import Mail
def show(self, mail: Mail):
    mail.to(..)
def boot(self, request: Request):
    ..request..
class AuthenticationMiddleware(object):
    """ Middleware To Check If The User Is Logged In """
    def __init__(self, request: Request):
        """ Inject Any Dependencies From The Service Container """
        self.request = request
    ...
def slack_send(self, IntegrationManager):
    return IntegrationManager.driver('slack').scopes('incoming-
webhook').state(self.request.param('id')).redirect()Resolving your own code6/12/25, 3:02 AM Masonite Documentation