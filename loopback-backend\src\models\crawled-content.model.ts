import {Entity, model, property, belongsTo} from '@loopback/repository';
import {CrawlJob} from './crawl-job.model';

@model({
  settings: {
    strict: true,
    indexes: {
      crawlJobIdIndex: {
        keys: {
          crawlJobId: 1,
        },
      },
      urlIndex: {
        keys: {
          url: 1,
        },
      },
      contentTypeIndex: {
        keys: {
          contentType: 1,
        },
      },
      statusIndex: {
        keys: {
          status: 1,
        },
      },
      createdAtIndex: {
        keys: {
          createdAt: -1,
        },
      },
    },
    postgresql: {
      schema: 'public',
      table: 'crawled_content'
    }
  },
})
export class CrawledContent extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      format: 'uri',
      minLength: 10,
      maxLength: 2000,
      errorMessage: 'URL should be a valid URL',
    },
  })
  url: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 1,
      maxLength: 500,
    },
  })
  title: string;

  @property({
    type: 'string',
    postgresql: {
      dataType: 'TEXT'
    }
  })
  content?: string;

  @property({
    type: 'string',
    postgresql: {
      dataType: 'TEXT'
    }
  })
  htmlContent?: string;

  @property({
    type: 'string',
    postgresql: {
      dataType: 'TEXT'
    }
  })
  markdownContent?: string;

  @property({
    type: 'string',
    required: true,
    default: 'text/html',
  })
  contentType: string;

  @property({
    type: 'number',
    default: 0,
    jsonSchema: {
      minimum: 0,
      maximum: 10,
    },
  })
  depth: number;

  @property({
    type: 'number',
    default: 0,
  })
  contentLength: number;

  @property({
    type: 'number',
    default: 200,
    jsonSchema: {
      minimum: 100,
      maximum: 600,
    },
  })
  statusCode: number;

  @property({
    type: 'string',
    required: true,
    default: 'pending',
    jsonSchema: {
      enum: ['pending', 'processing', 'completed', 'failed', 'skipped'],
    },
  })
  status: string;

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  extractedLinks: string[];

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  extractedImages: string[];

  @property({
    type: 'object',
    default: {},
  })
  metadata: object;

  @property({
    type: 'object',
    default: {},
  })
  headers: object;

  @property({
    type: 'string',
  })
  parentUrl?: string;

  @property({
    type: 'string',
  })
  errorMessage?: string;

  @property({
    type: 'number',
    default: 0,
  })
  processingTimeMs: number;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'file_path'
    }
  })
  filePath?: string;

  @property({
    type: 'number',
    postgresql: {
      columnName: 'file_size'
    }
  })
  fileSize?: number;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'file_hash'
    }
  })
  fileHash?: string;

  @property({
    type: 'boolean',
    default: false,
    postgresql: {
      columnName: 'is_selected'
    }
  })
  isSelected: boolean;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'selection_group'
    }
  })
  selectionGroup?: string;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'crawled_at'
    }
  })
  crawledAt?: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'created_at'
    }
  })
  createdAt: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'updated_at'
    }
  })
  updatedAt: Date;

  @belongsTo(() => CrawlJob, {}, {
    postgresql: {
      columnName: 'crawl_job_id'
    }
  })
  crawlJobId: string;

  constructor(data?: Partial<CrawledContent>) {
    super(data);
  }
}

export interface CrawledContentRelations {
  crawlJob?: CrawlJob;
}

export type CrawledContentWithRelations = CrawledContent & CrawledContentRelations;
