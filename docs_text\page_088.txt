=== Page 89 ===

89That's it! It's important to note that the variable it is filtering is always passed as the first
argument and all other parameters are passed in after so we could do something like:
and then our function would look like:
Adding filters is typically done inside your appService Provider (if you don't have one, you
should create one):
View tests are simply custom boolean expressions that can be used in your templates.
We may want to run boolean tests on specific objects to assert that they pass a test. For
example we may want to test if a user is an owner of a company like this:{{ variable|slug('-') }}
def slug(variable, replace_with):
    return variable.replace(' ', replace_with)
from masonite.facades import View
class AppProvider(Provider):
    def register(self):
        View.filter('slug', self.slug)
    @staticmethod
    def slug(item):
        return item.replace(' ', '-')Adding Filters
View Tests6/12/25, 3:02 AM Masonite Documentation