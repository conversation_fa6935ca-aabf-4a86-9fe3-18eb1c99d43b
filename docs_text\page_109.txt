=== Page 110 ===

110Simple exceptions will be handled as a 500 Server Error if no custom exceptions handlers
are defined for it.
HTTP exceptions are standard exceptions using frequently used HTTP status codes such
as 500, 404 or 403.
Those exceptions will be rendered by the HTTPExceptionHandler with the
corresponding status code and the corresponding default error template if it exists in 
errors/. (Note that in debug mode this template won't be rendered and the default
Exceptionite error page will be rendered instead).
The following HTTP exceptions are bundled into Masonite:
•AuthorizationException (403)
•RouteNotFoundException (404)
•ModelNotFoundException (404)
•MethodNotAllowedException (405)
In a default Masonite project, existing errors views are errors/404.html, 
errors/403.html and errors/500.html. Those views can be customized.
You can also build a custom HTTP exception by setting is_http_exception=True to it
and by defining the get_response(), get_status() and get_headers() methods:HTTP Exceptions6/12/25, 3:02 AM Masonite Documentation