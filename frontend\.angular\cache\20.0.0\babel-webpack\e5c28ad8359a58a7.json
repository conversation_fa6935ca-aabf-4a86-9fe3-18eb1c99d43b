{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/two-factor.service\";\nimport * as i3 from \"../../services/oauth.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/tabs\";\nimport * as i14 from \"../../components/auth/two-factor/two-factor-management.component\";\nfunction ProfileComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"img\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatarUrl, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r0.currentUser.firstName || \"\") + \" \" + (ctx_r0.currentUser.lastName || \"\"));\n  }\n}\nfunction ProfileComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Connected via:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nfunction ProfileComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.enableEditMode());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Last name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, ProfileComponent_mat_card_47_mat_error_21_span_1_Template, 2, 0, \"span\", 32)(2, ProfileComponent_mat_card_47_mat_error_21_span_2_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"email\"]);\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 41);\n  }\n}\nfunction ProfileComponent_mat_card_47_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 27)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Edit Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"form\", 28);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_mat_card_47_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.saveProfile());\n    });\n    i0.ɵɵelementStart(6, \"div\", 29)(7, \"mat-form-field\", 30)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 31);\n    i0.ɵɵtemplate(11, ProfileComponent_mat_card_47_mat_error_11_Template, 2, 0, \"mat-error\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 30)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 33);\n    i0.ɵɵtemplate(16, ProfileComponent_mat_card_47_mat_error_16_Template, 2, 0, \"mat-error\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 34)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 35);\n    i0.ɵɵtemplate(21, ProfileComponent_mat_card_47_mat_error_21_Template, 3, 2, \"mat-error\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-form-field\", 34)(23, \"mat-label\");\n    i0.ɵɵtext(24, \"Phone (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 37)(27, \"button\", 38);\n    i0.ɵɵtemplate(28, ProfileComponent_mat_card_47_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 39)(29, ProfileComponent_mat_card_47_span_29_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_mat_card_47_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancelEdit());\n    });\n    i0.ɵɵtext(31, \" Cancel \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.editProfileForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.editProfileForm.get(\"firstName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.editProfileForm.get(\"firstName\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.editProfileForm.get(\"lastName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.editProfileForm.get(\"lastName\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading || ctx_r0.editProfileForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nfunction ProfileComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Connected via \", ctx_r0.getOAuthProviderName(), \" \\u2022 You can also set a password for direct login\");\n  }\n}\nfunction ProfileComponent_div_69_mat_form_field_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 30)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 50);\n    i0.ɵɵelementStart(4, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_mat_form_field_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.hideCurrentPassword = !ctx_r0.hideCurrentPassword);\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideCurrentPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideCurrentPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"currentPassword\"));\n  }\n}\nfunction ProfileComponent_div_69_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\", 43);\n    i0.ɵɵtext(2, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Set up a password to enable direct login with your email address.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"small\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"You can continue using \", ctx_r0.getOAuthProviderName(), \" login or use your new password.\");\n  }\n}\nfunction ProfileComponent_div_69_mat_form_field_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 30)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"2FA Code (Required)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 52);\n    i0.ɵɵelementStart(4, \"mat-icon\", 53);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-hint\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-error\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"twoFactorToken\"));\n  }\n}\nfunction ProfileComponent_div_69_mat_spinner_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 41);\n  }\n}\nfunction ProfileComponent_div_69_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Change Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"form\", 28);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_69_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangePassword());\n    });\n    i0.ɵɵtemplate(2, ProfileComponent_div_69_mat_form_field_2_Template, 9, 3, \"mat-form-field\", 45)(3, ProfileComponent_div_69_div_3_Template, 8, 1, \"div\", 46);\n    i0.ɵɵelementStart(4, \"mat-form-field\", 30)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 47);\n    i0.ɵɵelementStart(8, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideNewPassword = !ctx_r0.hideNewPassword);\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-error\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 30)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 49);\n    i0.ɵɵelementStart(17, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideConfirmPassword = !ctx_r0.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"mat-error\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, ProfileComponent_div_69_mat_form_field_22_Template, 10, 1, \"mat-form-field\", 45);\n    i0.ɵɵelementStart(23, \"div\", 37)(24, \"button\", 38);\n    i0.ɵɵtemplate(25, ProfileComponent_div_69_mat_spinner_25_Template, 1, 0, \"mat-spinner\", 39)(26, ProfileComponent_div_69_span_26_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵtext(28, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.changePasswordForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOAuthUser());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOAuthUser());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideNewPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideNewPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"newPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"confirmPassword\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.twoFactorStatus.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, twoFactorService, oauthService, formBuilder, snackBar) {\n    this.authService = authService;\n    this.twoFactorService = twoFactorService;\n    this.oauthService = oauthService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.currentUser = null;\n    this.twoFactorStatus = {\n      enabled: false\n    };\n    this.loading = false;\n    this.editMode = false;\n    this.hideCurrentPassword = true;\n    this.hideNewPassword = true;\n    this.hideConfirmPassword = true;\n    this.showChangePassword = false;\n    this.changePasswordForm = this.formBuilder.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      twoFactorToken: ['']\n    }, {\n      validators: this.passwordMatchValidator\n    });\n    this.editProfileForm = this.formBuilder.group({\n      firstName: ['', [Validators.required]],\n      lastName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.currentUserValue;\n    this.initializeEditForm();\n    this.load2FAStatus();\n  }\n  initializeEditForm() {\n    if (this.currentUser) {\n      this.editProfileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        email: this.currentUser.email || '',\n        phone: this.currentUser.phone || ''\n      });\n    }\n  }\n  enableEditMode() {\n    this.editMode = true;\n    this.initializeEditForm();\n  }\n  cancelEdit() {\n    this.editMode = false;\n    this.initializeEditForm();\n  }\n  saveProfile() {\n    if (this.editProfileForm.invalid) {\n      this.markFormGroupTouched(this.editProfileForm);\n      return;\n    }\n    this.loading = true;\n    const profileData = this.editProfileForm.value;\n    this.authService.updateProfile(profileData).subscribe({\n      next: response => {\n        this.snackBar.open('Profile updated successfully!', 'Close', {\n          duration: 3000\n        });\n        this.editMode = false;\n        this.loading = false;\n        // Update current user data\n        if (this.currentUser) {\n          this.currentUser = {\n            ...this.currentUser,\n            ...profileData\n          };\n        }\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to update profile', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  toggleChangePassword() {\n    this.showChangePassword = !this.showChangePassword;\n    if (!this.showChangePassword) {\n      this.changePasswordForm.reset();\n    }\n  }\n  onChangePassword() {\n    if (this.changePasswordForm.invalid) {\n      this.markFormGroupTouched(this.changePasswordForm);\n      return;\n    }\n    this.loading = true;\n    const formValue = this.changePasswordForm.value;\n    this.authService.changePassword(formValue.currentPassword, formValue.newPassword, formValue.twoFactorToken || undefined).subscribe({\n      next: response => {\n        this.snackBar.open('Password changed successfully!', 'Close', {\n          duration: 3000\n        });\n        this.changePasswordForm.reset();\n        this.showChangePassword = false;\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Failed to change password', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onForgotPassword() {\n    if (!this.currentUser?.email) {\n      this.snackBar.open('No email address found', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.loading = true;\n    this.authService.forgotPassword(this.currentUser.email).subscribe({\n      next: response => {\n        this.snackBar.open('Password reset instructions have been sent to your email', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to send password reset email', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  isOAuthUser() {\n    return this.oauthService.isOAuthUser(this.currentUser);\n  }\n  getOAuthProviderName() {\n    return this.oauthService.getOAuthProviderName(this.currentUser);\n  }\n  getOAuthProviderIcon() {\n    return this.oauthService.getOAuthProviderIcon(this.currentUser);\n  }\n  getOAuthProviderColor() {\n    return this.oauthService.getOAuthProviderColor(this.currentUser);\n  }\n  getFieldError(fieldName) {\n    const field = this.changePasswordForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n    }\n    return '';\n  }\n  passwordMatchValidator(form) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n    return null;\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  load2FAStatus() {\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: status => {\n        this.twoFactorStatus = status;\n      },\n      error: error => {\n        console.error('Failed to load 2FA status:', error);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileComponent,\n    selectors: [[\"app-profile\"]],\n    standalone: false,\n    decls: 73,\n    vars: 28,\n    consts: [[1, \"profile-container\"], [1, \"container\"], [\"animationDuration\", \"0ms\", 1, \"profile-tabs\"], [\"label\", \"Profile Information\"], [1, \"tab-content\"], [1, \"user-info\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-details\"], [\"class\", \"oauth-info\", 4, \"ngIf\"], [1, \"account-status\"], [1, \"status-chips\"], [1, \"status-chip\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"edit-profile-card\", 4, \"ngIf\"], [\"label\", \"Security\"], [1, \"security-section\"], [1, \"section-header\"], [1, \"password-actions\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"class\", \"oauth-password-notice\", 4, \"ngIf\"], [\"class\", \"change-password-form\", 4, \"ngIf\"], [\"label\", \"Two-Factor Authentication\"], [1, \"user-avatar\"], [3, \"src\", \"alt\"], [1, \"oauth-info\"], [1, \"oauth-provider\"], [1, \"edit-profile-card\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"autocomplete\", \"given-name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"form-field\", \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"type\", \"tel\", \"autocomplete\", \"tel\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"diameter\", \"20\"], [1, \"oauth-password-notice\"], [\"color\", \"primary\"], [1, \"change-password-form\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [\"class\", \"oauth-password-setup-info\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"newPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"matInput\", \"\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"current-password\", 3, \"type\"], [1, \"oauth-password-setup-info\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matSuffix\", \"\"]],\n    template: function ProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Profile & Security Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"mat-tab-group\", 2)(5, \"mat-tab\", 3)(6, \"div\", 4)(7, \"mat-card\")(8, \"mat-card-header\")(9, \"mat-card-title\");\n        i0.ɵɵtext(10, \"User Information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 5);\n        i0.ɵɵtemplate(13, ProfileComponent_div_13_Template, 2, 2, \"div\", 6);\n        i0.ɵɵelementStart(14, \"div\", 7)(15, \"p\")(16, \"strong\");\n        i0.ɵɵtext(17, \"Name:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n        i0.ɵɵtext(21, \"Email:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n        i0.ɵɵtext(25, \"Phone:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"p\")(28, \"strong\");\n        i0.ɵɵtext(29, \"Member since:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(30);\n        i0.ɵɵpipe(31, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, ProfileComponent_div_32_Template, 7, 5, \"div\", 8);\n        i0.ɵɵelementStart(33, \"div\", 9)(34, \"div\", 10)(35, \"div\", 11)(36, \"mat-icon\");\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"span\");\n        i0.ɵɵtext(39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 11)(41, \"mat-icon\");\n        i0.ɵɵtext(42, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"span\");\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(45, \"mat-card-actions\");\n        i0.ɵɵtemplate(46, ProfileComponent_button_46_Template, 4, 0, \"button\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(47, ProfileComponent_mat_card_47_Template, 32, 7, \"mat-card\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"mat-tab\", 14)(49, \"div\", 4)(50, \"mat-card\")(51, \"mat-card-header\")(52, \"mat-card-title\");\n        i0.ɵɵtext(53, \"Password Settings\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"mat-card-content\")(55, \"div\", 15)(56, \"div\", 16)(57, \"h3\");\n        i0.ɵɵtext(58, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(59, \"div\", 17)(60, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_60_listener() {\n          return ctx.toggleChangePassword();\n        });\n        i0.ɵɵelementStart(61, \"mat-icon\");\n        i0.ɵɵtext(62);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(63);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(64, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_64_listener() {\n          return ctx.onForgotPassword();\n        });\n        i0.ɵɵelementStart(65, \"mat-icon\");\n        i0.ɵɵtext(66, \"help\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(67, \" Forgot Password \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(68, ProfileComponent_div_68_Template, 5, 1, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(69, ProfileComponent_div_69_Template, 29, 13, \"div\", 21);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(70, \"mat-tab\", 22)(71, \"div\", 4);\n        i0.ɵɵelement(72, \"app-two-factor-management\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentUser == null ? null : ctx.currentUser.avatarUrl);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(31, 25, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"verified\", ctx.currentUser == null ? null : ctx.currentUser.emailVerified)(\"unverified\", !(ctx.currentUser == null ? null : ctx.currentUser.emailVerified));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"Email \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"enabled\", ctx.twoFactorStatus.enabled)(\"disabled\", !ctx.twoFactorStatus.enabled);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"2FA \", ctx.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.editMode);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n        i0.ɵɵadvance(15);\n        i0.ɵɵtextInterpolate(ctx.showChangePassword ? \"expand_less\" : \"expand_more\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.showChangePassword ? \"Cancel\" : \"Change Password\", \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showChangePassword);\n      }\n    },\n    dependencies: [i6.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.MaxLengthValidator, i4.FormGroupDirective, i4.FormControlName, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatHint, i10.MatError, i10.MatSuffix, i11.MatInput, i12.MatProgressSpinner, i13.MatTab, i13.MatTabGroup, i14.TwoFactorManagementComponent, i6.DatePipe],\n    styles: [\".profile-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: #f5f5f5;\\n  padding: 2rem;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 2rem;\\n}\\n\\nmat-card[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  align-items: flex-start;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #e0e0e0;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-right: 0.5rem;\\n}\\n\\n.oauth-info[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.25rem 0.75rem;\\n  background: #f5f5f5;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n}\\n.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.account-status[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  flex-wrap: wrap;\\n  margin-top: 0.5rem;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 16px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%] {\\n  background: rgba(76, 175, 80, 0.1);\\n  color: #4caf50;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%] {\\n  background: rgba(255, 152, 0, 0.1);\\n  color: #ff9800;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%] {\\n  background: rgba(33, 150, 243, 0.1);\\n  color: #2196f3;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%] {\\n  background: rgba(158, 158, 158, 0.1);\\n  color: #9e9e9e;\\n}\\n\\n.security-section[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #ff9800;\\n  font-size: 0.875rem;\\n}\\n\\n.change-password-form[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-top: 1.5rem;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n.profile-tabs[_ngcontent-%COMP%]   .mat-tab-body-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  overflow: visible;\\n}\\n.profile-tabs[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem 0;\\n}\\n\\n.edit-profile-card[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-field.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-top: 1rem;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n@media (max-width: 768px) {\\n  .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wcm9maWxlL3Byb2ZpbGUuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi9Nb2R1bGFyJTIwYmFja2VuZCUyMHNlY3VyZSUyMHVzZXIlMjBzeXN0ZW0lMjBhbmQlMjBwYXltZW50L2Zyb250ZW5kL3NyYy9hcHAvbW9kdWxlcy9wcm9maWxlL3Byb2ZpbGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFBcUIsaUJBQUE7RUFBbUIsbUJBQUE7RUFBcUIsYUFBQTtBQ0k3RDs7QURIQTtFQUFhLGdCQUFBO0VBQWtCLGNBQUE7QUNRL0I7O0FEUEE7RUFBSyxXQUFBO0VBQWEsbUJBQUE7QUNZbEI7O0FEWEE7RUFBVyxxQkFBQTtBQ2VYOztBRGJBO0VBQ0UsYUFBQTtFQUFlLFdBQUE7RUFBYSx1QkFBQTtBQ2tCOUI7QURqQkU7RUFDRSxXQUFBO0VBQWEsWUFBQTtFQUFjLGtCQUFBO0VBQW9CLGlCQUFBO0VBQW1CLHlCQUFBO0FDdUJ0RTtBRHJCRTtFQUNFLE9BQUE7QUN1Qko7QUR0Qkk7RUFBSSxnQkFBQTtBQ3lCUjtBRHpCMEI7RUFBUyxXQUFBO0VBQWEsb0JBQUE7QUM2QmhEOztBRHpCQTtFQUNFLGdCQUFBO0FDNEJGO0FEM0JFO0VBQ0Usb0JBQUE7RUFBc0IsbUJBQUE7RUFBcUIsV0FBQTtFQUFhLHdCQUFBO0VBQ3hELG1CQUFBO0VBQXFCLG1CQUFBO0VBQXFCLG1CQUFBO0FDa0M5QztBRGpDSTtFQUFJLGVBQUE7QUNvQ1I7O0FEaENBO0VBQ0UsZ0JBQUE7QUNtQ0Y7QURsQ0U7RUFBZ0IsYUFBQTtFQUFlLFdBQUE7RUFBYSxlQUFBO0VBQWlCLGtCQUFBO0FDd0MvRDtBRHZDRTtFQUNFLGFBQUE7RUFBZSxtQkFBQTtFQUFxQixZQUFBO0VBQWMsd0JBQUE7RUFDbEQsbUJBQUE7RUFBcUIsbUJBQUE7RUFBcUIsZ0JBQUE7QUM4QzlDO0FEN0NJO0VBQVcsZUFBQTtFQUFpQixXQUFBO0VBQWEsWUFBQTtBQ2tEN0M7QURqREk7RUFBYSxrQ0FBQTtFQUFvQyxjQUFBO0FDcURyRDtBRHBESTtFQUFlLGtDQUFBO0VBQW9DLGNBQUE7QUN3RHZEO0FEdkRJO0VBQVksbUNBQUE7RUFBcUMsY0FBQTtBQzJEckQ7QUQxREk7RUFBYSxvQ0FBQTtFQUFzQyxjQUFBO0FDOER2RDs7QUQxREE7RUFDRSxrQkFBQTtBQzZERjtBRDVERTtFQUNFLGFBQUE7RUFBZSw4QkFBQTtFQUFnQyxtQkFBQTtFQUFxQixtQkFBQTtBQ2lFeEU7QURoRUk7RUFBSyxTQUFBO0VBQVcsV0FBQTtBQ29FcEI7QURuRUk7RUFBb0IsYUFBQTtFQUFlLFdBQUE7QUN1RXZDO0FEdEVJO0VBQ0UsYUFBQTtFQUFlLG1CQUFBO0VBQXFCLFdBQUE7RUFBYSxjQUFBO0VBQWdCLG1CQUFBO0FDNEV2RTs7QUR2RUE7RUFDRSxtQkFBQTtFQUFxQixlQUFBO0VBQWlCLGtCQUFBO0VBQW9CLHlCQUFBO0FDNkU1RDtBRDVFRTtFQUFjLFdBQUE7RUFBYSxtQkFBQTtBQ2dGN0I7QUQvRUU7RUFBZ0IsYUFBQTtFQUFlLFNBQUE7RUFBVyxrQkFBQTtBQ29GNUM7QURwRmdFO0VBQVMsZ0JBQUE7QUN1RnpFOztBRG5GRTtFQUF3QixVQUFBO0VBQVksaUJBQUE7QUN3RnRDO0FEdkZFO0VBQWUsaUJBQUE7QUMwRmpCOztBRHZGQTtFQUNFLGdCQUFBO0FDMEZGO0FEekZFO0VBQVksYUFBQTtFQUFlLFNBQUE7QUM2RjdCO0FENUZFO0VBQWMsT0FBQTtBQytGaEI7QUQvRnlCO0VBQWUsV0FBQTtBQ2tHeEM7QURqR0U7RUFBZ0IsYUFBQTtFQUFlLFNBQUE7RUFBVyxnQkFBQTtBQ3NHNUM7QUR0RzhEO0VBQVMsZ0JBQUE7QUN5R3ZFOztBRHRHQTtFQUNFO0lBQXNELHNCQUFBO0lBQXdCLFlBQUE7RUMyRzlFO0VEMUdBO0lBQStCLHNCQUFBO0lBQXdCLE1BQUE7RUM4R3ZEO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIucHJvZmlsZS1jb250YWluZXIgeyBtaW4taGVpZ2h0OiAxMDB2aDsgYmFja2dyb3VuZDogI2Y1ZjVmNTsgcGFkZGluZzogMnJlbTsgfVxuLmNvbnRhaW5lciB7IG1heC13aWR0aDogODAwcHg7IG1hcmdpbjogMCBhdXRvOyB9XG5oMSB7IGNvbG9yOiAjMzMzOyBtYXJnaW4tYm90dG9tOiAycmVtOyB9XG5tYXQtY2FyZCB7IG1hcmdpbi1ib3R0b206IDEuNXJlbTsgfVxuXG4udXNlci1pbmZvIHtcbiAgZGlzcGxheTogZmxleDsgZ2FwOiAxLjVyZW07IGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAudXNlci1hdmF0YXIgaW1nIHtcbiAgICB3aWR0aDogODBweDsgaGVpZ2h0OiA4MHB4OyBib3JkZXItcmFkaXVzOiA1MCU7IG9iamVjdC1maXQ6IGNvdmVyOyBib3JkZXI6IDNweCBzb2xpZCAjZTBlMGUwO1xuICB9XG4gIC51c2VyLWRldGFpbHMge1xuICAgIGZsZXg6IDE7XG4gICAgcCB7IG1hcmdpbjogMC41cmVtIDA7IHN0cm9uZyB7IGNvbG9yOiAjMzMzOyBtYXJnaW4tcmlnaHQ6IDAuNXJlbTsgfSB9XG4gIH1cbn1cblxuLm9hdXRoLWluZm8ge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xuICAub2F1dGgtcHJvdmlkZXIge1xuICAgIGRpc3BsYXk6IGlubGluZS1mbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBnYXA6IDAuNXJlbTsgcGFkZGluZzogMC4yNXJlbSAwLjc1cmVtOyBcbiAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1OyBib3JkZXItcmFkaXVzOiAyMHB4OyBmb250LXNpemU6IDAuODc1cmVtO1xuICAgIGkgeyBmb250LXNpemU6IDFyZW07IH1cbiAgfVxufVxuXG4uYWNjb3VudC1zdGF0dXMge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xuICAuc3RhdHVzLWNoaXBzIHsgZGlzcGxheTogZmxleDsgZ2FwOiAwLjVyZW07IGZsZXgtd3JhcDogd3JhcDsgbWFyZ2luLXRvcDogMC41cmVtOyB9XG4gIC5zdGF0dXMtY2hpcCB7XG4gICAgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgZ2FwOiAwLjI1cmVtOyBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07IFxuICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7IGZvbnQtc2l6ZTogMC44NzVyZW07IGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgbWF0LWljb24geyBmb250LXNpemU6IDE2cHg7IHdpZHRoOiAxNnB4OyBoZWlnaHQ6IDE2cHg7IH1cbiAgICAmLnZlcmlmaWVkIHsgYmFja2dyb3VuZDogcmdiYSg3NiwgMTc1LCA4MCwgMC4xKTsgY29sb3I6ICM0Y2FmNTA7IH1cbiAgICAmLnVudmVyaWZpZWQgeyBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMTUyLCAwLCAwLjEpOyBjb2xvcjogI2ZmOTgwMDsgfVxuICAgICYuZW5hYmxlZCB7IGJhY2tncm91bmQ6IHJnYmEoMzMsIDE1MCwgMjQzLCAwLjEpOyBjb2xvcjogIzIxOTZmMzsgfVxuICAgICYuZGlzYWJsZWQgeyBiYWNrZ3JvdW5kOiByZ2JhKDE1OCwgMTU4LCAxNTgsIDAuMSk7IGNvbG9yOiAjOWU5ZTllOyB9XG4gIH1cbn1cblxuLnNlY3VyaXR5LXNlY3Rpb24ge1xuICBtYXJnaW4tdG9wOiAxLjVyZW07XG4gIC5zZWN0aW9uLWhlYWRlciB7XG4gICAgZGlzcGxheTogZmxleDsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyBhbGlnbi1pdGVtczogY2VudGVyOyBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgIGgzIHsgbWFyZ2luOiAwOyBjb2xvcjogIzMzMzsgfVxuICAgIC5wYXNzd29yZC1hY3Rpb25zIHsgZGlzcGxheTogZmxleDsgZ2FwOiAwLjVyZW07IH1cbiAgICAub2F1dGgtcGFzc3dvcmQtbm90aWNlIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGdhcDogMC41cmVtOyBjb2xvcjogI2ZmOTgwMDsgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICB9XG4gIH1cbn1cblxuLmNoYW5nZS1wYXNzd29yZC1mb3JtIHtcbiAgYmFja2dyb3VuZDogI2Y5ZjlmOTsgcGFkZGluZzogMS41cmVtOyBib3JkZXItcmFkaXVzOiA4cHg7IGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XG4gIC5mb3JtLWZpZWxkIHsgd2lkdGg6IDEwMCU7IG1hcmdpbi1ib3R0b206IDFyZW07IH1cbiAgLmZvcm0tYWN0aW9ucyB7IGRpc3BsYXk6IGZsZXg7IGdhcDogMXJlbTsgbWFyZ2luLXRvcDogMS41cmVtOyBidXR0b24geyBtaW4td2lkdGg6IDEyMHB4OyB9IH1cbn1cblxuLnByb2ZpbGUtdGFicyB7XG4gIC5tYXQtdGFiLWJvZHktY29udGVudCB7IHBhZGRpbmc6IDA7IG92ZXJmbG93OiB2aXNpYmxlOyB9XG4gIC50YWItY29udGVudCB7IHBhZGRpbmc6IDEuNXJlbSAwOyB9XG59XG5cbi5lZGl0LXByb2ZpbGUtY2FyZCB7XG4gIG1hcmdpbi10b3A6IDFyZW07XG4gIC5mb3JtLXJvdyB7IGRpc3BsYXk6IGZsZXg7IGdhcDogMXJlbTsgfVxuICAuZm9ybS1maWVsZCB7IGZsZXg6IDE7ICYuZnVsbC13aWR0aCB7IHdpZHRoOiAxMDAlOyB9IH1cbiAgLmZvcm0tYWN0aW9ucyB7IGRpc3BsYXk6IGZsZXg7IGdhcDogMXJlbTsgbWFyZ2luLXRvcDogMXJlbTsgYnV0dG9uIHsgbWluLXdpZHRoOiAxMjBweDsgfSB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuc2VjdXJpdHktc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLnBhc3N3b3JkLWFjdGlvbnMgeyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyBnYXA6IDAuMjVyZW07IH1cbiAgLmVkaXQtcHJvZmlsZS1jYXJkIC5mb3JtLXJvdyB7IGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47IGdhcDogMDsgfVxufVxuIiwiLnByb2ZpbGUtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIGJhY2tncm91bmQ6ICNmNWY1ZjU7XG4gIHBhZGRpbmc6IDJyZW07XG59XG5cbi5jb250YWluZXIge1xuICBtYXgtd2lkdGg6IDgwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbn1cblxuaDEge1xuICBjb2xvcjogIzMzMztcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbn1cblxubWF0LWNhcmQge1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG59XG5cbi51c2VyLWluZm8ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDEuNXJlbTtcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG59XG4udXNlci1pbmZvIC51c2VyLWF2YXRhciBpbWcge1xuICB3aWR0aDogODBweDtcbiAgaGVpZ2h0OiA4MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICBib3JkZXI6IDNweCBzb2xpZCAjZTBlMGUwO1xufVxuLnVzZXItaW5mbyAudXNlci1kZXRhaWxzIHtcbiAgZmxleDogMTtcbn1cbi51c2VyLWluZm8gLnVzZXItZGV0YWlscyBwIHtcbiAgbWFyZ2luOiAwLjVyZW0gMDtcbn1cbi51c2VyLWluZm8gLnVzZXItZGV0YWlscyBwIHN0cm9uZyB7XG4gIGNvbG9yOiAjMzMzO1xuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcbn1cblxuLm9hdXRoLWluZm8ge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xufVxuLm9hdXRoLWluZm8gLm9hdXRoLXByb3ZpZGVyIHtcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07XG4gIGJhY2tncm91bmQ6ICNmNWY1ZjU7XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG59XG4ub2F1dGgtaW5mbyAub2F1dGgtcHJvdmlkZXIgaSB7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbn1cblxuLmFjY291bnQtc3RhdHVzIHtcbiAgbWFyZ2luLXRvcDogMXJlbTtcbn1cbi5hY2NvdW50LXN0YXR1cyAuc3RhdHVzLWNoaXBzIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAwLjVyZW07XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgbWFyZ2luLXRvcDogMC41cmVtO1xufVxuLmFjY291bnQtc3RhdHVzIC5zdGF0dXMtY2hpcCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC4yNXJlbTtcbiAgcGFkZGluZzogMC4yNXJlbSAwLjc1cmVtO1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuLmFjY291bnQtc3RhdHVzIC5zdGF0dXMtY2hpcCBtYXQtaWNvbiB7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgd2lkdGg6IDE2cHg7XG4gIGhlaWdodDogMTZweDtcbn1cbi5hY2NvdW50LXN0YXR1cyAuc3RhdHVzLWNoaXAudmVyaWZpZWQge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDc2LCAxNzUsIDgwLCAwLjEpO1xuICBjb2xvcjogIzRjYWY1MDtcbn1cbi5hY2NvdW50LXN0YXR1cyAuc3RhdHVzLWNoaXAudW52ZXJpZmllZCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAxNTIsIDAsIDAuMSk7XG4gIGNvbG9yOiAjZmY5ODAwO1xufVxuLmFjY291bnQtc3RhdHVzIC5zdGF0dXMtY2hpcC5lbmFibGVkIHtcbiAgYmFja2dyb3VuZDogcmdiYSgzMywgMTUwLCAyNDMsIDAuMSk7XG4gIGNvbG9yOiAjMjE5NmYzO1xufVxuLmFjY291bnQtc3RhdHVzIC5zdGF0dXMtY2hpcC5kaXNhYmxlZCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMTU4LCAxNTgsIDE1OCwgMC4xKTtcbiAgY29sb3I6ICM5ZTllOWU7XG59XG5cbi5zZWN1cml0eS1zZWN0aW9uIHtcbiAgbWFyZ2luLXRvcDogMS41cmVtO1xufVxuLnNlY3VyaXR5LXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xufVxuLnNlY3VyaXR5LXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIGgzIHtcbiAgbWFyZ2luOiAwO1xuICBjb2xvcjogIzMzMztcbn1cbi5zZWN1cml0eS1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAucGFzc3dvcmQtYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMC41cmVtO1xufVxuLnNlY3VyaXR5LXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIC5vYXV0aC1wYXNzd29yZC1ub3RpY2Uge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDAuNXJlbTtcbiAgY29sb3I6ICNmZjk4MDA7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG59XG5cbi5jaGFuZ2UtcGFzc3dvcmQtZm9ybSB7XG4gIGJhY2tncm91bmQ6ICNmOWY5Zjk7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xufVxuLmNoYW5nZS1wYXNzd29yZC1mb3JtIC5mb3JtLWZpZWxkIHtcbiAgd2lkdGg6IDEwMCU7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG4uY2hhbmdlLXBhc3N3b3JkLWZvcm0gLmZvcm0tYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMXJlbTtcbiAgbWFyZ2luLXRvcDogMS41cmVtO1xufVxuLmNoYW5nZS1wYXNzd29yZC1mb3JtIC5mb3JtLWFjdGlvbnMgYnV0dG9uIHtcbiAgbWluLXdpZHRoOiAxMjBweDtcbn1cblxuLnByb2ZpbGUtdGFicyAubWF0LXRhYi1ib2R5LWNvbnRlbnQge1xuICBwYWRkaW5nOiAwO1xuICBvdmVyZmxvdzogdmlzaWJsZTtcbn1cbi5wcm9maWxlLXRhYnMgLnRhYi1jb250ZW50IHtcbiAgcGFkZGluZzogMS41cmVtIDA7XG59XG5cbi5lZGl0LXByb2ZpbGUtY2FyZCB7XG4gIG1hcmdpbi10b3A6IDFyZW07XG59XG4uZWRpdC1wcm9maWxlLWNhcmQgLmZvcm0tcm93IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxcmVtO1xufVxuLmVkaXQtcHJvZmlsZS1jYXJkIC5mb3JtLWZpZWxkIHtcbiAgZmxleDogMTtcbn1cbi5lZGl0LXByb2ZpbGUtY2FyZCAuZm9ybS1maWVsZC5mdWxsLXdpZHRoIHtcbiAgd2lkdGg6IDEwMCU7XG59XG4uZWRpdC1wcm9maWxlLWNhcmQgLmZvcm0tYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMXJlbTtcbiAgbWFyZ2luLXRvcDogMXJlbTtcbn1cbi5lZGl0LXByb2ZpbGUtY2FyZCAuZm9ybS1hY3Rpb25zIGJ1dHRvbiB7XG4gIG1pbi13aWR0aDogMTIwcHg7XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuc2VjdXJpdHktc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLnBhc3N3b3JkLWFjdGlvbnMge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAwLjI1cmVtO1xuICB9XG4gIC5lZGl0LXByb2ZpbGUtY2FyZCAuZm9ybS1yb3cge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAwO1xuICB9XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "currentUser", "avatarUrl", "ɵɵsanitizeUrl", "firstName", "lastName", "ɵɵtext", "ɵɵclassMap", "getOAuthProviderIcon", "ɵɵstyleProp", "getOAuthProviderColor", "ɵɵtextInterpolate1", "getOAuthProviderName", "ɵɵlistener", "ProfileComponent_button_46_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "enableEditMode", "ɵɵtemplate", "ProfileComponent_mat_card_47_mat_error_21_span_1_Template", "ProfileComponent_mat_card_47_mat_error_21_span_2_Template", "tmp_2_0", "editProfileForm", "get", "errors", "tmp_3_0", "ProfileComponent_mat_card_47_Template_form_ngSubmit_5_listener", "_r3", "saveProfile", "ProfileComponent_mat_card_47_mat_error_11_Template", "ProfileComponent_mat_card_47_mat_error_16_Template", "ProfileComponent_mat_card_47_mat_error_21_Template", "ProfileComponent_mat_card_47_mat_spinner_28_Template", "ProfileComponent_mat_card_47_span_29_Template", "ProfileComponent_mat_card_47_Template_button_click_30_listener", "cancelEdit", "invalid", "touched", "tmp_4_0", "loading", "ProfileComponent_div_69_mat_form_field_2_Template_button_click_4_listener", "_r5", "hideCurrentPassword", "ɵɵtextInterpolate", "getFieldError", "ProfileComponent_div_69_Template_form_ngSubmit_1_listener", "_r4", "onChangePassword", "ProfileComponent_div_69_mat_form_field_2_Template", "ProfileComponent_div_69_div_3_Template", "ProfileComponent_div_69_Template_button_click_8_listener", "hideNewPassword", "ProfileComponent_div_69_Template_button_click_17_listener", "hideConfirmPassword", "ProfileComponent_div_69_mat_form_field_22_Template", "ProfileComponent_div_69_mat_spinner_25_Template", "ProfileComponent_div_69_span_26_Template", "ProfileComponent_div_69_Template_button_click_27_listener", "toggleChangePassword", "changePasswordForm", "isOAuthUser", "twoFactorStatus", "enabled", "ProfileComponent", "constructor", "authService", "twoFactorService", "oauthService", "formBuilder", "snackBar", "editMode", "showChangePassword", "group", "currentPassword", "required", "newPassword", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "twoFactorToken", "validators", "passwordMatchValidator", "email", "phone", "ngOnInit", "currentUserValue", "initializeEditForm", "load2FAStatus", "patchValue", "markFormGroupTouched", "profileData", "value", "updateProfile", "subscribe", "next", "response", "open", "duration", "error", "message", "reset", "formValue", "changePassword", "undefined", "onForgotPassword", "forgotPassword", "fieldName", "field", "<PERSON><PERSON><PERSON><PERSON>", "form", "setErrors", "passwordMismatch", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "get2FAStatus", "status", "console", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "TwoFactorService", "i3", "OAuthService", "i4", "FormBuilder", "i5", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_13_Template", "ProfileComponent_div_32_Template", "ProfileComponent_button_46_Template", "ProfileComponent_mat_card_47_Template", "ProfileComponent_Template_button_click_60_listener", "ProfileComponent_Template_button_click_64_listener", "ProfileComponent_div_68_Template", "ProfileComponent_div_69_Template", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "createdAt", "ɵɵclassProp", "emailVerified"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../services/auth.service';\nimport { TwoFactorService } from '../../services/two-factor.service';\nimport { OAuthService } from '../../services/oauth.service';\nimport { User, ChangePasswordRequest } from '../../models/user.model';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss'],\n  standalone: false\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: User | null = null;\n  changePasswordForm: FormGroup;\n  editProfileForm: FormGroup;\n  twoFactorStatus = { enabled: false };\n\n  loading = false;\n  editMode = false;\n  hideCurrentPassword = true;\n  hideNewPassword = true;\n  hideConfirmPassword = true;\n  showChangePassword = false;\n\n  constructor(\n    private authService: AuthService,\n    private twoFactorService: TwoFactorService,\n    private oauthService: OAuthService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {\n    this.changePasswordForm = this.formBuilder.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      twoFactorToken: ['']\n    }, { validators: this.passwordMatchValidator });\n\n    this.editProfileForm = this.formBuilder.group({\n      firstName: ['', [Validators.required]],\n      lastName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.currentUserValue;\n    this.initializeEditForm();\n    this.load2FAStatus();\n  }\n\n  initializeEditForm(): void {\n    if (this.currentUser) {\n      this.editProfileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        email: this.currentUser.email || '',\n        phone: this.currentUser.phone || ''\n      });\n    }\n  }\n\n  enableEditMode(): void {\n    this.editMode = true;\n    this.initializeEditForm();\n  }\n\n  cancelEdit(): void {\n    this.editMode = false;\n    this.initializeEditForm();\n  }\n\n  saveProfile(): void {\n    if (this.editProfileForm.invalid) {\n      this.markFormGroupTouched(this.editProfileForm);\n      return;\n    }\n\n    this.loading = true;\n    const profileData = this.editProfileForm.value;\n\n    this.authService.updateProfile(profileData).subscribe({\n      next: (response) => {\n        this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\n        this.editMode = false;\n        this.loading = false;\n        \n        // Update current user data\n        if (this.currentUser) {\n          this.currentUser = { ...this.currentUser, ...profileData };\n        }\n      },\n      error: (error) => {\n        this.snackBar.open(\n          error.error?.message || 'Failed to update profile',\n          'Close',\n          { duration: 5000 }\n        );\n        this.loading = false;\n      }\n    });\n  }\n\n  toggleChangePassword(): void {\n    this.showChangePassword = !this.showChangePassword;\n    if (!this.showChangePassword) {\n      this.changePasswordForm.reset();\n    }\n  }\n\n  onChangePassword(): void {\n    if (this.changePasswordForm.invalid) {\n      this.markFormGroupTouched(this.changePasswordForm);\n      return;\n    }\n\n    this.loading = true;\n    const formValue = this.changePasswordForm.value;\n\n    this.authService.changePassword(\n      formValue.currentPassword,\n      formValue.newPassword,\n      formValue.twoFactorToken || undefined\n    ).subscribe({\n      next: (response) => {\n        this.snackBar.open('Password changed successfully!', 'Close', { duration: 3000 });\n        this.changePasswordForm.reset();\n        this.showChangePassword = false;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Failed to change password', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  onForgotPassword(): void {\n    if (!this.currentUser?.email) {\n      this.snackBar.open('No email address found', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.loading = true;\n    this.authService.forgotPassword(this.currentUser.email).subscribe({\n      next: (response) => {\n        this.snackBar.open(\n          'Password reset instructions have been sent to your email',\n          'Close',\n          { duration: 5000 }\n        );\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(\n          error.error?.message || 'Failed to send password reset email',\n          'Close',\n          { duration: 5000 }\n        );\n        this.loading = false;\n      }\n    });\n  }\n\n  isOAuthUser(): boolean {\n    return this.oauthService.isOAuthUser(this.currentUser);\n  }\n\n  getOAuthProviderName(): string {\n    return this.oauthService.getOAuthProviderName(this.currentUser);\n  }\n\n  getOAuthProviderIcon(): string {\n    return this.oauthService.getOAuthProviderIcon(this.currentUser);\n  }\n\n  getOAuthProviderColor(): string {\n    return this.oauthService.getOAuthProviderColor(this.currentUser);\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.changePasswordForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n    }\n    return '';\n  }\n\n  private passwordMatchValidator(form: FormGroup) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n\n    return null;\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  load2FAStatus(): void {\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: (status) => {\n        this.twoFactorStatus = status;\n      },\n      error: (error) => {\n        console.error('Failed to load 2FA status:', error);\n      }\n    });\n  }\n}\n", "<div class=\"profile-container\">\n  <div class=\"container\">\n    <h1>Profile & Security Settings</h1>\n    \n    <mat-tab-group animationDuration=\"0ms\" class=\"profile-tabs\">\n      <!-- Profile Information Tab -->\n      <mat-tab label=\"Profile Information\">\n        <div class=\"tab-content\">\n          <mat-card>\n            <mat-card-header>\n              <mat-card-title>User Information</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"user-info\">\n                <div class=\"user-avatar\" *ngIf=\"currentUser?.avatarUrl\">\n                  <img [src]=\"currentUser!.avatarUrl\" [alt]=\"(currentUser!.firstName || '') + ' ' + (currentUser!.lastName || '')\">\n                </div>\n                <div class=\"user-details\">\n                  <p><strong>Name:</strong> {{ currentUser?.firstName }} {{ currentUser?.lastName }}</p>\n                  <p><strong>Email:</strong> {{ currentUser?.email }}</p>\n                  <p><strong>Phone:</strong> {{ currentUser?.phone || 'Not provided' }}</p>\n                  <p><strong>Member since:</strong> {{ currentUser?.createdAt | date:'mediumDate' }}</p>\n\n                  <!-- OAuth Provider Info -->\n                  <div *ngIf=\"isOAuthUser()\" class=\"oauth-info\">\n                    <p><strong>Connected via:</strong>\n                      <span class=\"oauth-provider\">\n                        <i [class]=\"getOAuthProviderIcon()\" [style.color]=\"getOAuthProviderColor()\"></i>\n                        {{ getOAuthProviderName() }}\n                      </span>\n                    </p>\n                  </div>\n\n                  <!-- Account Status -->\n                  <div class=\"account-status\">\n                    <div class=\"status-chips\">\n                      <div class=\"status-chip\" [class.verified]=\"currentUser?.emailVerified\" [class.unverified]=\"!currentUser?.emailVerified\">\n                        <mat-icon>{{ currentUser?.emailVerified ? 'verified' : 'warning' }}</mat-icon>\n                        <span>Email {{ currentUser?.emailVerified ? 'Verified' : 'Unverified' }}</span>\n                      </div>\n                      <div class=\"status-chip\" [class.enabled]=\"twoFactorStatus.enabled\" [class.disabled]=\"!twoFactorStatus.enabled\">\n                        <mat-icon>security</mat-icon>\n                        <span>2FA {{ twoFactorStatus.enabled ? 'Enabled' : 'Disabled' }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n            <mat-card-actions>\n              <button mat-button color=\"primary\" (click)=\"enableEditMode()\" *ngIf=\"!editMode\">\n                <mat-icon>edit</mat-icon>\n                Edit Profile\n              </button>\n            </mat-card-actions>\n          </mat-card>\n\n          <!-- Edit Profile Form -->\n          <mat-card *ngIf=\"editMode\" class=\"edit-profile-card\">\n            <mat-card-header>\n              <mat-card-title>Edit Profile</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <form [formGroup]=\"editProfileForm\" (ngSubmit)=\"saveProfile()\">\n                <div class=\"form-row\">\n                  <mat-form-field class=\"form-field\" appearance=\"outline\">\n                    <mat-label>First Name</mat-label>\n                    <input matInput formControlName=\"firstName\" autocomplete=\"given-name\">\n                    <mat-error *ngIf=\"editProfileForm.get('firstName')?.invalid && editProfileForm.get('firstName')?.touched\">\n                      First name is required\n                    </mat-error>\n                  </mat-form-field>\n\n                  <mat-form-field class=\"form-field\" appearance=\"outline\">\n                    <mat-label>Last Name</mat-label>\n                    <input matInput formControlName=\"lastName\" autocomplete=\"family-name\">\n                    <mat-error *ngIf=\"editProfileForm.get('lastName')?.invalid && editProfileForm.get('lastName')?.touched\">\n                      Last name is required\n                    </mat-error>\n                  </mat-form-field>\n                </div>\n\n                <mat-form-field class=\"form-field full-width\" appearance=\"outline\">\n                  <mat-label>Email</mat-label>\n                  <input matInput formControlName=\"email\" type=\"email\" autocomplete=\"email\">\n                  <mat-error *ngIf=\"editProfileForm.get('email')?.invalid && editProfileForm.get('email')?.touched\">\n                    <span *ngIf=\"editProfileForm.get('email')?.errors?.['required']\">Email is required</span>\n                    <span *ngIf=\"editProfileForm.get('email')?.errors?.['email']\">Please enter a valid email</span>\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field class=\"form-field full-width\" appearance=\"outline\">\n                  <mat-label>Phone (Optional)</mat-label>\n                  <input matInput formControlName=\"phone\" type=\"tel\" autocomplete=\"tel\">\n                </mat-form-field>\n\n                <div class=\"form-actions\">\n                  <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading || editProfileForm.invalid\">\n                    <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n                    <span *ngIf=\"!loading\">Save Changes</span>\n                  </button>\n                  <button mat-button type=\"button\" (click)=\"cancelEdit()\">\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </mat-tab>\n\n      <!-- Security Settings Tab -->\n      <mat-tab label=\"Security\">\n        <div class=\"tab-content\">\n          <mat-card>\n            <mat-card-header>\n              <mat-card-title>Password Settings</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <!-- Change Password Section -->\n              <div class=\"security-section\">\n                <div class=\"section-header\">\n                  <h3>Password</h3>\n                  <div class=\"password-actions\">\n                    <button mat-button color=\"primary\" (click)=\"toggleChangePassword()\">\n                      <mat-icon>{{ showChangePassword ? 'expand_less' : 'expand_more' }}</mat-icon>\n                      {{ showChangePassword ? 'Cancel' : 'Change Password' }}\n                    </button>\n                    <button mat-button color=\"accent\" (click)=\"onForgotPassword()\" [disabled]=\"loading\">\n                      <mat-icon>help</mat-icon>\n                      Forgot Password\n                    </button>\n                  </div>\n                  <div *ngIf=\"isOAuthUser()\" class=\"oauth-password-notice\">\n                    <mat-icon color=\"primary\">info</mat-icon>\n                    <span>Connected via {{ getOAuthProviderName() }} • You can also set a password for direct login</span>\n                  </div>\n                </div>\n\n                <!-- Change Password Form -->\n                <div *ngIf=\"showChangePassword\" class=\"change-password-form\">\n                  <form [formGroup]=\"changePasswordForm\" (ngSubmit)=\"onChangePassword()\">\n                    <mat-form-field class=\"form-field\" appearance=\"outline\" *ngIf=\"!isOAuthUser()\">\n                      <mat-label>Current Password</mat-label>\n                      <input matInput [type]=\"hideCurrentPassword ? 'password' : 'text'\"\n                             formControlName=\"currentPassword\" autocomplete=\"current-password\">\n                      <button mat-icon-button matSuffix (click)=\"hideCurrentPassword = !hideCurrentPassword\" type=\"button\">\n                        <mat-icon>{{ hideCurrentPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                      </button>\n                      <mat-error>{{ getFieldError('currentPassword') }}</mat-error>\n                    </mat-form-field>\n\n                    <!-- OAuth users get a helpful message about first-time password setup -->\n                    <div *ngIf=\"isOAuthUser()\" class=\"oauth-password-setup-info\">\n                      <mat-icon color=\"primary\">lock</mat-icon>\n                      <p>Set up a password to enable direct login with your email address.</p>\n                      <p><small>You can continue using {{ getOAuthProviderName() }} login or use your new password.</small></p>\n                    </div>\n\n                    <mat-form-field class=\"form-field\" appearance=\"outline\">\n                      <mat-label>New Password</mat-label>\n                      <input matInput [type]=\"hideNewPassword ? 'password' : 'text'\"\n                             formControlName=\"newPassword\" autocomplete=\"new-password\">\n                      <button mat-icon-button matSuffix (click)=\"hideNewPassword = !hideNewPassword\" type=\"button\">\n                        <mat-icon>{{ hideNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                      </button>\n                      <mat-error>{{ getFieldError('newPassword') }}</mat-error>\n                    </mat-form-field>\n\n                    <mat-form-field class=\"form-field\" appearance=\"outline\">\n                      <mat-label>Confirm New Password</mat-label>\n                      <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                             formControlName=\"confirmPassword\" autocomplete=\"new-password\">\n                      <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n                        <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                      </button>\n                      <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n                    </mat-form-field>\n\n                    <!-- 2FA Token Field (if 2FA is enabled) -->\n                    <mat-form-field *ngIf=\"twoFactorStatus.enabled\" class=\"form-field\" appearance=\"outline\">\n                      <mat-label>2FA Code (Required)</mat-label>\n                      <input matInput formControlName=\"twoFactorToken\" placeholder=\"000000\"\n                             maxlength=\"6\" autocomplete=\"one-time-code\">\n                      <mat-icon matSuffix>verified_user</mat-icon>\n                      <mat-hint>Enter the 6-digit code from your authenticator app</mat-hint>\n                      <mat-error>{{ getFieldError('twoFactorToken') }}</mat-error>\n                    </mat-form-field>\n\n                    <div class=\"form-actions\">\n                      <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading\">\n                        <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n                        <span *ngIf=\"!loading\">Change Password</span>\n                      </button>\n                      <button mat-button type=\"button\" (click)=\"toggleChangePassword()\">\n                        Cancel\n                      </button>\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </mat-tab>\n\n      <!-- Two-Factor Authentication Tab -->\n      <mat-tab label=\"Two-Factor Authentication\">\n        <div class=\"tab-content\">\n          <app-two-factor-management></app-two-factor-management>\n        </div>\n      </mat-tab>\n    </mat-tab-group>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;ICanDC,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,cAAiH;IACnHF,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAI,SAAA,EAA8B;IAACJ,EAA/B,CAAAK,UAAA,QAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,EAAAR,EAAA,CAAAS,aAAA,CAA8B,SAAAH,MAAA,CAAAC,WAAA,CAAAG,SAAA,iBAAAJ,MAAA,CAAAC,WAAA,CAAAI,QAAA,QAA6E;;;;;IAU3GX,EADL,CAAAC,cAAA,cAA8C,QACzC,aAAQ;IAAAD,EAAA,CAAAY,MAAA,qBAAc;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,SAAA,QAAgF;IAChFF,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAG,YAAA,EAAO,EACL,EACA;;;;IAJGH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,CAAAP,MAAA,CAAAQ,oBAAA,GAAgC;IAACd,EAAA,CAAAe,WAAA,UAAAT,MAAA,CAAAU,qBAAA,GAAuC;IAC3EhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAX,MAAA,CAAAY,oBAAA,QACF;;;;;;IAqBRlB,EAAA,CAAAC,cAAA,iBAAgF;IAA7CD,EAAA,CAAAmB,UAAA,mBAAAC,4DAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAmB,cAAA,EAAgB;IAAA,EAAC;IAC3DzB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAY,MAAA,qBACF;IAAAZ,EAAA,CAAAG,YAAA,EAAS;;;;;IAeHH,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAY,MAAA,+BACF;IAAAZ,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAwG;IACtGD,EAAA,CAAAY,MAAA,8BACF;IAAAZ,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAY,MAAA,wBAAiB;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzFH,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAY,MAAA,iCAA0B;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IAFjGH,EAAA,CAAAC,cAAA,gBAAkG;IAEhGD,EADA,CAAA0B,UAAA,IAAAC,yDAAA,mBAAiE,IAAAC,yDAAA,mBACH;IAChE5B,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAK,UAAA,UAAAwB,OAAA,GAAAvB,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;IACxDhC,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAK,UAAA,UAAA4B,OAAA,GAAA3B,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAqD;;;;;IAW5DhC,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAvChDH,EAFJ,CAAAC,cAAA,mBAAqD,sBAClC,qBACC;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAC9BZ,EAD8B,CAAAG,YAAA,EAAiB,EAC7B;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,eAC+C;IAA3BD,EAAA,CAAAmB,UAAA,sBAAAe,+DAAA;MAAAlC,EAAA,CAAAqB,aAAA,CAAAc,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAYlB,MAAA,CAAA8B,WAAA,EAAa;IAAA,EAAC;IAGxDpC,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,gBAC3C;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAE,SAAA,iBAAsE;IACtEF,EAAA,CAAA0B,UAAA,KAAAW,kDAAA,wBAA0G;IAG5GrC,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAY,MAAA,iBAAS;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAE,SAAA,iBAAsE;IACtEF,EAAA,CAAA0B,UAAA,KAAAY,kDAAA,wBAAwG;IAI5GtC,EADE,CAAAG,YAAA,EAAiB,EACb;IAGJH,EADF,CAAAC,cAAA,0BAAmE,iBACtD;IAAAD,EAAA,CAAAY,MAAA,aAAK;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAE,SAAA,iBAA0E;IAC1EF,EAAA,CAAA0B,UAAA,KAAAa,kDAAA,wBAAkG;IAIpGvC,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAmE,iBACtD;IAAAD,EAAA,CAAAY,MAAA,wBAAgB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,iBAAsE;IACxEF,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,eAA0B,kBACgF;IAEtGD,EADA,CAAA0B,UAAA,KAAAc,oDAAA,0BAA2C,KAAAC,6CAAA,mBACpB;IACzBzC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwD;IAAvBD,EAAA,CAAAmB,UAAA,mBAAAuB,+DAAA;MAAA1C,EAAA,CAAAqB,aAAA,CAAAc,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAqC,UAAA,EAAY;IAAA,EAAC;IACrD3C,EAAA,CAAAY,MAAA,gBACF;IAIRZ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV;;;;;;;IA5CDH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,UAAA,cAAAC,MAAA,CAAAwB,eAAA,CAA6B;IAKjB9B,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAK,UAAA,WAAAwB,OAAA,GAAAvB,MAAA,CAAAwB,eAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAe,OAAA,OAAAf,OAAA,GAAAvB,MAAA,CAAAwB,eAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAgB,OAAA,EAA4F;IAQ5F7C,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAK,UAAA,WAAA4B,OAAA,GAAA3B,MAAA,CAAAwB,eAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAW,OAAA,OAAAX,OAAA,GAAA3B,MAAA,CAAAwB,eAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAY,OAAA,EAA0F;IAS5F7C,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAK,UAAA,WAAAyC,OAAA,GAAAxC,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAe,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAxC,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAe,OAAA,CAAAD,OAAA,EAAoF;IAYxC7C,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAAyC,OAAA,IAAAzC,MAAA,CAAAwB,eAAA,CAAAc,OAAA,CAA+C;IACvF5C,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAyC,OAAA,CAAa;IACpB/C,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAyC,OAAA,CAAc;;;;;IAmCrB/C,EADF,CAAAC,cAAA,cAAyD,mBAC7B;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAyF;IACjGZ,EADiG,CAAAG,YAAA,EAAO,EAClG;;;;IADEH,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAiB,kBAAA,mBAAAX,MAAA,CAAAY,oBAAA,2DAAyF;;;;;;IAQ7FlB,EADF,CAAAC,cAAA,yBAA+E,gBAClE;IAAAD,EAAA,CAAAY,MAAA,uBAAgB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,gBACyE;IACzEF,EAAA,CAAAC,cAAA,iBAAqG;IAAnED,EAAA,CAAAmB,UAAA,mBAAA6B,0EAAA;MAAAhD,EAAA,CAAAqB,aAAA,CAAA4B,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAA4C,mBAAA,IAAA5C,MAAA,CAAA4C,mBAAA;IAAA,EAAoD;IACpFlD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,GAA2D;IACvEZ,EADuE,CAAAG,YAAA,EAAW,EACzE;IACTH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAY,MAAA,GAAsC;IACnDZ,EADmD,CAAAG,YAAA,EAAY,EAC9C;;;;IANCH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA4C,mBAAA,uBAAkD;IAGtDlD,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA4C,mBAAA,mCAA2D;IAE5DlD,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,oBAAsC;;;;;IAKjDpD,EADF,CAAAC,cAAA,cAA6D,mBACjC;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,wEAAiE;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACrEH,EAAH,CAAAC,cAAA,QAAG,YAAO;IAAAD,EAAA,CAAAY,MAAA,GAAmF;IAC/FZ,EAD+F,CAAAG,YAAA,EAAQ,EAAI,EACrG;;;;IADMH,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAiB,kBAAA,4BAAAX,MAAA,CAAAY,oBAAA,uCAAmF;;;;;IAyB7FlB,EADF,CAAAC,cAAA,yBAAwF,gBAC3E;IAAAD,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAE,SAAA,gBACkD;IAClDF,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAY,MAAA,oBAAa;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAC5CH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,yDAAkD;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACvEH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAY,MAAA,GAAqC;IAClDZ,EADkD,CAAAG,YAAA,EAAY,EAC7C;;;;IADJH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,mBAAqC;;;;;IAK9CpD,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAY,MAAA,sBAAe;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAnDnDH,EADF,CAAAC,cAAA,cAA6D,eACY;IAAhCD,EAAA,CAAAmB,UAAA,sBAAAkC,0DAAA;MAAArD,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAYlB,MAAA,CAAAiD,gBAAA,EAAkB;IAAA,EAAC;IAYpEvD,EAXA,CAAA0B,UAAA,IAAA8B,iDAAA,6BAA+E,IAAAC,sCAAA,kBAWlB;IAO3DzD,EADF,CAAAC,cAAA,yBAAwD,gBAC3C;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAE,SAAA,gBACiE;IACjEF,EAAA,CAAAC,cAAA,iBAA6F;IAA3DD,EAAA,CAAAmB,UAAA,mBAAAuC,yDAAA;MAAA1D,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAAqD,eAAA,IAAArD,MAAA,CAAAqD,eAAA;IAAA,EAA4C;IAC5E3D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,IAAuD;IACnEZ,EADmE,CAAAG,YAAA,EAAW,EACrE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAkC;IAC/CZ,EAD+C,CAAAG,YAAA,EAAY,EAC1C;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAY,MAAA,4BAAoB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAE,SAAA,iBACqE;IACrEF,EAAA,CAAAC,cAAA,kBAAqG;IAAnED,EAAA,CAAAmB,UAAA,mBAAAyC,0DAAA;MAAA5D,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAAuD,mBAAA,IAAAvD,MAAA,CAAAuD,mBAAA;IAAA,EAAoD;IACpF7D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAY,MAAA,IAA2D;IACvEZ,EADuE,CAAAG,YAAA,EAAW,EACzE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAsC;IACnDZ,EADmD,CAAAG,YAAA,EAAY,EAC9C;IAGjBH,EAAA,CAAA0B,UAAA,KAAAoC,kDAAA,8BAAwF;IAUtF9D,EADF,CAAAC,cAAA,eAA0B,kBACqD;IAE3ED,EADA,CAAA0B,UAAA,KAAAqC,+CAAA,0BAA2C,KAAAC,wCAAA,mBACpB;IACzBhE,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAkE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAA8C,0DAAA;MAAAjE,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA4D,oBAAA,EAAsB;IAAA,EAAC;IAC/DlE,EAAA,CAAAY,MAAA,gBACF;IAGNZ,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACH;;;;IA1DEH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,UAAA,cAAAC,MAAA,CAAA6D,kBAAA,CAAgC;IACqBnE,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAA8D,WAAA,GAAoB;IAWvEpE,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA8D,WAAA,GAAmB;IAQPpE,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAqD,eAAA,uBAA8C;IAGlD3D,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAAqD,eAAA,mCAAuD;IAExD3D,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,gBAAkC;IAK7BpD,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAuD,mBAAA,uBAAkD;IAGtD7D,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAAuD,mBAAA,mCAA2D;IAE5D7D,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,oBAAsC;IAIlCpD,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA+D,eAAA,CAAAC,OAAA,CAA6B;IAUYtE,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAAyC,OAAA,CAAoB;IAC5D/C,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAyC,OAAA,CAAa;IACpB/C,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAyC,OAAA,CAAc;;;ADlL7C,OAAM,MAAOwB,gBAAgB;EAa3BC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,YAA0B,EAC1BC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAjBlB,KAAAtE,WAAW,GAAgB,IAAI;IAG/B,KAAA8D,eAAe,GAAG;MAAEC,OAAO,EAAE;IAAK,CAAE;IAEpC,KAAAvB,OAAO,GAAG,KAAK;IACf,KAAA+B,QAAQ,GAAG,KAAK;IAChB,KAAA5B,mBAAmB,GAAG,IAAI;IAC1B,KAAAS,eAAe,GAAG,IAAI;IACtB,KAAAE,mBAAmB,GAAG,IAAI;IAC1B,KAAAkB,kBAAkB,GAAG,KAAK;IASxB,IAAI,CAACZ,kBAAkB,GAAG,IAAI,CAACS,WAAW,CAACI,KAAK,CAAC;MAC/CC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAClF,UAAU,CAACmF,QAAQ,CAAC,CAAC;MAC5CC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACpF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACqF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACjEC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACtF,UAAU,CAACmF,QAAQ,CAAC,CAAC;MAC5CI,cAAc,EAAE,CAAC,EAAE;KACpB,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;IAE/C,IAAI,CAAC1D,eAAe,GAAG,IAAI,CAAC8C,WAAW,CAACI,KAAK,CAAC;MAC5CtE,SAAS,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAACmF,QAAQ,CAAC,CAAC;MACtCvE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACmF,QAAQ,CAAC,CAAC;MACrCO,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1F,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAAC0F,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACpF,WAAW,GAAG,IAAI,CAACkE,WAAW,CAACmB,gBAAgB;IACpD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAD,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACtF,WAAW,EAAE;MACpB,IAAI,CAACuB,eAAe,CAACiE,UAAU,CAAC;QAC9BrF,SAAS,EAAE,IAAI,CAACH,WAAW,CAACG,SAAS,IAAI,EAAE;QAC3CC,QAAQ,EAAE,IAAI,CAACJ,WAAW,CAACI,QAAQ,IAAI,EAAE;QACzC8E,KAAK,EAAE,IAAI,CAAClF,WAAW,CAACkF,KAAK,IAAI,EAAE;QACnCC,KAAK,EAAE,IAAI,CAACnF,WAAW,CAACmF,KAAK,IAAI;OAClC,CAAC;IACJ;EACF;EAEAjE,cAAcA,CAAA;IACZ,IAAI,CAACqD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACe,kBAAkB,EAAE;EAC3B;EAEAlD,UAAUA,CAAA;IACR,IAAI,CAACmC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,kBAAkB,EAAE;EAC3B;EAEAzD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACN,eAAe,CAACc,OAAO,EAAE;MAChC,IAAI,CAACoD,oBAAoB,CAAC,IAAI,CAAClE,eAAe,CAAC;MAC/C;IACF;IAEA,IAAI,CAACiB,OAAO,GAAG,IAAI;IACnB,MAAMkD,WAAW,GAAG,IAAI,CAACnE,eAAe,CAACoE,KAAK;IAE9C,IAAI,CAACzB,WAAW,CAAC0B,aAAa,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChF,IAAI,CAAC1B,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC/B,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,IAAI,CAACxC,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,GAAG;YAAE,GAAG,IAAI,CAACA,WAAW;YAAE,GAAG0F;UAAW,CAAE;QAC5D;MACF,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5B,QAAQ,CAAC0B,IAAI,CAChBE,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,0BAA0B,EAClD,OAAO,EACP;UAAEF,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACzD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmB,oBAAoBA,CAAA;IAClB,IAAI,CAACa,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAAC,IAAI,CAACA,kBAAkB,EAAE;MAC5B,IAAI,CAACZ,kBAAkB,CAACwC,KAAK,EAAE;IACjC;EACF;EAEApD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACY,kBAAkB,CAACvB,OAAO,EAAE;MACnC,IAAI,CAACoD,oBAAoB,CAAC,IAAI,CAAC7B,kBAAkB,CAAC;MAClD;IACF;IAEA,IAAI,CAACpB,OAAO,GAAG,IAAI;IACnB,MAAM6D,SAAS,GAAG,IAAI,CAACzC,kBAAkB,CAAC+B,KAAK;IAE/C,IAAI,CAACzB,WAAW,CAACoC,cAAc,CAC7BD,SAAS,CAAC3B,eAAe,EACzB2B,SAAS,CAACzB,WAAW,EACrByB,SAAS,CAACtB,cAAc,IAAIwB,SAAS,CACtC,CAACV,SAAS,CAAC;MACVC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACjF,IAAI,CAACrC,kBAAkB,CAACwC,KAAK,EAAE;QAC/B,IAAI,CAAC5B,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAChC,OAAO,GAAG,KAAK;MACtB,CAAC;MACD0D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5B,QAAQ,CAAC0B,IAAI,CAACE,KAAK,CAACC,OAAO,IAAI,2BAA2B,EAAE,OAAO,EAAE;UAAEF,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F,IAAI,CAACzD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAgE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACxG,WAAW,EAAEkF,KAAK,EAAE;MAC5B,IAAI,CAACZ,QAAQ,CAAC0B,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE;IACF;IAEA,IAAI,CAACzD,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0B,WAAW,CAACuC,cAAc,CAAC,IAAI,CAACzG,WAAW,CAACkF,KAAK,CAAC,CAACW,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAChB,0DAA0D,EAC1D,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACzD,OAAO,GAAG,KAAK;MACtB,CAAC;MACD0D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5B,QAAQ,CAAC0B,IAAI,CAChBE,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,qCAAqC,EAC7D,OAAO,EACP;UAAEF,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACzD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAqB,WAAWA,CAAA;IACT,OAAO,IAAI,CAACO,YAAY,CAACP,WAAW,CAAC,IAAI,CAAC7D,WAAW,CAAC;EACxD;EAEAW,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACyD,YAAY,CAACzD,oBAAoB,CAAC,IAAI,CAACX,WAAW,CAAC;EACjE;EAEAO,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC6D,YAAY,CAAC7D,oBAAoB,CAAC,IAAI,CAACP,WAAW,CAAC;EACjE;EAEAS,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC2D,YAAY,CAAC3D,qBAAqB,CAAC,IAAI,CAACT,WAAW,CAAC;EAClE;EAEA6C,aAAaA,CAAC6D,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC/C,kBAAkB,CAACpC,GAAG,CAACkF,SAAS,CAAC;IACpD,IAAIC,KAAK,EAAElF,MAAM,IAAIkF,KAAK,CAACrE,OAAO,EAAE;MAClC,IAAIqE,KAAK,CAAClF,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGiF,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAAClF,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGiF,SAAS,qBAAqBC,KAAK,CAAClF,MAAM,CAAC,WAAW,CAAC,CAACmF,cAAc,aAAa;MAC5H,IAAID,KAAK,CAAClF,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAO,wBAAwB;IACvE;IACA,OAAO,EAAE;EACX;EAEQwD,sBAAsBA,CAAC4B,IAAe;IAC5C,MAAMjC,WAAW,GAAGiC,IAAI,CAACrF,GAAG,CAAC,aAAa,CAAC;IAC3C,MAAMsD,eAAe,GAAG+B,IAAI,CAACrF,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIoD,WAAW,IAAIE,eAAe,IAAIF,WAAW,CAACe,KAAK,KAAKb,eAAe,CAACa,KAAK,EAAE;MACjFb,eAAe,CAACgC,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;IACvD,CAAC,MAAM;MACLjC,eAAe,EAAEgC,SAAS,CAAC,IAAI,CAAC;IAClC;IAEA,OAAO,IAAI;EACb;EAEQrB,oBAAoBA,CAACuB,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACxF,GAAG,CAAC6F,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAhC,aAAaA,CAAA;IACX,IAAI,CAACpB,gBAAgB,CAACqD,YAAY,EAAE,CAAC3B,SAAS,CAAC;MAC7CC,IAAI,EAAG2B,MAAM,IAAI;QACf,IAAI,CAAC3D,eAAe,GAAG2D,MAAM;MAC/B,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACfwB,OAAO,CAACxB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAAC,QAAAyB,CAAA,G;qCAjNU3D,gBAAgB,EAAAvE,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArI,EAAA,CAAAmI,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAvI,EAAA,CAAAmI,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAzI,EAAA,CAAAmI,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA3I,EAAA,CAAAmI,iBAAA,CAAAS,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBvE,gBAAgB;IAAAwE,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZzBtJ,EAFJ,CAAAC,cAAA,aAA+B,aACN,SACjB;QAAAD,EAAA,CAAAY,MAAA,kCAA2B;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAQ1BH,EANV,CAAAC,cAAA,uBAA4D,iBAErB,aACV,eACb,sBACS,qBACC;QAAAD,EAAA,CAAAY,MAAA,wBAAgB;QAClCZ,EADkC,CAAAG,YAAA,EAAiB,EACjC;QAEhBH,EADF,CAAAC,cAAA,wBAAkB,cACO;QACrBD,EAAA,CAAA0B,UAAA,KAAA8H,gCAAA,iBAAwD;QAInDxJ,EADL,CAAAC,cAAA,cAA0B,SACrB,cAAQ;QAAAD,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAwD;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACnFH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAwB;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACpDH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAA0C;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACtEH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,qBAAa;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAgD;;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QAGtFH,EAAA,CAAA0B,UAAA,KAAA+H,gCAAA,iBAA8C;QAaxCzJ,EAHN,CAAAC,cAAA,cAA4B,eACA,eACgG,gBAC5G;QAAAD,EAAA,CAAAY,MAAA,IAAyD;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC9EH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAAkE;QAC1EZ,EAD0E,CAAAG,YAAA,EAAO,EAC3E;QAEJH,EADF,CAAAC,cAAA,eAA+G,gBACnG;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAA0D;QAM5EZ,EAN4E,CAAAG,YAAA,EAAO,EACnE,EACF,EACF,EACF,EACF,EACW;QACnBH,EAAA,CAAAC,cAAA,wBAAkB;QAChBD,EAAA,CAAA0B,UAAA,KAAAgI,mCAAA,qBAAgF;QAKpF1J,EADE,CAAAG,YAAA,EAAmB,EACV;QAGXH,EAAA,CAAA0B,UAAA,KAAAiI,qCAAA,wBAAqD;QAmDzD3J,EADE,CAAAG,YAAA,EAAM,EACE;QAOFH,EAJR,CAAAC,cAAA,mBAA0B,cACC,gBACb,uBACS,sBACC;QAAAD,EAAA,CAAAY,MAAA,yBAAiB;QACnCZ,EADmC,CAAAG,YAAA,EAAiB,EAClC;QAKZH,EAJN,CAAAC,cAAA,wBAAkB,eAEc,eACA,UACtB;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAEfH,EADF,CAAAC,cAAA,eAA8B,kBACwC;QAAjCD,EAAA,CAAAmB,UAAA,mBAAAyI,mDAAA;UAAA,OAASL,GAAA,CAAArF,oBAAA,EAAsB;QAAA,EAAC;QACjElE,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAY,MAAA,IAAwD;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC7EH,EAAA,CAAAY,MAAA,IACF;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAoF;QAAlDD,EAAA,CAAAmB,UAAA,mBAAA0I,mDAAA;UAAA,OAASN,GAAA,CAAAxC,gBAAA,EAAkB;QAAA,EAAC;QAC5D/G,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAY,MAAA,yBACF;QACFZ,EADE,CAAAG,YAAA,EAAS,EACL;QACNH,EAAA,CAAA0B,UAAA,KAAAoI,gCAAA,kBAAyD;QAI3D9J,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAA0B,UAAA,KAAAqI,gCAAA,oBAA6D;QAgEvE/J,EAJQ,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACE;QAIRH,EADF,CAAAC,cAAA,mBAA2C,cAChB;QACvBD,EAAA,CAAAE,SAAA,iCAAuD;QAKjEF,EAJQ,CAAAG,YAAA,EAAM,EACE,EACI,EACZ,EACF;;;QAxMoCH,EAAA,CAAAI,SAAA,IAA4B;QAA5BJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAAC,SAAA,CAA4B;QAI1BR,EAAA,CAAAI,SAAA,GAAwD;QAAxDJ,EAAA,CAAAgK,kBAAA,MAAAT,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAAG,SAAA,OAAA6I,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAAI,QAAA,CAAwD;QACvDX,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAiB,kBAAA,MAAAsI,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAAkF,KAAA,CAAwB;QACxBzF,EAAA,CAAAI,SAAA,GAA0C;QAA1CJ,EAAA,CAAAiB,kBAAA,OAAAsI,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAAmF,KAAA,oBAA0C;QACnC1F,EAAA,CAAAI,SAAA,GAAgD;QAAhDJ,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAiK,WAAA,SAAAV,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAA2J,SAAA,gBAAgD;QAG5ElK,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAAnF,WAAA,GAAmB;QAYIpE,EAAA,CAAAI,SAAA,GAA6C;QAACJ,EAA9C,CAAAmK,WAAA,aAAAZ,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAA6J,aAAA,CAA6C,iBAAAb,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAA6J,aAAA,EAAiD;QAC3GpK,EAAA,CAAAI,SAAA,GAAyD;QAAzDJ,EAAA,CAAAmD,iBAAA,EAAAoG,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAA6J,aAAA,2BAAyD;QAC7DpK,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAiB,kBAAA,YAAAsI,GAAA,CAAAhJ,WAAA,kBAAAgJ,GAAA,CAAAhJ,WAAA,CAAA6J,aAAA,8BAAkE;QAEjDpK,EAAA,CAAAI,SAAA,EAAyC;QAACJ,EAA1C,CAAAmK,WAAA,YAAAZ,GAAA,CAAAlF,eAAA,CAAAC,OAAA,CAAyC,cAAAiF,GAAA,CAAAlF,eAAA,CAAAC,OAAA,CAA4C;QAEtGtE,EAAA,CAAAI,SAAA,GAA0D;QAA1DJ,EAAA,CAAAiB,kBAAA,SAAAsI,GAAA,CAAAlF,eAAA,CAAAC,OAAA,0BAA0D;QAQXtE,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAK,UAAA,UAAAkJ,GAAA,CAAAzE,QAAA,CAAe;QAQvE9E,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAAzE,QAAA,CAAc;QAmEH9E,EAAA,CAAAI,SAAA,IAAwD;QAAxDJ,EAAA,CAAAmD,iBAAA,CAAAoG,GAAA,CAAAxE,kBAAA,iCAAwD;QAClE/E,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAiB,kBAAA,MAAAsI,GAAA,CAAAxE,kBAAA,qCACF;QAC+D/E,EAAA,CAAAI,SAAA,EAAoB;QAApBJ,EAAA,CAAAK,UAAA,aAAAkJ,GAAA,CAAAxG,OAAA,CAAoB;QAK/E/C,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAAnF,WAAA,GAAmB;QAOrBpE,EAAA,CAAAI,SAAA,EAAwB;QAAxBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAAxE,kBAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}