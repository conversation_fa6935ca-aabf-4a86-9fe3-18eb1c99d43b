# Website Crawler & Document Generator Setup Script (PowerShell)
# This script helps set up the development environment on Windows

param(
    [switch]$SkipPython,
    [switch]$SkipDatabase,
    [switch]$Help
)

if ($Help) {
    Write-Host "Website Crawler & Document Generator Setup Script"
    Write-Host "Usage: .\setup.ps1 [-SkipPython] [-SkipDatabase] [-Help]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -SkipPython    Skip Python environment setup"
    Write-Host "  -SkipDatabase  Skip database setup"
    Write-Host "  -Help          Show this help message"
    exit 0
}

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check Node.js
    if (Test-Command "node") {
        $nodeVersion = node --version
        Write-Success "Node.js found: $nodeVersion"
    }
    else {
        Write-Error "Node.js not found. Please install Node.js 18+ or 20+"
        exit 1
    }
    
    # Check npm
    if (Test-Command "npm") {
        $npmVersion = npm --version
        Write-Success "npm found: $npmVersion"
    }
    else {
        Write-Error "npm not found. Please install npm"
        exit 1
    }
    
    # Check Python
    if (Test-Command "python") {
        $pythonVersion = python --version
        Write-Success "Python found: $pythonVersion"
    }
    elseif (Test-Command "python3") {
        $pythonVersion = python3 --version
        Write-Success "Python found: $pythonVersion"
    }
    else {
        Write-Error "Python not found. Please install Python 3.8+"
        exit 1
    }
    
    # Check pip
    if (Test-Command "pip") {
        $pipVersion = pip --version
        Write-Success "pip found: $pipVersion"
    }
    elseif (Test-Command "pip3") {
        $pipVersion = pip3 --version
        Write-Success "pip found: $pipVersion"
    }
    else {
        Write-Error "pip not found. Please install pip"
        exit 1
    }
    
    # Check PostgreSQL
    if (Test-Command "psql") {
        $psqlVersion = psql --version
        Write-Success "PostgreSQL found: $psqlVersion"
    }
    else {
        Write-Warning "PostgreSQL not found. Please install PostgreSQL 12+"
        Write-Warning "You can continue setup and install PostgreSQL later"
    }
    
    # Check conda
    if (Test-Command "conda") {
        $condaVersion = conda --version
        Write-Success "Conda found: $condaVersion"
    }
    else {
        Write-Warning "Conda not found. Using system Python instead"
    }
}

function Setup-PythonEnvironment {
    if ($SkipPython) {
        Write-Warning "Skipping Python environment setup"
        return
    }
    
    Write-Status "Setting up Python environment..."
    
    if (Test-Command "conda") {
        Write-Status "Conda found. Please activate environment manually:"
        Write-Warning "conda activate masonite-secure-env"
        Write-Warning "Then run this script again with -SkipPython, or continue with system Python"
    }
    
    Write-Status "Installing Python dependencies..."
    try {
        if (Test-Command "pip") {
            pip install -r loopback-backend/scripts/requirements.txt
        }
        elseif (Test-Command "pip3") {
            pip3 install -r loopback-backend/scripts/requirements.txt
        }
        Write-Success "Python dependencies installed"
    }
    catch {
        Write-Error "Failed to install Python dependencies: $_"
        exit 1
    }
}

function Setup-Backend {
    Write-Status "Setting up backend..."
    
    Push-Location loopback-backend
    
    try {
        Write-Status "Installing Node.js dependencies..."
        npm install
        
        Write-Status "Building backend..."
        npm run build
        
        Write-Success "Backend setup complete"
    }
    catch {
        Write-Error "Failed to setup backend: $_"
        exit 1
    }
    finally {
        Pop-Location
    }
}

function Setup-Frontend {
    Write-Status "Setting up frontend..."
    
    Push-Location frontend
    
    try {
        Write-Status "Installing Node.js dependencies..."
        npm install
        
        Write-Success "Frontend setup complete"
    }
    catch {
        Write-Error "Failed to setup frontend: $_"
        exit 1
    }
    finally {
        Pop-Location
    }
}

function New-StorageDirectories {
    Write-Status "Creating storage directories..."
    
    $directories = @(
        "loopback-backend/storage/crawled_content",
        "loopback-backend/storage/generated_documents",
        "loopback-backend/storage/temp"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Success "Storage directories created"
}

function New-EnvironmentFile {
    Write-Status "Creating environment configuration..."
    
    $envFile = "loopback-backend/.env"
    
    if (Test-Path $envFile) {
        Write-Warning "Environment file already exists: $envFile"
        return
    }
    
    $envContent = @"
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=website_crawler
DB_USER=postgres
DB_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_this_in_production

# Storage Configuration
STORAGE_PATH=./storage

# Application Configuration
NODE_ENV=development
PORT=3002

# CORS Configuration
CORS_ORIGIN=http://localhost:4200
"@
    
    $envContent | Out-File -FilePath $envFile -Encoding UTF8
    
    Write-Success "Environment file created: $envFile"
    Write-Warning "Please update the database credentials and JWT secret in $envFile"
}

function Setup-Database {
    if ($SkipDatabase) {
        Write-Warning "Skipping database setup"
        return
    }
    
    Write-Status "Setting up database..."
    
    if (!(Test-Command "psql")) {
        Write-Warning "PostgreSQL not found. Skipping database setup."
        Write-Warning "Please install PostgreSQL and run: npm run db:migrate:crawler"
        return
    }
    
    Push-Location loopback-backend
    
    try {
        Write-Status "Running database migrations..."
        
        # Check if we can connect to database
        $statusResult = npm run db:migrate:status 2>$null
        if ($LASTEXITCODE -eq 0) {
            npm run db:migrate:crawler
            Write-Success "Database migrations completed"
        }
        else {
            Write-Warning "Could not connect to database. Please:"
            Write-Warning "1. Ensure PostgreSQL is running"
            Write-Warning "2. Create database: website_crawler"
            Write-Warning "3. Update credentials in .env file"
            Write-Warning "4. Run: npm run db:migrate:crawler"
        }
    }
    catch {
        Write-Warning "Database setup failed. Please setup manually."
    }
    finally {
        Pop-Location
    }
}

function Main {
    Write-Host ""
    Write-Host "🚀 Website Crawler & Document Generator Setup" -ForegroundColor $Blue
    Write-Host "=============================================="
    Write-Host ""
    
    # Check if we're in the right directory
    if (!(Test-Path "README.md") -or !(Test-Path "frontend") -or !(Test-Path "loopback-backend")) {
        Write-Error "Please run this script from the project root directory"
        exit 1
    }
    
    Test-Prerequisites
    Write-Host ""
    
    New-StorageDirectories
    Write-Host ""
    
    New-EnvironmentFile
    Write-Host ""
    
    Setup-PythonEnvironment
    Write-Host ""
    
    Setup-Backend
    Write-Host ""
    
    Setup-Frontend
    Write-Host ""
    
    Setup-Database
    Write-Host ""
    
    Write-Success "Setup completed successfully! 🎉"
    Write-Host ""
    Write-Status "Next steps:"
    Write-Host "1. Update database credentials in loopback-backend/.env"
    Write-Host "2. Start backend: cd loopback-backend; npm run dev"
    Write-Host "3. Start frontend: cd frontend; ng serve"
    Write-Host "4. Open http://localhost:4200 in your browser"
    Write-Host ""
    Write-Status "For more information, see README.md"
}

# Run main function
Main
