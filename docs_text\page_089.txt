=== Page 90 ===

90In order to do this we need to add a test on the View class. We can once again do this
inside the app Service Provider
The code is simple and looks something like this:
That's it! Now we can use the a_company_owner in our templates just like the first code
snippet above!
Notice that we only supplied the function and we did not instantiate anything. The
function or object we supply needs to have 1 parameter which is the object or string
we are testing.<div>
    
<div data-gb-custom-block data-tag="if">
        hey boss
    
<div data-gb-custom-block data-tag="else"></div>
        you are an employee
    
</div>
</div>
from masonite.facades import View
def a_company_owner(user):
    # Returns True or False
    return user.owner == 1
class AppProvider(Provider):
    def register(self):
        # template alias
        View.test('a_company_owner', a_company_owner)6/12/25, 3:02 AM Masonite Documentation