=== Page 169 ===

169•Gate
•Hash
•Loader
•Mail
•Notification
•Queue
•Request
•Response
•Session
•Storage
•View
•Url
To create your own facade is simple:
Then import and use your facade:
To benefit from code intellisense/type hinting when using Facade (e.g. in VSCode editor)
you should create a .pyi file just next to your facade file class to add type hinting to
your new facade.from masonite.facades import Facade
class YourFacade(metaclass=Facade):
  key = 'container_key'
from app.facades import YourFacade
YourFacade.method()Creating Your Own Facades6/12/25, 3:02 AM Masonite Documentation