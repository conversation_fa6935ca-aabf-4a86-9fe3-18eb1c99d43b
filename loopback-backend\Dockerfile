# Multi-stage build for <PERSON><PERSON><PERSON> backend with Python support
FROM node:18-alpine AS builder

# Install Python and build dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    python3-dev \
    build-base \
    postgresql-dev \
    curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install Python and runtime dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    postgresql-client \
    curl \
    && ln -sf python3 /usr/bin/python

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/dist ./dist
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nodejs:nodejs /app/package*.json ./

# Copy Python scripts and requirements
COPY --chown=nodejs:nodejs scripts/ ./scripts/
COPY --chown=nodejs:nodejs database/ ./database/

# Install Python dependencies
RUN pip3 install --no-cache-dir -r scripts/requirements.txt

# Create storage directories
RUN mkdir -p storage/crawled_content storage/generated_documents storage/temp && \
    chown -R nodejs:nodejs storage

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3002/ping || exit 1

# Start the application
CMD ["node", "-r", "source-map-support/register", "dist/index.js"]
