=== Page 437 ===

437Masonite 2.0 to 2.1
Masonite 2.1 is a fantastic release. It works out a lot of the kinks that were in 2.0 as well
as brings several new syntactically good looking code generation
This guide just shows the major changes between version to get your application working
on 2.1. You should see the What's New in 2.1 documentation to upgrade smaller parts of
your code that are likely to be smaller quality of life improvements.
For 2.1 you will need masonite-cli>=2.1.0.
Make sure you run:
Middleware has been changed to classes so instead of doing this in your 
config/middleware.py file:
You will now import it directly:$ pip install masonite-cli --upgrade
HTTP_MIDDLEWARE = [
    'app.http.middleware.DashboardMiddleware.DashboardMiddleware',
]Introduction
Masonite CLI
Middleware6/12/25, 3:02 AM Masonite Documentation