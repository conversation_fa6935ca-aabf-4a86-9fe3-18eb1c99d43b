=== Page 350 ===

350The measures collector you can use to measure 2 points in your application. By default
there is the time it takes for your application to complete the whole request. You can start
and stop any measures you want:
You will now see the time it takes to run this code in the measures tab
If you find a need to create your own collector, maybe to log information related to
exceptions or something similar, you can create your own collector simply:
Collectors are simple instances like this:
The restart method is required to restart your collector have each request so the
information doesn't get persisted bewteen requests. If this is not required for your
collector then you can simply return self.from debugbar.debugger import Debugger
Debugger.start_measure("loop check")
# .. Long running code
Debugger.stop_measure("loop check")
class YourCollector:
    def __init__(self, name="Your Collector", 
description="Description"):
        self.messages = []
        self.name = name
        self.description = description
    def restart(self):
        self.messages = []
        return selfAdding Your Own Collectors
Creating the Collector6/12/25, 3:02 AM Masonite Documentation