=== Page 251 ===

251In any service provider's boot method (preferably a provider where wsgi=False to
prevent it from running on every request) we can register our rule with the validator class.
If you don't have a provider yet we can make one specifically for adding custom rules:
Then inside this rule provider's boot method we can resolve and register our rule. This will
look like:
Now instead of importing the rule we can just use it as normal:terminal
$ python craft provider RuleProvider
from app.rules.equals_masonite import equals_masonite
from masonite.validation import Validator
class RuleProvider(ServiceProvider):
    """Provides Services To The Service Container
    """
    def __init__(self, application):
        self.application = application
    def register(self, validator: Validator):
        """Boots services required by the container
        """
        self.application.make('validator').register(equals_masonite)6/12/25, 3:02 AM Masonite Documentation