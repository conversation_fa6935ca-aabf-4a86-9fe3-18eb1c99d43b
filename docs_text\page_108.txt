=== Page 109 ===

1091.A masonite.exception.SomeException event will be fired.
2.A specific ExceptionHandler will be used if it exists for the given exception.
3.Exceptionite will then handle the error by rendering it in the console
•and with the Exceptionite JSON response if accepted content is 
application/json
•else with the Exceptionite HTML error page
1.A masonite.exception.SomeException event will be fired.
2.A specific ExceptionHandler will be used if it exists for the given exception. In this
case the default ExceptionHandler won't process the exception anymore.
3.If exception is an HTTP exception it will be handled by the HTTPExceptionHandler
which is responsible for selecting an error template (errors/500.html, 
errors/404.html, errors/403.html) and rendering it.
4.If exception is a Renderable exception it will be rendered accordingly.
5.Else this exception has not been yet handled and will be handled as an HTTP
exception with 500 Server Error status code.
To report an exception you should simply raise it as any Python exceptions:
There are different type of exceptions.def index(self, view:View):
    user = User.find(request.param("id"))
    if not user:
        raise RouteNotFoundException("User not found")In Development
In Production
Report Exceptions
Simple Exceptions6/12/25, 3:02 AM Masonite Documentation