"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneratedDocument = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const crawl_job_model_1 = require("./crawl-job.model");
const user_model_1 = require("./user.model");
let GeneratedDocument = class GeneratedDocument extends repository_1.Entity {
    constructor(data) {
        super(data);
    }
};
exports.GeneratedDocument = GeneratedDocument;
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        id: true,
        generated: true,
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "id", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        jsonSchema: {
            minLength: 1,
            maxLength: 255,
        },
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "filename", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'pdf',
        jsonSchema: {
            enum: ['pdf', 'docx', 'markdown', 'html', 'txt'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "format", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'pending',
        jsonSchema: {
            enum: ['pending', 'generating', 'completed', 'failed'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "status", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        required: true,
        default: 'single_file',
        jsonSchema: {
            enum: ['single_file', 'separate_files', 'grouped_folders'],
        },
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "organizationType", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'array',
        itemType: 'string',
        default: [],
    }),
    tslib_1.__metadata("design:type", Array)
], GeneratedDocument.prototype, "selectedContentIds", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
        default: {},
    }),
    tslib_1.__metadata("design:type", Object)
], GeneratedDocument.prototype, "generationOptions", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'file_path'
        }
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "filePath", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
        postgresql: {
            columnName: 'file_size'
        }
    }),
    tslib_1.__metadata("design:type", Number)
], GeneratedDocument.prototype, "fileSize", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'download_url'
        }
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "downloadUrl", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'destination_folder'
        }
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "destinationFolder", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'object',
        default: {},
    }),
    tslib_1.__metadata("design:type", Object)
], GeneratedDocument.prototype, "metadata", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "errorMessage", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
        jsonSchema: {
            minimum: 0,
            maximum: 100,
        },
    }),
    tslib_1.__metadata("design:type", Number)
], GeneratedDocument.prototype, "progressPercentage", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], GeneratedDocument.prototype, "totalPages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], GeneratedDocument.prototype, "processedPages", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
    }),
    tslib_1.__metadata("design:type", Number)
], GeneratedDocument.prototype, "generationTimeMs", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'expires_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], GeneratedDocument.prototype, "expiresAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'boolean',
        default: false,
        postgresql: {
            columnName: 'is_public'
        }
    }),
    tslib_1.__metadata("design:type", Boolean)
], GeneratedDocument.prototype, "isPublic", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'string',
        postgresql: {
            columnName: 'access_token'
        }
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "accessToken", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'number',
        default: 0,
        postgresql: {
            columnName: 'download_count'
        }
    }),
    tslib_1.__metadata("design:type", Number)
], GeneratedDocument.prototype, "downloadCount", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'last_downloaded_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], GeneratedDocument.prototype, "lastDownloadedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'started_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], GeneratedDocument.prototype, "startedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        postgresql: {
            columnName: 'completed_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], GeneratedDocument.prototype, "completedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'created_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], GeneratedDocument.prototype, "createdAt", void 0);
tslib_1.__decorate([
    (0, repository_1.property)({
        type: 'date',
        default: () => new Date(),
        postgresql: {
            columnName: 'updated_at'
        }
    }),
    tslib_1.__metadata("design:type", Date)
], GeneratedDocument.prototype, "updatedAt", void 0);
tslib_1.__decorate([
    (0, repository_1.belongsTo)(() => crawl_job_model_1.CrawlJob, {}, {
        postgresql: {
            columnName: 'crawl_job_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "crawlJobId", void 0);
tslib_1.__decorate([
    (0, repository_1.belongsTo)(() => user_model_1.User, {}, {
        postgresql: {
            columnName: 'user_id'
        }
    }),
    tslib_1.__metadata("design:type", String)
], GeneratedDocument.prototype, "userId", void 0);
exports.GeneratedDocument = GeneratedDocument = tslib_1.__decorate([
    (0, repository_1.model)({
        settings: {
            strict: true,
            indexes: {
                crawlJobIdIndex: {
                    keys: {
                        crawlJobId: 1,
                    },
                },
                userIdIndex: {
                    keys: {
                        userId: 1,
                    },
                },
                formatIndex: {
                    keys: {
                        format: 1,
                    },
                },
                statusIndex: {
                    keys: {
                        status: 1,
                    },
                },
                createdAtIndex: {
                    keys: {
                        createdAt: -1,
                    },
                },
            },
            postgresql: {
                schema: 'public',
                table: 'generated_document'
            }
        },
    }),
    tslib_1.__metadata("design:paramtypes", [Object])
], GeneratedDocument);
//# sourceMappingURL=generated-document.model.js.map