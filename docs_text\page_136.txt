=== Page 137 ===

137Note that the event name emitted to the client will be the name of the class. Here it would
be UserAdded.
You can broadcast the event using the Broadcast facade or by resolving Broadcast
class from container.
You can broadcast easily without creating a Broadcast event
Or you can broadcast the event class created earlier
You may broadcast on multiple channels as well:
This type of broadcasting will emit all channels as public. For private and presence
channels, keep reading.from masonite.broadcasting import CanBroadcast, Channel
class UserAdded(CanBroadcast):
    def broadcast_on(self):
        return Channel("channel_name")
from masonite.facades import Broadcast
Broadcast.channel('channel_name', "event_name", {"key": "value"})
from masonite.facades import Broadcast
from app.broadcasts import UserAdded
broadcast.channel(UserAdded())
Broadcast.channel(['channel1', 'channel2'], "event_name", {"key": 
"value"})
Broadcasting Events6/12/25, 3:02 AM Masonite Documentation