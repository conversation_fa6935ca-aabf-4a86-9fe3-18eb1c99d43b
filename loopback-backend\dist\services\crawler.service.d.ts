import { CrawlJobRepository, CrawledContentRepository } from '../repositories';
import { CrawlJob } from '../models';
export interface CrawlOptions {
    maxDepth?: number;
    maxPages?: number;
    allowedContentTypes?: string[];
    excludePatterns?: string[];
    includePatterns?: string[];
    followExternalLinks?: boolean;
    respectRobotsTxt?: boolean;
    delayBetweenRequests?: number;
    userAgent?: string;
    timeout?: number;
}
export interface CrawlProgress {
    jobId: string;
    status: string;
    processedPages: number;
    totalPages: number;
    currentUrl?: string;
    errorMessage?: string;
}
export declare class CrawlerService {
    crawlJobRepository: CrawlJobRepository;
    crawledContentRepository: CrawledContentRepository;
    private activeCrawlers;
    private crawlerScriptPath;
    constructor(crawlJobRepository: CrawlJobRepository, crawledContentRepository: CrawledContentRepository);
    /**
     * Start a new crawl job
     */
    startCrawl(crawlJob: CrawlJob): Promise<void>;
    /**
     * Stop a running crawl job
     */
    stopCrawl(jobId: string): Promise<void>;
    /**
     * Pause a running crawl job
     */
    pauseCrawl(jobId: string): Promise<void>;
    /**
     * Resume a paused crawl job
     */
    resumeCrawl(jobId: string): Promise<void>;
    /**
     * Get crawl job progress
     */
    getCrawlProgress(jobId: string): Promise<CrawlProgress>;
    /**
     * Spawn Python crawler process
     */
    private spawnCrawlerProcess;
    /**
     * Handle crawler output for progress updates
     */
    private handleCrawlerOutput;
    /**
     * Handle crawler process exit
     */
    private handleCrawlerExit;
    /**
     * Handle crawl errors
     */
    private handleCrawlError;
    /**
     * Update crawl progress
     */
    private updateCrawlProgress;
    /**
     * Save crawled content
     */
    private saveCrawledContent;
    /**
     * Get active crawlers count
     */
    getActiveCrawlersCount(): number;
    /**
     * Get all active crawler job IDs
     */
    getActiveCrawlerJobIds(): string[];
}
