=== Page 329 ===

329Assert that response is JSON and contains the given path, with eventually the given value
if provided. The path can be a dotted path.
Assert that response is JSON and is strictly equal to the given dictionary.
Assert that response is JSON and has the given count of keys at root level or at the given
key (if provided).
Assert that response is JSON and does not contain given path. The path can be a dotted
path.
Handy dump and dd helpers are available on the HTTPTestResponse returned by 
get, post, put, patch, or delete during a unit test.self.get("/").assertJsonPath(path, value=None)
self.get("/").assertJsonPath("user.profile.name", "<PERSON>")
self.get("/").assertJsonExact(data)
self.get("/").assertJsonCount(count, key=None)
self.get("/").assertJsonMissing(path)assertJsonPath
assertJsonExact
assertJsonCount
assertJsonMissing
Dump Data During Tests6/12/25, 3:02 AM Masonite Documentation