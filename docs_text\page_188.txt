=== Page 189 ===

189Each notification class has a via method that specify on which channels the
notification will be delivered. Notifications may be sent on the mail, database, 
broadcast, slack and vonage channels. More details on this later. When sending the
notification it will be automatically sent to each channel.
If you would like to use an other delivery channel, feel free to check if a community driver
has been developed for it or create your own driver and share it with the community !
via method should returns a list of the channels you want your notification to be
delivered on. This method receives a notifiable instance.
You can send your notification inside your controller easily by using the Notification
class:
If the notification needs to be delivered to multiple channels you can chain the different
routes:
class WelcomeNotification(Notification, Mailable):
    ...
    def via(self, notifiable):
        """Send this notification by email, Slack and save it in 
database."""
        return ["mail", "slack", "database"]
from masonite.notification import NotificationManager
from app.notifications.Welcome import Welcome
class WelcomeController(Controller):
    def welcome(self, notification: NotificationManager):
        notification.route('mail', '<EMAIL>').send(Welcome())Sending a Notification
Basic6/12/25, 3:02 AM Masonite Documentation