=== Page 335 ===

335Commands Tests
You can test your custom commands running in console with craft test helper.
This will programmatically run the command if it has been registered in your project and
assert that no errors has been reported.
The following assertions are available when testing command with craft.
•assertSuccess
•assertHasErrors
•assertOutputContains
•assertExactOutput
•assertOutputMissing
•assertExactErrors
Assert that command exited with code 0 meaning that it ran successfully.
Assert command output has errors.def test_my_command(self):
    self.craft("my_command", "arg1 arg2").assertSuccess()
self.craft("my_command").assertSuccess()
self.craft("my_command").assertHasErrors()Available Assertions
assertSuccess
assertHasErrors6/12/25, 3:02 AM Masonite Documentation