=== Page 18 ===

18Masonite ORM by default protects against mass assignment as a security measure so
we will explicitly need to set what columns we would like to be fillable (this way we can
pass the column names into the create and update methods later).
The relationship is pretty straight forward here. Remember that we created a foreign key
in our migration. We can create that relationship in our model like so:"""Post Model."""
from masoniteorm.models import Model
class Post(Model):
    __table__ = 'user_posts'
app/Post.py
"""A Post Database Model."""
from masoniteorm.models import Model
class Post(Model):
    __fillable__ = ['title', 'author_id', 'body']
app/models/Post.pyMass Assignment
Relationships6/12/25, 3:02 AM Masonite Documentation