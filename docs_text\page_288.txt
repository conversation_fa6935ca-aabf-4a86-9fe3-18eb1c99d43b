=== Page 289 ===

289In order to retrieve a class from the service container, we can simply use the make
method.
That's it! This is useful as an IOC container which you can load a single class into the
container and use that class everywhere throughout your project.
You can bind singletons into the container. This will resolve the object at the time of
binding. This will allow the same object to be used throughout the lifetime of the server.
You can also check if a key exists in the container by using the has method:
You can also check if a key exists in the container by using the in keyword.>>> from app.User import User
>>> app.bind('User', User)
>>> app.make('User')
<class app.User.User>
from masonite.provider import ServiceProvider
from app.helpers import SomeClass
class UserModelProvider(ServiceProvider):
    def register(self):
        self.application.singleton('SomeClass', SomeClass)
    def boot(self):
        pass
app.has('request')Make
Singleton
Has6/12/25, 3:02 AM Masonite Documentation