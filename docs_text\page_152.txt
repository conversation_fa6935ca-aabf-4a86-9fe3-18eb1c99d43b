=== Page 153 ===

153Compiling Assets
Masonite uses Laravel Mix which provides a really simple way to handle asset compiling
even greater than simple SASS and LESS. You don't need to be an expert in either Laravel
Mix or NPM to compile assets, though.
To get started we can simply run NPM install:
This will install everything you need to start compiling assets.
The configuration settings will be made inside your webpack.mix.js file located in the
root of your project.
You can see there is already an example config setup for you that looks like this:
This will move these 2 files, resources/js/app.js and resources/css/app.css and
compile them both into the storage/compiled directory.
Feel free to change which directories the files get compiled to. For more information on
additional configuration values take a look at theLaravel Mix Documentation$ npm install
mix
  .js("resources/js/app.js", "storage/compiled/js")
  .postCss("resources/css/app.css", "storage/compiled/css", [
    //
  ]);
Getting Started
Configuration
Installing TailwindCSS6/12/25, 3:02 AM Masonite Documentation