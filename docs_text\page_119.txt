=== Page 120 ===

120Once these steps are done, you may now create your API's
Once the setup is done, we may start building the API.
One of the ways to build an API is using controller and route resources.
A controller resource is a controller with several methods on them used to specify each
action within an entity in the application (like users).
To create a controller resource you can run the controller command with an -a flag:
This will create a controller with the following methods:
•index
•show
•store
•update
•destroy
You can then create a route resource:$ python craft controller api/UsersController -a
# routes/api.py
from masonite.routes import Route
ROUTES = [
    # ..
    Route.api('users', "api.UserController")
]Creating Your API
Route Resources6/12/25, 3:02 AM Masonite Documentation