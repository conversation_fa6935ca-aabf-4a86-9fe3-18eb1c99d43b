=== Page 217 ===

217If you want to publish some specific resources only, you can use --resources flag:
Here this will only publish configuration and views into your project.
Finally if you want to check what resources a package can publish you just need to run:
This will output the list of resources that the package is going to publish into your project.
If the package has been released on PyPi you need to install it as any Python package:
Then you should follow package installation guidelines but often it will consist in:
•registering the package Service Provider in your project:
•publishing some files if you need to tweak package resources or configuration:
You should be ready to use the package in your project !$ python craft package:publish super_awesome --resources config,views
$ python craft package:publish super_awesome --dry
$ pip install super-awesome-package
from super_awesome_package.providers import SuperAwesomeProvider
PROVIDERS = [
  # ...
  SuperAwesomeProvider,
]
$ python craft package:publish super-awesome-packageConsuming a Package6/12/25, 3:02 AM Masonite Documentation