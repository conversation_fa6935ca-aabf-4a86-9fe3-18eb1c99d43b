=== Page 449 ===

449Masonite 2.2 completely removed the validation package from 2.1 and created an even
better all new validation package. You'll have to remove all your validation classes and
use the new validation package.
For example you may have had a validation class that looked like this:
and used it inside your views like this:from masonite.validation.providers import ValidationProvider
PROVIDERS = [
    ...
    ValidationProvider,
    ...
]
class RegisterValidator(Validator):
    def register(self):
        users = User.all()
        self.messages({
            'email': 'That email already exists',
            'username': 'Usernames should be between 3 and 20 
characters long'
        })
        return self.validate({
            'username': [Required, Length(3, 20)],
            'email': [Required, Length(1), 
Not(In(users.pluck('email')))],
            'password': [Required]
        })Replacing Validation Code6/12/25, 3:02 AM Masonite Documentation