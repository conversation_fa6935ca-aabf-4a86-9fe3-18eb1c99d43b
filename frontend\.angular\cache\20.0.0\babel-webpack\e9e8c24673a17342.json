{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/progress-spinner\";\nexport class OAuthSuccessComponent {\n  constructor(route, router, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.isNewUser = false;\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      const token = params['token'];\n      const isNewUser = params['isNewUser'] === 'true';\n      this.isNewUser = isNewUser;\n      if (token) {\n        // Store token and redirect\n        localStorage.setItem('authToken', token);\n        localStorage.setItem('isAuthenticated', 'true');\n        localStorage.setItem('oauthLogin', 'true');\n        // Redirect to dashboard after 2 seconds\n        setTimeout(() => {\n          this.router.navigate(['/dashboard'], {\n            queryParams: {\n              welcome: this.isNewUser ? 'true' : null,\n              oauth: 'true'\n            }\n          });\n        }, 2000);\n      } else {\n        // Check if data is already in localStorage (from the backend redirect)\n        const storedToken = localStorage.getItem('authToken');\n        const storedUser = localStorage.getItem('user');\n        if (storedToken && storedUser) {\n          // Redirect to dashboard\n          setTimeout(() => {\n            this.router.navigate(['/dashboard'], {\n              queryParams: {\n                welcome: this.isNewUser ? 'true' : null,\n                oauth: 'true'\n              }\n            });\n          }, 1500);\n        } else {\n          // No token found, redirect to login\n          this.router.navigate(['/auth/login']);\n        }\n      }\n    });\n  }\n  static #_ = this.ɵfac = function OAuthSuccessComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OAuthSuccessComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: OAuthSuccessComponent,\n    selectors: [[\"app-oauth-success\"]],\n    standalone: false,\n    decls: 13,\n    vars: 2,\n    consts: [[1, \"oauth-success-container\"], [1, \"success-card\"], [1, \"success-icon\"], [1, \"loading-spinner\"], [\"diameter\", \"40\"], [1, \"loading-text\"]],\n    template: function OAuthSuccessComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\");\n        i0.ɵɵtext(4, \"check_circle\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"h2\");\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"p\");\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 3);\n        i0.ɵɵelement(10, \"mat-spinner\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"p\", 5);\n        i0.ɵɵtext(12, \"Redirecting to dashboard...\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.isNewUser ? \"Account Created Successfully!\" : \"Login Successful!\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.isNewUser ? \"Welcome to our platform!\" : \"Welcome back!\");\n      }\n    },\n    dependencies: [i4.MatIcon, i5.MatProgressSpinner],\n    styles: [\".oauth-success-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n      padding: 20px;\\n    }\\n    \\n    .success-card[_ngcontent-%COMP%] {\\n      background: white;\\n      padding: 40px;\\n      border-radius: 10px;\\n      box-shadow: 0 10px 30px rgba(0,0,0,0.2);\\n      text-align: center;\\n      max-width: 400px;\\n      width: 100%;\\n    }\\n    \\n    .success-icon[_ngcontent-%COMP%] {\\n      color: #4CAF50;\\n      font-size: 48px;\\n      margin-bottom: 20px;\\n    }\\n    \\n    .success-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n      font-size: 48px;\\n      width: 48px;\\n      height: 48px;\\n    }\\n    \\n    h2[_ngcontent-%COMP%] {\\n      margin: 20px 0;\\n      color: #333;\\n    }\\n    \\n    p[_ngcontent-%COMP%] {\\n      color: #666;\\n      margin: 10px 0;\\n    }\\n    \\n    .loading-spinner[_ngcontent-%COMP%] {\\n      margin: 20px 0;\\n    }\\n    \\n    .loading-text[_ngcontent-%COMP%] {\\n      font-size: 14px;\\n      color: #888;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["OAuthSuccessComponent", "constructor", "route", "router", "authService", "snackBar", "isNewUser", "ngOnInit", "queryParams", "subscribe", "params", "token", "localStorage", "setItem", "setTimeout", "navigate", "welcome", "o<PERSON>h", "storedToken", "getItem", "storedUser", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthService", "i3", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "OAuthSuccessComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\oauth-success\\oauth-success.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-oauth-success',\r\n  template: `\r\n    <div class=\"oauth-success-container\">\r\n      <div class=\"success-card\">\r\n        <div class=\"success-icon\">\r\n          <mat-icon>check_circle</mat-icon>\r\n        </div>\r\n        <h2>{{ isNewUser ? 'Account Created Successfully!' : 'Login Successful!' }}</h2>\r\n        <p>{{ isNewUser ? 'Welcome to our platform!' : 'Welcome back!' }}</p>\r\n        <div class=\"loading-spinner\">\r\n          <mat-spinner diameter=\"40\"></mat-spinner>\r\n        </div>\r\n        <p class=\"loading-text\">Redirecting to dashboard...</p>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .oauth-success-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      padding: 20px;\r\n    }\r\n    \r\n    .success-card {\r\n      background: white;\r\n      padding: 40px;\r\n      border-radius: 10px;\r\n      box-shadow: 0 10px 30px rgba(0,0,0,0.2);\r\n      text-align: center;\r\n      max-width: 400px;\r\n      width: 100%;\r\n    }\r\n    \r\n    .success-icon {\r\n      color: #4CAF50;\r\n      font-size: 48px;\r\n      margin-bottom: 20px;\r\n    }\r\n    \r\n    .success-icon mat-icon {\r\n      font-size: 48px;\r\n      width: 48px;\r\n      height: 48px;\r\n    }\r\n    \r\n    h2 {\r\n      margin: 20px 0;\r\n      color: #333;\r\n    }\r\n    \r\n    p {\r\n      color: #666;\r\n      margin: 10px 0;\r\n    }\r\n    \r\n    .loading-spinner {\r\n      margin: 20px 0;\r\n    }\r\n    \r\n    .loading-text {\r\n      font-size: 14px;\r\n      color: #888;\r\n    }\r\n  `],\r\n  standalone: false\r\n})\r\nexport class OAuthSuccessComponent implements OnInit {\r\n  isNewUser = false;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.route.queryParams.subscribe(params => {\r\n      const token = params['token'];\r\n      const isNewUser = params['isNewUser'] === 'true';\r\n      \r\n      this.isNewUser = isNewUser;\r\n\r\n      if (token) {\r\n        // Store token and redirect\r\n        localStorage.setItem('authToken', token);\r\n        localStorage.setItem('isAuthenticated', 'true');\r\n        localStorage.setItem('oauthLogin', 'true');\r\n        \r\n        // Redirect to dashboard after 2 seconds\r\n        setTimeout(() => {\r\n          this.router.navigate(['/dashboard'], {\r\n            queryParams: { \r\n              welcome: this.isNewUser ? 'true' : null,\r\n              oauth: 'true'\r\n            }\r\n          });\r\n        }, 2000);\r\n      } else {\r\n        // Check if data is already in localStorage (from the backend redirect)\r\n        const storedToken = localStorage.getItem('authToken');\r\n        const storedUser = localStorage.getItem('user');\r\n        \r\n        if (storedToken && storedUser) {\r\n          // Redirect to dashboard\r\n          setTimeout(() => {\r\n            this.router.navigate(['/dashboard'], {\r\n              queryParams: { \r\n                welcome: this.isNewUser ? 'true' : null,\r\n                oauth: 'true'\r\n              }\r\n            });\r\n          }, 1500);\r\n        } else {\r\n          // No token found, redirect to login\r\n          this.router.navigate(['/auth/login']);\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AA2EA,OAAM,MAAOA,qBAAqB;EAGhCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IANlB,KAAAC,SAAS,GAAG,KAAK;EAOd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACL,KAAK,CAACM,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,MAAMC,KAAK,GAAGD,MAAM,CAAC,OAAO,CAAC;MAC7B,MAAMJ,SAAS,GAAGI,MAAM,CAAC,WAAW,CAAC,KAAK,MAAM;MAEhD,IAAI,CAACJ,SAAS,GAAGA,SAAS;MAE1B,IAAIK,KAAK,EAAE;QACT;QACAC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC;QACxCC,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;QAC/CD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;QAE1C;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;YACnCP,WAAW,EAAE;cACXQ,OAAO,EAAE,IAAI,CAACV,SAAS,GAAG,MAAM,GAAG,IAAI;cACvCW,KAAK,EAAE;;WAEV,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL;QACA,MAAMC,WAAW,GAAGN,YAAY,CAACO,OAAO,CAAC,WAAW,CAAC;QACrD,MAAMC,UAAU,GAAGR,YAAY,CAACO,OAAO,CAAC,MAAM,CAAC;QAE/C,IAAID,WAAW,IAAIE,UAAU,EAAE;UAC7B;UACAN,UAAU,CAAC,MAAK;YACd,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;cACnCP,WAAW,EAAE;gBACXQ,OAAO,EAAE,IAAI,CAACV,SAAS,GAAG,MAAM,GAAG,IAAI;gBACvCW,KAAK,EAAE;;aAEV,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL;UACA,IAAI,CAACd,MAAM,CAACY,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACvC;MACF;IACF,CAAC,CAAC;EACJ;EAAC,QAAAM,CAAA,G;qCArDUrB,qBAAqB,EAAAsB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArB/B,qBAAqB;IAAAgC,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhExBjB,EAHN,CAAAmB,cAAA,aAAqC,aACT,aACE,eACd;QAAAnB,EAAA,CAAAoB,MAAA,mBAAY;QACxBpB,EADwB,CAAAqB,YAAA,EAAW,EAC7B;QACNrB,EAAA,CAAAmB,cAAA,SAAI;QAAAnB,EAAA,CAAAoB,MAAA,GAAuE;QAAApB,EAAA,CAAAqB,YAAA,EAAK;QAChFrB,EAAA,CAAAmB,cAAA,QAAG;QAAAnB,EAAA,CAAAoB,MAAA,GAA8D;QAAApB,EAAA,CAAAqB,YAAA,EAAI;QACrErB,EAAA,CAAAmB,cAAA,aAA6B;QAC3BnB,EAAA,CAAAsB,SAAA,sBAAyC;QAC3CtB,EAAA,CAAAqB,YAAA,EAAM;QACNrB,EAAA,CAAAmB,cAAA,YAAwB;QAAAnB,EAAA,CAAAoB,MAAA,mCAA2B;QAEvDpB,EAFuD,CAAAqB,YAAA,EAAI,EACnD,EACF;;;QAPErB,EAAA,CAAAuB,SAAA,GAAuE;QAAvEvB,EAAA,CAAAwB,iBAAA,CAAAN,GAAA,CAAAlC,SAAA,yDAAuE;QACxEgB,EAAA,CAAAuB,SAAA,GAA8D;QAA9DvB,EAAA,CAAAwB,iBAAA,CAAAN,GAAA,CAAAlC,SAAA,gDAA8D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}