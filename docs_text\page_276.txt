=== Page 277 ===

277Sometimes you will want only one of several fields to be required. At least one of them
need to be required.
This will pass because at least 1 value has been found: user.
You can also use the phone validator to validate the most common phone number
formats:
The available patterns are:"""
{
  'age': 25,
  'active': None
}
"""
validate.numeric('age')
"""
{
  'user': 'Joe',
  'email': '<EMAIL>,
  'phone': '************'
}
"""
validate.one_of(['user', 'accepted', 'location'])
"""
{
  'phone': '************'
}
"""
validate.phone('phone', pattern='************')One_of
Phone6/12/25, 3:02 AM Masonite Documentation