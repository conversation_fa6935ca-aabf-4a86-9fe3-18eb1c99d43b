=== Page 75 ===

75Inputs can be from any kind of request method. In Masonite, fetching the input is the
same no matter which request method it is.
To fetch an input we can do:
If an input doesn't exist you can pass in a default:
To fetch all inputs from request we can do:
Or we can only fetch some inputs:
To fetch all inputs from request we can do:from masonite.request import Request
#..
def show(self, request: Request):
  # GET /dashboard?user_id=1
  request.input('user_id') #== 1
from masonite.request import Request
#..
def show(self, request: Request):
  # GET /dashboard
  request.input('user_id', 5) #== 5
from masonite.request import Request
def show(self, request: Request):
  request.all() #== {"user_id": 1, "name": "<PERSON>", "email": 
"<EMAIL>"}
from masonite.request import Request
def show(self, request: Request):
  request.only("user_id", "name") #== {"user_id": 1, "name": "<PERSON>"}
Getting Dictionary Input6/12/25, 3:02 AM Masonite Documentation