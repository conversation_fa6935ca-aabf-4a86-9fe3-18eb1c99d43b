=== Page 220 ===

220If you want to save failed jobs you should also create a migration for the failed jobs table:
You should then migrate your project:
The database driver is used to process jobs and failed job via a database connection.
The AMQP driver is used for connection that use the AMQP protocol, such as RabbitMQ.
The available options include:$ python craft queue:failed
$ python craft migrate
Option Description
connectionSpecifies the connection to use for finding the
jobs and failed jobs table.
table Specifies the name of the table to store jobs in
failed_jobsSpecifies the table to store failed_jobs in. Set
to None to not save failed jobs.
attemptsSpecifies the default number of times to
attempt a job before considering it a failed job.
pollSpecifies the time in seconds to wait before
calling the database to find new jobs
tzThe timezone that the database should save
and find timestamps in
Option Description
username The username of your AMQP connectionAMQP Driver6/12/25, 3:02 AM Masonite Documentation