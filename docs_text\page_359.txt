=== Page 360 ===

360Y<PERSON> may now simply upload images to both disk and Amazon S3 storage right out of the
box. With the new UploadProvider service provider you can simply do something like:
As well as support for Amazon S3 by setting the DRIVER to s3.
These helper functions are added functions to the builtin Python functions which can be
used by simply calling them as usual:
Notice how we never imported anything from the module or Service Container. See the 
Helper Functions documentation for a more exhaustive list
Very often you will want to have a single variable accessible in all of your views, such as
the Request object or other class. We can use the new View class for this and put it in
it's own service provider:<html>
  <body>
      <form action="/upload" method="POST" 
enctype="multipart/formdata">
        <input type="file" name="file">
      </form>
  </body>
</html>
def show(self, Upload):
    Upload.store(Request.input('file'))
def show(self):
    return view('welcome')
Added several helper functions
Added a way to have global template variables6/12/25, 3:02 AM Masonite Documentation