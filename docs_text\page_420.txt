=== Page 421 ===

421Masonite 1.3 to 1.4
Masonite 1.4 brings several new features and a few new files. This is a very simple
upgrade and most of the changes were done in the pip package of Masonite. The
upgrade from 1.3 to 1.4 should take less than 10 minutes
This requirement file has the masonite>=1.3,<=1.3.99 requirement. This should be
changed to masonite>=1.4,<=1.4.99. You should also run pip install --upgrade -
r requirements.txt to upgrade the Masonite pip package.
There is now a new cache folder under bootstrap/cache which will be used to store
any cached files you use with the caching feature. Simply create a new 
bootstrap/cache folder and optionally put a .gitignore file in it so your source
control will pick it up.
Masonite 1.4 brings a new config/cache.py and config/broadcast.py files. These
files can be found on the GitHub page and can be copied and pasted into your project.
Take a look at the new config/cache.py file and the config/broadcast.py file. Just
copy and paste those configuration files into your project.
Introduction
Requirements.txt File
New Cache Folder
New Cache and Broadcast Configuration
3 New Service Providers6/12/25, 3:02 AM Masonite Documentation