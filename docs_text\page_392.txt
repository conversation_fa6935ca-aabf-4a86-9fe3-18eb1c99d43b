=== Page 393 ===

393Previously the port was missing from the database configuration settings. This was fine
when using the default connection but did not work unless added to the config.
Instead of doing something like:
We can now use a dictionary:
We can now specify a route with multiple HTTP methods. This can be done like so:
Core can now emit events that can be listened to through the container.def show(self, request: Request):
    request.header('key1', 'value1')
    request.header('key2', 'value2')
def show(self, request: Request):
    request.header({
        'key1': 'value1',
        'key2': 'value2'
    })
from masonite.routes import Match
Match(['GET', 'POST']).route('/url', 'SomeController@show')Added ability to use a dictionary for setting
headers.
Added a new Match route
Added Masonite Events into core6/12/25, 3:02 AM Masonite Documentation