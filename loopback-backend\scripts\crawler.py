#!/usr/bin/env python3
"""
Website Crawler Script
Crawls websites and extracts content for document generation
"""

import argparse
import json
import sys
import time
import signal
import requests
from urllib.parse import urljoin, urlparse, urlunparse
from urllib.robotparser import RobotFileParser
import hashlib
import re
from typing import Set, List, Dict, Optional
from dataclasses import dataclass
from bs4 import BeautifulSoup
import markdownify
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CrawlOptions:
    max_depth: int = 2
    max_pages: int = 100
    allowed_content_types: List[str] = None
    exclude_patterns: List[str] = None
    include_patterns: List[str] = None
    follow_external_links: bool = True
    respect_robots_txt: bool = False
    delay_between_requests: int = 1000
    user_agent: str = "WebsiteCrawler/1.0"
    timeout: int = 30

    def __post_init__(self):
        if self.allowed_content_types is None:
            self.allowed_content_types = ['text/html']
        if self.exclude_patterns is None:
            self.exclude_patterns = []
        if self.include_patterns is None:
            self.include_patterns = []

class WebsiteCrawler:
    def __init__(self, job_id: str, start_url: str, options: CrawlOptions, callback_url: str):
        self.job_id = job_id
        self.start_url = start_url
        self.options = options
        self.callback_url = callback_url
        
        self.visited_urls: Set[str] = set()
        self.to_visit: List[tuple] = [(start_url, 0, None)]  # (url, depth, parent_url)
        self.processed_pages = 0
        self.failed_pages = 0
        self.total_pages = 0
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': options.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        self.robots_parser = None
        if options.respect_robots_txt:
            self.setup_robots_parser()
        
        self.paused = False
        signal.signal(signal.SIGUSR1, self.pause_handler)
        signal.signal(signal.SIGUSR2, self.resume_handler)

    def setup_robots_parser(self):
        """Setup robots.txt parser"""
        try:
            robots_url = urljoin(self.start_url, '/robots.txt')
            self.robots_parser = RobotFileParser()
            self.robots_parser.set_url(robots_url)
            self.robots_parser.read()
        except Exception as e:
            logger.warning(f"Could not read robots.txt: {e}")

    def pause_handler(self, signum, frame):
        """Handle pause signal"""
        self.paused = True
        logger.info("Crawler paused")

    def resume_handler(self, signum, frame):
        """Handle resume signal"""
        self.paused = False
        logger.info("Crawler resumed")

    def is_allowed_by_robots(self, url: str) -> bool:
        """Check if URL is allowed by robots.txt"""
        if not self.robots_parser:
            return True
        return self.robots_parser.can_fetch(self.options.user_agent, url)

    def should_crawl_url(self, url: str, depth: int) -> bool:
        """Determine if URL should be crawled"""
        # Check depth limit
        if depth > self.options.max_depth:
            return False
        
        # Check if already visited
        if url in self.visited_urls:
            return False
        
        # Check robots.txt
        if not self.is_allowed_by_robots(url):
            return False
        
        # Check external links
        if not self.options.follow_external_links:
            start_domain = urlparse(self.start_url).netloc
            url_domain = urlparse(url).netloc
            if start_domain != url_domain:
                return False
        
        # Check exclude patterns
        for pattern in self.options.exclude_patterns:
            if re.search(pattern, url):
                return False
        
        # Check include patterns (if any)
        if self.options.include_patterns:
            for pattern in self.options.include_patterns:
                if re.search(pattern, url):
                    break
            else:
                return False
        
        return True

    def extract_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract all links from the page"""
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            absolute_url = urljoin(base_url, href)
            
            # Clean up the URL
            parsed = urlparse(absolute_url)
            clean_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, parsed.query, ''  # Remove fragment
            ))
            
            if clean_url not in links:
                links.append(clean_url)
        
        return links

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract all image URLs from the page"""
        images = []
        
        for img in soup.find_all('img', src=True):
            src = img['src']
            absolute_url = urljoin(base_url, src)
            if absolute_url not in images:
                images.append(absolute_url)
        
        return images

    def extract_content(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract text content from the page"""
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get title
        title_tag = soup.find('title')
        title = title_tag.get_text().strip() if title_tag else 'Untitled'
        
        # Get main content
        # Try to find main content areas
        main_content = None
        for selector in ['main', 'article', '.content', '#content', '.main', '#main']:
            main_content = soup.select_one(selector)
            if main_content:
                break
        
        if not main_content:
            main_content = soup.find('body') or soup
        
        # Extract text content
        text_content = main_content.get_text(separator='\n', strip=True)
        
        # Convert to markdown
        markdown_content = markdownify.markdownify(str(main_content), heading_style="ATX")
        
        return {
            'title': title,
            'content': text_content,
            'markdown': markdown_content
        }

    def crawl_page(self, url: str, depth: int, parent_url: Optional[str]) -> Optional[Dict]:
        """Crawl a single page"""
        try:
            logger.info(f"Crawling: {url} (depth: {depth})")
            
            response = self.session.get(url, timeout=self.options.timeout)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('content-type', '').split(';')[0]
            if content_type not in self.options.allowed_content_types:
                logger.info(f"Skipping {url} - unsupported content type: {content_type}")
                return None
            
            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract content
            content_data = self.extract_content(soup)
            
            # Extract links for further crawling
            links = self.extract_links(soup, url)
            images = self.extract_images(soup, url)
            
            # Add new links to crawl queue
            for link in links:
                if self.should_crawl_url(link, depth + 1):
                    self.to_visit.append((link, depth + 1, url))
            
            # Prepare content data
            crawled_content = {
                'url': url,
                'title': content_data['title'],
                'content': content_data['content'],
                'htmlContent': str(soup),
                'markdownContent': content_data['markdown'],
                'contentType': content_type,
                'depth': depth,
                'contentLength': len(content_data['content']),
                'statusCode': response.status_code,
                'extractedLinks': links,
                'extractedImages': images,
                'metadata': {
                    'description': self.get_meta_description(soup),
                    'keywords': self.get_meta_keywords(soup),
                    'author': self.get_meta_author(soup),
                },
                'headers': dict(response.headers),
                'parentUrl': parent_url,
                'processingTimeMs': 0,  # Will be calculated by caller
            }
            
            return crawled_content
            
        except Exception as e:
            logger.error(f"Error crawling {url}: {e}")
            self.failed_pages += 1
            return None

    def get_meta_description(self, soup: BeautifulSoup) -> str:
        """Extract meta description"""
        meta = soup.find('meta', attrs={'name': 'description'})
        return meta.get('content', '') if meta else ''

    def get_meta_keywords(self, soup: BeautifulSoup) -> str:
        """Extract meta keywords"""
        meta = soup.find('meta', attrs={'name': 'keywords'})
        return meta.get('content', '') if meta else ''

    def get_meta_author(self, soup: BeautifulSoup) -> str:
        """Extract meta author"""
        meta = soup.find('meta', attrs={'name': 'author'})
        return meta.get('content', '') if meta else ''

    def send_progress_update(self):
        """Send progress update to callback URL"""
        progress_data = {
            'jobId': self.job_id,
            'status': 'running',
            'processedPages': self.processed_pages,
            'totalPages': len(self.to_visit) + self.processed_pages,
            'failedPages': self.failed_pages,
        }
        
        print(f"PROGRESS:{json.dumps(progress_data)}")
        sys.stdout.flush()

    def send_content_data(self, content_data: Dict):
        """Send crawled content data"""
        print(f"CONTENT:{json.dumps(content_data)}")
        sys.stdout.flush()

    def crawl(self):
        """Main crawling loop"""
        logger.info(f"Starting crawl of {self.start_url}")
        
        while self.to_visit and self.processed_pages < self.options.max_pages:
            # Check if paused
            while self.paused:
                time.sleep(1)
            
            url, depth, parent_url = self.to_visit.pop(0)
            
            if url in self.visited_urls:
                continue
            
            self.visited_urls.add(url)
            
            start_time = time.time()
            content_data = self.crawl_page(url, depth, parent_url)
            processing_time = int((time.time() - start_time) * 1000)
            
            if content_data:
                content_data['processingTimeMs'] = processing_time
                self.send_content_data(content_data)
                self.processed_pages += 1
            
            # Send progress update
            self.send_progress_update()
            
            # Delay between requests
            if self.options.delay_between_requests > 0:
                time.sleep(self.options.delay_between_requests / 1000.0)
        
        logger.info(f"Crawl completed. Processed {self.processed_pages} pages, failed {self.failed_pages}")

def main():
    parser = argparse.ArgumentParser(description='Website Crawler')
    parser.add_argument('--job-id', required=True, help='Crawl job ID')
    parser.add_argument('--url', required=True, help='Starting URL')
    parser.add_argument('--options', required=True, help='Crawl options JSON')
    parser.add_argument('--callback-url', required=True, help='Callback URL for updates')
    
    args = parser.parse_args()
    
    try:
        options_dict = json.loads(args.options)
        options = CrawlOptions(**options_dict)
        
        crawler = WebsiteCrawler(args.job_id, args.url, options, args.callback_url)
        crawler.crawl()
        
    except Exception as e:
        logger.error(f"Crawler failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
