=== Page 88 ===

88Get the URL to a location:
To help in debugging, you can use the dd() helper
<PERSON><PERSON><PERSON> allows adding filters to your views. Before we explain how to add filters to all of
your templates, let's explain exactly what a view filter is.
Filters can be attached to view variables in order to change and modify them. For
example you may have a variable that you want to turn into a slug and have something
like:
In Masonite, this slug filter is simply a function that takes the variable as an argument
and would look like a simple function like this:<h2> Token: {{ cookie('token') }}</h2>
<form action="{{ url('/about', full=True) }}" method="POST">
</form>
{{ dd(variable) }}
{{ variable|slug }}
def slug(variable):
    return variable.replace(' ', '-')Url
DD
View Filters6/12/25, 3:02 AM Masonite Documentation