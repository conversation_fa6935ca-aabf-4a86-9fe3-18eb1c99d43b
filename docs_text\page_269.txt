=== Page 270 ===

270Used to make sure that value is a valid file.
Additionally you can check file size, with different file size formats:
Finally file type can be checked through a MIME types list:
You can combine all those file checks at once:"""
{
  'email': '<EMAIL>',
  'terms': 'on'
  'age': 18
}
"""
validate.when(
    validate.exists('terms')
).then(
    validate.greater_than('age', 18)
)
"""
{
  'document': '/my/doc.pdf'
}
"""
validate.file('document')
validate.file('document', 1024) # check valid file and max size is 1 
Kilobyte (1024 bytes)
validate.file('document', '1K') # check valid file and max size is 1 
Kilobyte (1024 bytes), 1k or 1KB also works
validate.file('document', '15M') # check valid file and max size is 15 
Megabytes
validate.file('document', mimes=['jpg', 'png'])File6/12/25, 3:02 AM Masonite Documentation