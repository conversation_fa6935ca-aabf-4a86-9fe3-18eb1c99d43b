=== Page 411 ===

411Parsing the query to the original way is no longer possible. This also comes with a 
query_parse helper which you can use to parse a query string the same way Masonite
does.
The container has a helpful collect method which allows you to collect all the classes
in the container with a certain key or an instance of a class like this:
Will collect everything in the container where the binding key ends with Command.
You can also collect everything that is a subclass:
This will collect everything that is a subclass of Responsable
This has now been expanded to also include instances of. So it will work for objects now
and not just classes.{
    "email": "<EMAIL>",
    "filter": {
        "name": "<PERSON>",
        "user": "bob"
    }
}
app.collect('*Command')
from masonite.response import Responsable
app.collect(Responsable)Improved Container Collection
Moved The masonite/ Directory To The 
src/masonite Directory6/12/25, 3:02 AM Masonite Documentation