{"ast": null, "code": "import { RateLimitService } from '../../services/rate-limit.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/rate-limit.service\";\nimport * as i2 from \"@angular/common\";\nfunction RateLimitNotificationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"i\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"h6\", 5);\n    i0.ɵɵelement(5, \"i\", 6);\n    i0.ɵɵtext(6, \"Rate Limit Exceeded \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 7);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"span\", 2)(13, \"strong\");\n    i0.ɵɵtext(14, \"Time Remaining:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 10);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 11);\n    i0.ɵɵelement(19, \"div\", 12);\n    i0.ɵɵpipe(20, \"async\");\n    i0.ɵɵpipe(21, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"small\", 13);\n    i0.ɵɵelement(23, \"i\", 14);\n    i0.ɵɵtext(24, \" Rate limiting helps protect our service. Thank you for your patience! \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 15);\n    i0.ɵɵpipe(26, \"async\");\n    i0.ɵɵlistener(\"click\", function RateLimitNotificationComponent_div_0_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.dismissNotification());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((tmp_1_0 = i0.ɵɵpipeBind1(9, 8, ctx_r1.rateLimit$)) == null ? null : tmp_1_0.message);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatTime(((tmp_2_0 = i0.ɵɵpipeBind1(17, 10, ctx_r1.rateLimit$)) == null ? null : tmp_2_0.remainingTime) || 0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage(), \"%\");\n    i0.ɵɵattribute(\"aria-valuenow\", (tmp_4_0 = i0.ɵɵpipeBind1(20, 12, ctx_r1.rateLimit$)) == null ? null : tmp_4_0.remainingTime)(\"aria-valuemin\", 0)(\"aria-valuemax\", (tmp_6_0 = i0.ɵɵpipeBind1(21, 14, ctx_r1.rateLimit$)) == null ? null : tmp_6_0.retryAfter);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ((tmp_7_0 = i0.ɵɵpipeBind1(26, 16, ctx_r1.rateLimit$)) == null ? null : tmp_7_0.remainingTime) > 0);\n  }\n}\nexport class RateLimitNotificationComponent {\n  constructor(rateLimitService) {\n    this.rateLimitService = rateLimitService;\n    this.rateLimit$ = this.rateLimitService.rateLimit$;\n  }\n  ngOnInit() {\n    // Component initialization\n  }\n  ngOnDestroy() {\n    // Cleanup if needed\n  }\n  /**\n   * Format time for display\n   */\n  formatTime(seconds) {\n    return RateLimitService.formatTimeRemaining(seconds);\n  }\n  /**\n   * Get progress percentage for progress bar\n   */\n  getProgressPercentage() {\n    const status = this.rateLimitService.getCurrentStatus();\n    if (status.retryAfter === 0) return 0;\n    const elapsed = status.retryAfter - status.remainingTime;\n    return elapsed / status.retryAfter * 100;\n  }\n  /**\n   * Dismiss notification (only when countdown is finished)\n   */\n  dismissNotification() {\n    const status = this.rateLimitService.getCurrentStatus();\n    if (status.remainingTime <= 0) {\n      this.rateLimitService.clearRateLimit();\n    }\n  }\n  static #_ = this.ɵfac = function RateLimitNotificationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RateLimitNotificationComponent)(i0.ɵɵdirectiveInject(i1.RateLimitService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RateLimitNotificationComponent,\n    selectors: [[\"app-rate-limit-notification\"]],\n    decls: 2,\n    vars: 3,\n    consts: [[\"class\", \"rate-limit-notification alert alert-warning d-flex align-items-center\", \"role\", \"alert\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"rate-limit-notification\", \"alert\", \"alert-warning\", \"d-flex\", \"align-items-center\"], [1, \"me-3\"], [1, \"fas\", \"fa-clock\", \"fa-2x\", \"text-warning\"], [1, \"flex-grow-1\"], [1, \"alert-heading\", \"mb-2\"], [1, \"fas\", \"fa-ban\", \"me-2\"], [1, \"mb-2\"], [1, \"rate-limit-countdown\"], [1, \"d-flex\", \"align-items-center\"], [1, \"badge\", \"bg-danger\", \"ms-2\", \"fs-6\"], [1, \"progress\", \"flex-grow-1\", 2, \"height\", \"6px\"], [\"role\", \"progressbar\", 1, \"progress-bar\", \"progress-bar-striped\", \"progress-bar-animated\", \"bg-warning\"], [1, \"text-muted\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\", \"disabled\"]],\n    template: function RateLimitNotificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, RateLimitNotificationComponent_div_0_Template, 27, 18, \"div\", 0);\n        i0.ɵɵpipe(1, \"async\");\n      }\n      if (rf & 2) {\n        let tmp_0_0;\n        i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = i0.ɵɵpipeBind1(1, 1, ctx.rateLimit$)) == null ? null : tmp_0_0.isRateLimited);\n      }\n    },\n    dependencies: [i2.NgIf, i2.AsyncPipe],\n    styles: [\".rate-limit-notification[_ngcontent-%COMP%] {\\n      position: fixed;\\n      top: 20px;\\n      left: 20px;\\n      right: 20px;\\n      z-index: 9999;\\n      max-width: 600px;\\n      margin: 0 auto;\\n      box-shadow: 0 4px 12px rgba(0,0,0,0.15);\\n      border: 2px solid #ffc107;\\n      border-radius: 8px;\\n      animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_slideDown {\\n      from {\\n        transform: translateY(-100%);\\n        opacity: 0;\\n      }\\n      to {\\n        transform: translateY(0);\\n        opacity: 1;\\n      }\\n    }\\n\\n    .rate-limit-countdown[_ngcontent-%COMP%] {\\n      margin-top: 12px;\\n    }\\n\\n    .progress[_ngcontent-%COMP%] {\\n      background-color: rgba(255, 193, 7, 0.2);\\n    }\\n\\n    .badge[_ngcontent-%COMP%] {\\n      font-family: 'Courier New', monospace;\\n      letter-spacing: 0.5px;\\n    }\\n\\n    .btn-close[_ngcontent-%COMP%]:disabled {\\n      opacity: 0.3;\\n      cursor: not-allowed;\\n    }\\n\\n    @media (max-width: 576px) {\\n      .rate-limit-notification[_ngcontent-%COMP%] {\\n        left: 10px;\\n        right: 10px;\\n        top: 10px;\\n      }\\n      \\n      .d-flex.align-items-center[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        align-items: flex-start !important;\\n      }\\n      \\n      .progress[_ngcontent-%COMP%] {\\n        width: 100%;\\n        margin-top: 8px;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["RateLimitService", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "RateLimitNotificationComponent_div_0_Template_button_click_25_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "dismissNotification", "ɵɵadvance", "ɵɵtextInterpolate", "tmp_1_0", "ɵɵpipeBind1", "rateLimit$", "message", "ɵɵtextInterpolate1", "formatTime", "tmp_2_0", "remainingTime", "ɵɵstyleProp", "getProgressPercentage", "ɵɵproperty", "tmp_7_0", "RateLimitNotificationComponent", "constructor", "rateLimitService", "ngOnInit", "ngOnDestroy", "seconds", "formatTimeRemaining", "status", "getCurrentStatus", "retryAfter", "elapsed", "clearRateLimit", "_", "ɵɵdirectiveInject", "i1", "_2", "selectors", "decls", "vars", "consts", "template", "RateLimitNotificationComponent_Template", "rf", "ctx", "ɵɵtemplate", "RateLimitNotificationComponent_div_0_Template", "tmp_0_0", "isRateLimited"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\rate-limit-notification\\rate-limit-notification.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { RateLimitService, RateLimitStatus } from '../../services/rate-limit.service';\r\n\r\n@Component({\r\n  selector: 'app-rate-limit-notification',\r\n  template: `\r\n    <div *ngIf=\"(rateLimit$ | async)?.isRateLimited\" \r\n         class=\"rate-limit-notification alert alert-warning d-flex align-items-center\"\r\n         role=\"alert\">\r\n      <div class=\"me-3\">\r\n        <i class=\"fas fa-clock fa-2x text-warning\"></i>\r\n      </div>\r\n      <div class=\"flex-grow-1\">\r\n        <h6 class=\"alert-heading mb-2\">\r\n          <i class=\"fas fa-ban me-2\"></i>Rate Limit Exceeded\r\n        </h6>\r\n        <p class=\"mb-2\">{{ (rateLimit$ | async)?.message }}</p>\r\n        <div class=\"rate-limit-countdown\">\r\n          <div class=\"d-flex align-items-center\">\r\n            <span class=\"me-3\">\r\n              <strong>Time Remaining:</strong>\r\n              <span class=\"badge bg-danger ms-2 fs-6\">\r\n                {{ formatTime((rateLimit$ | async)?.remainingTime || 0) }}\r\n              </span>\r\n            </span>\r\n            <div class=\"progress flex-grow-1\" style=\"height: 6px;\">\r\n              <div class=\"progress-bar progress-bar-striped progress-bar-animated bg-warning\" \r\n                   role=\"progressbar\" \r\n                   [style.width.%]=\"getProgressPercentage()\"\r\n                   [attr.aria-valuenow]=\"(rateLimit$ | async)?.remainingTime\"\r\n                   [attr.aria-valuemin]=\"0\"\r\n                   [attr.aria-valuemax]=\"(rateLimit$ | async)?.retryAfter\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <small class=\"text-muted\">\r\n          <i class=\"fas fa-info-circle me-1\"></i>\r\n          Rate limiting helps protect our service. Thank you for your patience!\r\n        </small>\r\n      </div>\r\n      <button type=\"button\" \r\n              class=\"btn-close\" \r\n              (click)=\"dismissNotification()\" \r\n              [disabled]=\"(rateLimit$ | async)?.remainingTime! > 0\"\r\n              aria-label=\"Close\">\r\n      </button>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .rate-limit-notification {\r\n      position: fixed;\r\n      top: 20px;\r\n      left: 20px;\r\n      right: 20px;\r\n      z-index: 9999;\r\n      max-width: 600px;\r\n      margin: 0 auto;\r\n      box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n      border: 2px solid #ffc107;\r\n      border-radius: 8px;\r\n      animation: slideDown 0.3s ease-out;\r\n    }\r\n\r\n    @keyframes slideDown {\r\n      from {\r\n        transform: translateY(-100%);\r\n        opacity: 0;\r\n      }\r\n      to {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .rate-limit-countdown {\r\n      margin-top: 12px;\r\n    }\r\n\r\n    .progress {\r\n      background-color: rgba(255, 193, 7, 0.2);\r\n    }\r\n\r\n    .badge {\r\n      font-family: 'Courier New', monospace;\r\n      letter-spacing: 0.5px;\r\n    }\r\n\r\n    .btn-close:disabled {\r\n      opacity: 0.3;\r\n      cursor: not-allowed;\r\n    }\r\n\r\n    @media (max-width: 576px) {\r\n      .rate-limit-notification {\r\n        left: 10px;\r\n        right: 10px;\r\n        top: 10px;\r\n      }\r\n      \r\n      .d-flex.align-items-center {\r\n        flex-direction: column;\r\n        align-items: flex-start !important;\r\n      }\r\n      \r\n      .progress {\r\n        width: 100%;\r\n        margin-top: 8px;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class RateLimitNotificationComponent implements OnInit, OnDestroy {\r\n  rateLimit$: Observable<RateLimitStatus>;\r\n\r\n  constructor(private rateLimitService: RateLimitService) {\r\n    this.rateLimit$ = this.rateLimitService.rateLimit$;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Component initialization\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup if needed\r\n  }\r\n\r\n  /**\r\n   * Format time for display\r\n   */\r\n  formatTime(seconds: number): string {\r\n    return RateLimitService.formatTimeRemaining(seconds);\r\n  }\r\n\r\n  /**\r\n   * Get progress percentage for progress bar\r\n   */\r\n  getProgressPercentage(): number {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    if (status.retryAfter === 0) return 0;\r\n    \r\n    const elapsed = status.retryAfter - status.remainingTime;\r\n    return (elapsed / status.retryAfter) * 100;\r\n  }\r\n\r\n  /**\r\n   * Dismiss notification (only when countdown is finished)\r\n   */\r\n  dismissNotification(): void {\r\n    const status = this.rateLimitService.getCurrentStatus();\r\n    if (status.remainingTime <= 0) {\r\n      this.rateLimitService.clearRateLimit();\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,gBAAgB,QAAyB,mCAAmC;;;;;;;IAQ/EC,EAHF,CAAAC,cAAA,aAEkB,aACE;IAChBD,EAAA,CAAAE,SAAA,WAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAAyB,YACQ;IAC7BD,EAAA,CAAAE,SAAA,WAA+B;IAAAF,EAAA,CAAAI,MAAA,2BACjC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,WAAgB;IAAAD,EAAA,CAAAI,MAAA,GAAmC;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIjDH,EAHN,CAAAC,cAAA,cAAkC,cACO,eAClB,cACT;IAAAD,EAAA,CAAAI,MAAA,uBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAC,cAAA,gBAAwC;IACtCD,EAAA,CAAAI,MAAA,IACF;;IACFJ,EADE,CAAAG,YAAA,EAAO,EACF;IACPH,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAE,SAAA,eAMM;;;IAGZF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAI,MAAA,+EACF;IACFJ,EADE,CAAAG,YAAA,EAAQ,EACJ;IACNH,EAAA,CAAAC,cAAA,kBAI2B;;IAFnBD,EAAA,CAAAK,UAAA,mBAAAC,uEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,mBAAA,EAAqB;IAAA,EAAC;IAIzCZ,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;;;;IA/BcH,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,iBAAA,EAAAC,OAAA,GAAAf,EAAA,CAAAgB,WAAA,OAAAP,MAAA,CAAAQ,UAAA,oBAAAF,OAAA,CAAAG,OAAA,CAAmC;IAM3ClB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAmB,kBAAA,MAAAV,MAAA,CAAAW,UAAA,GAAAC,OAAA,GAAArB,EAAA,CAAAgB,WAAA,SAAAP,MAAA,CAAAQ,UAAA,oBAAAI,OAAA,CAAAC,aAAA,aACF;IAKKtB,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAuB,WAAA,UAAAd,MAAA,CAAAe,qBAAA,QAAyC;;IAgB9CxB,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAyB,UAAA,eAAAC,OAAA,GAAA1B,EAAA,CAAAgB,WAAA,SAAAP,MAAA,CAAAQ,UAAA,oBAAAS,OAAA,CAAAJ,aAAA,MAAqD;;;AAoEnE,OAAM,MAAOK,8BAA8B;EAGzCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAClC,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACY,gBAAgB,CAACZ,UAAU;EACpD;EAEAa,QAAQA,CAAA;IACN;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGF;;;EAGAX,UAAUA,CAACY,OAAe;IACxB,OAAOjC,gBAAgB,CAACkC,mBAAmB,CAACD,OAAO,CAAC;EACtD;EAEA;;;EAGAR,qBAAqBA,CAAA;IACnB,MAAMU,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACM,gBAAgB,EAAE;IACvD,IAAID,MAAM,CAACE,UAAU,KAAK,CAAC,EAAE,OAAO,CAAC;IAErC,MAAMC,OAAO,GAAGH,MAAM,CAACE,UAAU,GAAGF,MAAM,CAACZ,aAAa;IACxD,OAAQe,OAAO,GAAGH,MAAM,CAACE,UAAU,GAAI,GAAG;EAC5C;EAEA;;;EAGAxB,mBAAmBA,CAAA;IACjB,MAAMsB,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAACM,gBAAgB,EAAE;IACvD,IAAID,MAAM,CAACZ,aAAa,IAAI,CAAC,EAAE;MAC7B,IAAI,CAACO,gBAAgB,CAACS,cAAc,EAAE;IACxC;EACF;EAAC,QAAAC,CAAA,G;qCAzCUZ,8BAA8B,EAAA3B,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAA1C,gBAAA;EAAA;EAAA,QAAA2C,EAAA,G;UAA9Bf,8BAA8B;IAAAgB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA1GvCjD,EAAA,CAAAmD,UAAA,IAAAC,6CAAA,mBAEkB;;;;;QAFZpD,EAAA,CAAAyB,UAAA,UAAA4B,OAAA,GAAArD,EAAA,CAAAgB,WAAA,OAAAkC,GAAA,CAAAjC,UAAA,oBAAAoC,OAAA,CAAAC,aAAA,CAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}