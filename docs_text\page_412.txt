=== Page 413 ===

413method (which is used to schedule jobs).
These are really just helper methods to help bind things to the container more
expressively.
You can now schedule jobs or commands in a new expressive way. In addition to setting
the schedule interval as attributes on the Task you can now do it directly in the provider:
There are several other methods that will be documented on release.
We also changed the namespace from scheduler to masonite.scheduler. So you will
need to refactor your imports.
There are now mailable classes which you can use to wrap some of the logic of building
emails around. Instead of doing something like this:
You can now do:def register(self):
    self.call('your:command --flag').daily().at('9:00')
    self.call('your:command --flag').every_minute()
    self.schedule(YourJob()).every('3 days')
mail.to('<EMAIL>').reply_to('<EMAIL>').template('emails.r
egister').subject(..).send()
from app.mailables import RegistrationMailable
mail.mailable(RegistrationMailable()).send()New Scheduling
Namespace Change
Mailable classes6/12/25, 3:02 AM Masonite Documentation