import { Getter } from '@loopback/core';
import { DefaultCrudRepository, HasManyRepositoryFactory, BelongsToAccessor } from '@loopback/repository';
import { DbDataSource } from '../datasources';
import { CrawlJob, CrawlJobRelations, User, CrawledContent, GeneratedDocument } from '../models';
import { UserRepository } from './user.repository';
import { CrawledContentRepository } from './crawled-content.repository';
import { GeneratedDocumentRepository } from './generated-document.repository';
export declare class CrawlJobRepository extends DefaultCrudRepository<CrawlJob, typeof CrawlJob.prototype.id, CrawlJobRelations> {
    protected userRepositoryGetter: Getter<UserRepository>;
    protected crawledContentRepositoryGetter: Getter<CrawledContentRepository>;
    protected generatedDocumentRepositoryGetter: Getter<GeneratedDocumentRepository>;
    readonly user: BelongsToAccessor<User, typeof CrawlJob.prototype.id>;
    readonly crawledContents: HasManyRepositoryFactory<CrawledContent, typeof CrawlJob.prototype.id>;
    readonly generatedDocuments: HasManyRepositoryFactory<GeneratedDocument, typeof CrawlJob.prototype.id>;
    constructor(dataSource: DbDataSource, userRepositoryGetter: Getter<UserRepository>, crawledContentRepositoryGetter: Getter<CrawledContentRepository>, generatedDocumentRepositoryGetter: Getter<GeneratedDocumentRepository>);
    /**
     * Find crawl jobs by user ID
     */
    findByUserId(userId: string): Promise<CrawlJob[]>;
    /**
     * Find active crawl jobs (running or pending)
     */
    findActiveCrawlJobs(): Promise<CrawlJob[]>;
    /**
     * Find crawl jobs by status
     */
    findByStatus(status: string): Promise<CrawlJob[]>;
    /**
     * Update crawl job progress
     */
    updateProgress(id: string, processedPages: number, totalPages: number, status?: string): Promise<void>;
    /**
     * Get crawl job statistics for a user
     */
    getUserCrawlStatistics(userId: string): Promise<object>;
    /**
     * Clean up old completed crawl jobs
     */
    cleanupOldJobs(daysOld?: number): Promise<number>;
}
