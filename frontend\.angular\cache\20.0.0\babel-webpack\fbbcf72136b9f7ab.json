{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n// Components\nimport { ProfileComponent } from './profile.component';\nimport { TwoFactorSetupComponent } from '../../components/auth/two-factor/two-factor-setup.component';\nimport { TwoFactorManagementComponent } from '../../components/auth/two-factor/two-factor-management.component';\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}];\nlet ProfileModule = class ProfileModule {};\nProfileModule = __decorate([NgModule({\n  declarations: [ProfileComponent, TwoFactorSetupComponent, TwoFactorManagementComponent, Disable2FADialogComponent],\n  imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatProgressSpinnerModule, MatSnackBarModule, MatTabsModule, MatDividerModule, MatCheckboxModule, MatDialogModule, MatTooltipModule]\n})], ProfileModule);\nexport { ProfileModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "ReactiveFormsModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatTabsModule", "MatDividerModule", "MatCheckboxModule", "MatDialogModule", "MatTooltipModule", "ProfileComponent", "TwoFactorSetupComponent", "TwoFactorManagementComponent", "routes", "path", "component", "ProfileModule", "__decorate", "declarations", "Disable2FADialogComponent", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n\n// Components\nimport { ProfileComponent } from './profile.component';\nimport { TwoFactorSetupComponent } from '../../components/auth/two-factor/two-factor-setup.component';\nimport { TwoFactorManagementComponent } from '../../components/auth/two-factor/two-factor-management.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfileComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfileComponent,\n    TwoFactorSetupComponent,\n    TwoFactorManagementComponent,\n    Disable2FADialogComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    ReactiveFormsModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatTabsModule,\n    MatDividerModule,\n    MatCheckboxModule,\n    MatDialogModule,\n    MatTooltipModule\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,4BAA4B,QAAQ,kEAAkE;AAE/G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL;CACZ,CACF;AA4BM,IAAMM,aAAa,GAAnB,MAAMA,aAAa,GAAI;AAAjBA,aAAa,GAAAC,UAAA,EA1BzBxB,QAAQ,CAAC;EACRyB,YAAY,EAAE,CACZR,gBAAgB,EAChBC,uBAAuB,EACvBC,4BAA4B,EAC5BO,yBAAyB,CAC1B;EACDC,OAAO,EAAE,CACP1B,YAAY,EACZC,YAAY,CAAC0B,QAAQ,CAACR,MAAM,CAAC,EAC7BjB,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,iBAAiB,EACjBC,eAAe,EACfC,gBAAgB;CAEnB,CAAC,C,EACWO,aAAa,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}