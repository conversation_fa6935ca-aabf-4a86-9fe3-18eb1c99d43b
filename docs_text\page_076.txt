=== Page 77 ===

77You can get the value of the @user_id parameter like this:
As a convenient way to fetch the user, you can do so directly on the request class if a
user is logged in:
If the user is not authenticated then this will be set to None
You can fetch the IP address (ipv4) from which the request has been made by adding the 
IpMiddleware to the HTTP middlewares of the project:Route.get('/dashboard/@user_id', 'DashboardController@show')
from masonite.request import Request
#..
def show(self, request: Request):
  # GET /dashboard?user_id=1
  request.param('user_id') #== 1
from masonite.request import Request
#..
def show(self, request: Request):
  # GET /dashboard?user_id=1
  request.user() #== <app.User.User>User
IP Address6/12/25, 3:02 AM Masonite Documentation