=== Page 321 ===

321•assertRouteHasParameter
•assertJson
•assertJsonPath
•assertJsonExact
•assertJsonCount
•assertJsonMissing
Assert that returned response contains the given content string. Note that the
response content will be eventually decoded if required.
Assert that returned response does not contain the given content string. Note that the
response content will be eventually decoded if required.
Assert that returned response contains in order the given strings. Note that the response
content will be eventually decoded if required.
Assert that returned response has no content and the given HTTP status code. The
default status code that is asserted is 204.self.get("/").assertContains(content)
self.get("/").assertNotContains(content)
self.get("/").assertContains(string1, string2, ...)assertContains
assertNotContains
assertContainsInOrder
assertNoContent6/12/25, 3:02 AM Masonite Documentation