import { UserRepository } from '../repositories';
import { EmailService } from './email.service';
/**
 * Service for handling secure 2FA disable requests via email
 * Provides secure token-based flow for disabling 2FA when recovery codes are exhausted
 */
export declare class TwoFactorDisableService {
    private userRepository;
    private emailService;
    private readonly TOKEN_EXPIRY_HOURS;
    private readonly TOKEN_LENGTH;
    constructor(userRepository: UserRepository, emailService: EmailService);
    /**
     * Generate and send a secure 2FA disable token via email
     * @param email - User's email address
     * @param reason - Optional reason for the request (e.g., 'recovery_codes_exhausted', 'lost_device')
     * @returns Promise<void>
     */
    requestDisable2FA(email: string, reason?: string): Promise<void>;
    /**
     * Process a 2FA disable token and disable 2FA if valid
     * @param token - The disable token from the email link
     * @returns Promise<{success: boolean, message: string}>
     */
    processDisable2FA(token: string): Promise<{
        success: boolean;
        message: string;
        user?: any;
    }>;
    /**
     * Cancel/invalidate a pending 2FA disable request
     * @param userId - User ID to cancel request for
     */
    cancelDisableRequest(userId: string): Promise<void>;
    /**
     * Check if a user has a pending 2FA disable request
     * @param userId - User ID to check
     * @returns Promise<boolean>
     */
    hasPendingDisableRequest(userId: string): Promise<boolean>;
    /**
     * Generate a cryptographically secure token
     */
    private generateSecureToken;
}
