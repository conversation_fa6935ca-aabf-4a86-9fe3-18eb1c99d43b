=== Page 132 ===

132You are free to add any other methods on your policies:
Then in your service provider (as for defining gates) you should register the policies and
bind them with a model:from masonite.authorization import Policy
class PostPolicy(Policy):
    def create(self, user):
        return False
    def view_any(self, user):
        return False
    def view(self, user, instance):
        return False
    def update(self, user, instance):
        return False
    def delete(self, user, instance):
        return False
    def force_delete(self, user, instance):
        return False
    def restore(self, user, instance):
        return False
from masonite.authorization import Policy
class PostPolicy(Policy):
    #.. 
    def publish(self, user):
        return user.email == "<EMAIL>"
Registering Policies6/12/25, 3:02 AM Masonite Documentation