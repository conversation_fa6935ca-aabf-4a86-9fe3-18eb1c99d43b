=== Page 224 ===

224A command with modified options will look like this:
If you configurations are setup properly, when jobs fail, they will go into a failed jobs table.
This is where you can monitor why your jobs are failing and choose to rerun them or
remove them.
If you choose to rerun your jobs, they will be placed back onto the queue at the end and
rerun with the normal job queuing process.
To rerun jobs that failed you can use the command:
You can specify a few options as well:--poll 5Specifies the time in seconds to wait to fetch
new jobs. Default is 1 second.
--attempts 5Specifies the number of attempts to retry a job
before considering it a failed job. Default is 3
times.
$ python craft queue:work --driver database --connection mysql --poll 5 
--attempts 2
$ python craft queue:retry
Option Description
--driver databaseSpecifies which driver to use to find the failed
jobs.
--queue invoicesSpecifis which queue to put the failed jobs
back onto the queue with.
--connection mysqlSpecifies the connection to use to fetch the
failed jobs.Failed Jobs6/12/25, 3:02 AM Masonite Documentation