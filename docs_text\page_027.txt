=== Page 28 ===

28Go ahead and run the server and head over the http://localhost:8000/post/1 route
and then http://localhost:8000/post/2 and see how the posts are different.
By now, all of the logic we have gone over so far will take you a long way so let's just
finish up quickly with updating and deleting posts. We'll assume you are comfortable with
what we have learned so far so we will run through this faster since this is just more of
what were doing in the previous parts.
Let's just make an update method on the PostController:
Since we are more comfortable with controllers we can go ahead and make two at once.
We made one that shows a view that shows a form to update a post and then one that
actually updates the post with the database.app/controllers/PostController.py
def update(self, view: View, request: Request):
    post = Post.find(request.param('id'))
    return view.render('update', {'post': post})
def store(self, request: Request):
    post = Post.find(request.param('id'))
    post.title = request.input('title')
    post.body = request.input('body')
    post.save()
    return 'post updated'Updating and Deleting Posts
Update Controller Method
Create The View6/12/25, 3:02 AM Masonite Documentation