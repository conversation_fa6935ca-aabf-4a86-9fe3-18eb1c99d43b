=== Page 234 ===

234To get back the session data you set you can simply using the "get" method:
You can check if a session has a specific key:
You can also delete a key from the session
You can reset all data in a session:from masonite.sessions import Session
def store(self, session: Session):
  data = session.get('key')
from masonite.sessions import Session
def store(self, session: Session):
  if session.has('key'):
    pass
from masonite.sessions import Session
def store(self, session: Session):
  session.delete('key')Retrieving Session Data
Checking For Existence
Deleting Session Data
Resetting the Session6/12/25, 3:02 AM Masonite Documentation