=== Page 325 ===

325Assert that the request contains the given cookie name and is expired.
Assert that the request does not contain the given cookie.
Assert that the session contains the given key and value (if provided).
Assert that the session does not contain the given key.
Assert that the session contains an errors key or contains the given list of keys in 
errors key.self.get("/").assertCookieExpired(name)
self.get("/").assertCookieMissing(name)
self.get("/").assertSessionHas(name, value=None)
self.get("/").assertSessionMissing(name)
self.get("/").assertSessionHasErrors()
self.get("/").assertSessionHasErrors(["email", "first_name"])assertCookieMissing
assertSessionHas
assertSessionMissing
assertSessionHasErrors
assertSessionHasNoErrors6/12/25, 3:02 AM Masonite Documentation