=== Page 168 ===

168Facades
Facades are an easy way to access the Service Container classes without making them
from the Service Container.
Facades are just a shortcut to resolve a key in the Service Container. Instead of doing:
you can do:
To import any built-in facades you can import them from the masonite.facades
namespace:
Masonite ships with several facades you can use out of the box:
•Auth
•Broadcast
•Config
•Dumpapplication.make("mail").send()
from masonite.facades import Mail
Mail.send()
from masonite.facades import Request
def show(self):
    avatar = Request.input('avatar_url')Overview
Built-in Facades6/12/25, 3:02 AM Masonite Documentation