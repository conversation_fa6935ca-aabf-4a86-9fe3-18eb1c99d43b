=== Page 293 ===

293R<PERSON><PERSON>ber not to call it and only reference the function. The Service Container needs to
inject dependencies into the object so it requires a reference and not a callable.
This will fetch all of the parameters of randomFunction and retrieve them from the
service container. There probably won't be many times you'll have to resolve your own
code but the option is there.
Sometimes you may wish to resolve your code in addition to passing in variables within
the same parameter list. For example you may want to have 3 parameters like this:
You can resolve and pass parameter at the same time by adding them to the resolve()
method:
Masonite will go through each parameter list and resolve them, if it does not find the
parameter it will pull it from the other parameters specified. These parameters can be in
any order.from masonite.request import Request
from masonite.view import View
def randomFunction(view: View):
    print(view)
def show(self, request: Request):
    request.app.resolve(randomFunction) # Will print the View object
from masonite.request import Request
from masonite import Mail
def send_email(request: Request, mail: Mail, email):
    pass
app.resolve(send_email, '<EMAIL>')Resolving With Additional Parameters
Using the container outside of Masonite flow6/12/25, 3:02 AM Masonite Documentation