"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthAuthorizationCodeRepository = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const models_1 = require("../models");
const datasources_1 = require("../datasources");
const core_1 = require("@loopback/core");
let OAuthAuthorizationCodeRepository = class OAuthAuthorizationCodeRepository extends repository_1.DefaultCrudRepository {
    constructor(dataSource) {
        super(models_1.OAuthAuthorizationCode, dataSource);
    }
    /**
     * Clean up expired authorization codes
     */
    async cleanupExpiredCodes() {
        const now = new Date();
        await this.deleteAll({
            expiresAt: { lt: now }
        });
    }
    /**
     * Find and mark code as used
     */
    async findAndUseCode(code) {
        const authCode = await this.findOne({
            where: {
                code,
                used: false,
                expiresAt: { gt: new Date() }
            }
        });
        if (authCode) {
            // Mark as used
            await this.updateById(code, { used: true });
        }
        return authCode;
    }
};
exports.OAuthAuthorizationCodeRepository = OAuthAuthorizationCodeRepository;
exports.OAuthAuthorizationCodeRepository = OAuthAuthorizationCodeRepository = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)('datasources.db')),
    tslib_1.__metadata("design:paramtypes", [datasources_1.DbDataSource])
], OAuthAuthorizationCodeRepository);
//# sourceMappingURL=oauth-authorization-code.repository.js.map