=== Page 24 ===

24Notice that we now used request: Request here. This is the Request object. Where
did this come from? This is the power and beauty of Mason<PERSON> and your first introduction
to the Service Container. The Service Container is an extremely powerful implementation
as allows you to ask <PERSON><PERSON> for an object (in this case Request) and get that object.
This is an important concept to grasp so be sure to read the documentation further.
Also notice we used an input() method. Masonite does not discriminate against
different request methods so getting input on a GET or a POST request are done exactly
the same way by using this input method.
Go ahead and run the server using craft serve again and head over to 
http://localhost:8000/blog and create a post. This should hit the /blog/create
route with the POST request method and we should see "post created".
Lets go ahead and show how we can show the posts we just created. Now that we are
more comfortabale using the framework, in this part we will create 2 new templates to
show all posts and an individual post.
Let's create 2 new templates.
•templates/posts.html
•templates/single.html
Let's start with showing all posts
Let's create a controller for the posts to separate it out from the BlogController.
terminalShowing Our Posts
Creating The Templates
Creating The Controller6/12/25, 3:02 AM Masonite Documentation