=== Page 268 ===

268Used for running a set of rules when a set of rules does not match. Has a then()
method as well. Can be seen as the opposite of when."""
{
  'users': ['mark', 'joe', 'joe']
}
"""
validate.distinct('users')  # would fail
"""
{
  'users': [
       {
            'id': 1,
            'name': 'joe'
       },
       {
            'id': 2,
            'name': 'mark'
       },
  ]
}
"""
validate.distinct('users.*.id')  # would pass
"""
{
  'age': 15,
  'email': '<EMAIL>',
  'terms': 'on'
}
"""
validate.does_not(
    validate.exists('user')
).then(
    validate.accepted('terms'),
)Does_not
Email6/12/25, 3:02 AM Masonite Documentation