=== Page 445 ===

445That is all the main changes in 2.1. Go ahead and run your server and you should be good
to go. For a more up to date list on small improvements that you can make in your
application be sure to checkout the Whats New in 2.1 documentation article.
Although not a critical upgrade, it would be a good idea to replace all instances of
retrieval of environment variables with the new masonite.env function.
Change all instances of this:
with the new env function:from masonite import Cache
def show(self, cache: Cache):
    # From
    cache.cache_exists('key')
    # To
    cache.exists('key')
import os
..
DRIVER = os.getenv('key', 'default')
..
KEY = os.envrion.get('key', 'default')
from masonite import env
..
DRIVER = env('key', 'default')
..
KEY = env('key', 'default')Environment Variables6/12/25, 3:02 AM Masonite Documentation