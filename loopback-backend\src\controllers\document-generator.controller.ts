import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {inject} from '@loopback/core';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {GeneratedDocument} from '../models';
import {GeneratedDocumentRepository, CrawlJobRepository, CrawledContentRepository} from '../repositories';
import {DocumentGeneratorService, DocumentGenerationOptions} from '../services';

export class DocumentGeneratorController {
  constructor(
    @repository(GeneratedDocumentRepository)
    public generatedDocumentRepository: GeneratedDocumentRepository,
    @repository(CrawlJobRepository)
    public crawlJobRepository: CrawlJobRepository,
    @repository(CrawledContentRepository)
    public crawledContentRepository: CrawledContentRepository,
    @inject('services.DocumentGeneratorService')
    public documentGeneratorService: DocumentGeneratorService,
  ) {}

  @post('/document-generator/generate')
  @response(200, {
    description: 'Generate document from crawled content',
    content: {'application/json': {schema: getModelSchemaRef(GeneratedDocument)}},
  })
  @authenticate('jwt')
  async generateDocument(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['crawlJobId', 'format', 'organizationType', 'selectedContentIds'],
            properties: {
              crawlJobId: {type: 'string'},
              format: {
                type: 'string',
                enum: ['pdf', 'docx', 'markdown', 'html', 'txt'],
              },
              organizationType: {
                type: 'string',
                enum: ['single_file', 'separate_files', 'grouped_folders'],
              },
              selectedContentIds: {
                type: 'array',
                items: {type: 'string'},
              },
              destinationFolder: {type: 'string'},
              includeImages: {type: 'boolean'},
              includeToc: {type: 'boolean'},
              customStyles: {type: 'object'},
              template: {type: 'string'},
              metadata: {type: 'object'},
            },
          },
        },
      },
    })
    options: DocumentGenerationOptions & {crawlJobId: string},
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<GeneratedDocument> {
    // Verify crawl job ownership
    const crawlJob = await this.crawlJobRepository.findById(options.crawlJobId);
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    // Verify crawl job is completed
    if (crawlJob.status !== 'completed') {
      throw new HttpErrors.BadRequest('Crawl job must be completed before generating documents');
    }

    // Verify selected content exists and belongs to the crawl job
    const selectedContent = await this.crawledContentRepository.find({
      where: {
        id: {inq: options.selectedContentIds},
        crawlJobId: options.crawlJobId,
      },
    });

    if (selectedContent.length !== options.selectedContentIds.length) {
      throw new HttpErrors.BadRequest('Some selected content items do not exist or do not belong to this crawl job');
    }

    // Generate document
    return this.documentGeneratorService.generateDocument(
      options.crawlJobId,
      currentUser.id,
      options,
    );
  }

  @get('/document-generator/documents')
  @response(200, {
    description: 'Array of GeneratedDocument model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GeneratedDocument, {includeRelations: true}),
        },
      },
    },
  })
  @authenticate('jwt')
  async findGeneratedDocuments(
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @param.filter(GeneratedDocument) filter?: Filter<GeneratedDocument>,
  ): Promise<GeneratedDocument[]> {
    // Filter by current user
    const userFilter = {
      ...filter,
      where: {
        ...filter?.where,
        userId: currentUser.id,
      },
    };

    return this.generatedDocumentRepository.find(userFilter);
  }

  @get('/document-generator/documents/{id}')
  @response(200, {
    description: 'GeneratedDocument model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GeneratedDocument, {includeRelations: true}),
      },
    },
  })
  @authenticate('jwt')
  async findGeneratedDocumentById(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @param.filter(GeneratedDocument, {exclude: 'where'}) filter?: FilterExcludingWhere<GeneratedDocument>,
  ): Promise<GeneratedDocument> {
    const document = await this.generatedDocumentRepository.findById(id, filter);
    
    // Check ownership
    if (document.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return document;
  }

  @get('/document-generator/documents/{id}/progress')
  @response(200, {
    description: 'Get document generation progress',
  })
  @authenticate('jwt')
  async getGenerationProgress(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<object> {
    const document = await this.generatedDocumentRepository.findById(id);
    
    // Check ownership
    if (document.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return this.documentGeneratorService.getGenerationProgress(id);
  }

  @post('/document-generator/documents/{id}/cancel')
  @response(200, {
    description: 'Cancel document generation',
  })
  @authenticate('jwt')
  async cancelGeneration(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<{message: string}> {
    const document = await this.generatedDocumentRepository.findById(id);
    
    // Check ownership
    if (document.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    if (document.status !== 'generating' && document.status !== 'pending') {
      throw new HttpErrors.BadRequest('Document generation cannot be cancelled in current status');
    }

    await this.documentGeneratorService.cancelGeneration(id);
    
    return {message: 'Document generation cancelled successfully'};
  }

  @get('/document-generator/documents/{id}/download')
  @response(200, {
    description: 'Download generated document',
    content: {
      'application/octet-stream': {
        schema: {type: 'string', format: 'binary'},
      },
    },
  })
  @authenticate('jwt')
  async downloadDocument(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<object> {
    const document = await this.generatedDocumentRepository.findById(id);
    
    // Check ownership
    if (document.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    if (document.status !== 'completed') {
      throw new HttpErrors.BadRequest('Document is not ready for download');
    }

    if (!document.filePath) {
      throw new HttpErrors.NotFound('Document file not found');
    }

    // Record download
    await this.generatedDocumentRepository.recordDownload(id);

    // Return download information
    return {
      filename: document.filename,
      filePath: document.filePath,
      fileSize: document.fileSize,
      downloadUrl: document.downloadUrl,
    };
  }

  @del('/document-generator/documents/{id}')
  @response(204, {
    description: 'GeneratedDocument DELETE success',
  })
  @authenticate('jwt')
  async deleteGeneratedDocument(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<void> {
    const document = await this.generatedDocumentRepository.findById(id);
    
    // Check ownership
    if (document.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    // Cancel generation if running
    if (document.status === 'generating' || document.status === 'pending') {
      await this.documentGeneratorService.cancelGeneration(id);
    }

    // Delete document
    await this.generatedDocumentRepository.deleteById(id);
  }

  @get('/document-generator/statistics')
  @response(200, {
    description: 'Get user document generation statistics',
  })
  @authenticate('jwt')
  async getUserStatistics(
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<object> {
    return this.generatedDocumentRepository.getUserDocumentStatistics(currentUser.id);
  }

  @post('/document-generator/content/select')
  @response(200, {
    description: 'Update content selection for document generation',
  })
  @authenticate('jwt')
  async updateContentSelection(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['crawlJobId', 'contentIds', 'isSelected'],
            properties: {
              crawlJobId: {type: 'string'},
              contentIds: {
                type: 'array',
                items: {type: 'string'},
              },
              isSelected: {type: 'boolean'},
              selectionGroup: {type: 'string'},
            },
          },
        },
      },
    })
    selectionData: {
      crawlJobId: string;
      contentIds: string[];
      isSelected: boolean;
      selectionGroup?: string;
    },
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<{message: string}> {
    // Verify crawl job ownership
    const crawlJob = await this.crawlJobRepository.findById(selectionData.crawlJobId);
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    // Update content selection
    await this.crawledContentRepository.updateSelection(
      selectionData.contentIds,
      selectionData.isSelected,
      selectionData.selectionGroup,
    );

    return {message: 'Content selection updated successfully'};
  }

  @get('/document-generator/content/{crawlJobId}/selected')
  @response(200, {
    description: 'Get selected content for document generation',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GeneratedDocument),
        },
      },
    },
  })
  @authenticate('jwt')
  async getSelectedContent(
    @param.path.string('crawlJobId') crawlJobId: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @param.query.string('selectionGroup') selectionGroup?: string,
  ): Promise<object[]> {
    // Verify crawl job ownership
    const crawlJob = await this.crawlJobRepository.findById(crawlJobId);
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return this.crawledContentRepository.findSelectedContent(crawlJobId, selectionGroup);
  }
}
