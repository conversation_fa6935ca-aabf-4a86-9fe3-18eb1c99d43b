=== Page 76 ===

76If your input is a dictionary you can access nested data in two ways. Take this code
example:
You can either access it normally:
Or you can use dot notation to fetch the value for simplicity:
You can also use a * wildcard to get all values from a dictionary list.:
Route parameters are parameters specified in a route and captured from the URL:
If you have a route like this:"""
Request Payload: 
{
"user": {
    "id": 1,
    "addresses": [
        {"id": 1, "street": "A Street"},
        {"id": 2, "street": "B Street"}
    ],
"name":{
"first":"user",
"last":"A"
}
 }
}
"""
request.input('user')['name']['last'] # A
request.input('user.name.last') # A
request.input('user.addresses.*.id') # [1,2]
Route Params6/12/25, 3:02 AM Masonite Documentation