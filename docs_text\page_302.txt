=== Page 303 ===

303Hashing
Masonite provides secure hashing for storing user passwords or other data. Bcrypt and
Argon2 protocols can be used with Masonite (default is Bcrypt).
Hashing configuration is located at config/application.py file. In this file, you can
configure which protocol to use.
You can use the Hash facade to easily hash a string (e.g. a password):
Note that you can return a hash as bytes with:HASHING = {
    "default": "bcrypt",
    "bcrypt": {"rounds": 10},
    "argon2": {"memory": 1024, "threads": 2, "time": 2},
}
from masonite.facades import Hash
Hash.make("secret") #== $2b$10$3Nm9sWFYhi.GUJ...
from masonite.facades import Hash
Hash.make_bytes("secret") #== b"$2b$10$3Nm9sWFYhi.GUJ..."Configuration
Hashing a string
Checking a string matches a Hashconfig/application.py6/12/25, 3:02 AM Masonite Documentation