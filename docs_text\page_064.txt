=== Page 65 ===

65Middleware:
•can live anywhere in your project,
•Inherit from Masonite's base middleware class
•Contain a before and after method that accepts request and response parameters
It's important to note that in order for the request lifecycle to continue, you must return
the request class. If you do not return the request class, no other middleware will run
after that middleware.
That's it! Now we just have to make sure our route picks this up. If we wanted this to
execute after a request, we could use the exact same logic in the after method
instead.
If we are using a route middleware, we'll need to specify which route should execute the
middleware. To specify which route we can just append a .middleware() method onto
our routes. This will look something like:from masonite.middleware import Middleware
class AuthenticationMiddleware(Middleware):
    """Middleware class
    """
    def before(self, request, response):
        #..
        return request
    def after(self, request, response):
        #..
        return requestCreating Middleware
Consuming Middleware6/12/25, 3:02 AM Masonite Documentation