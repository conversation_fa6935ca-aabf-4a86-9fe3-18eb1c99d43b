=== Page 119 ===

119The above parameters are the defaults already so if you don't want to change them then
you don't have to specify them.
Since the routes in your api.py file are wrapped in an api middleware, you should add
a middleware stack in your route middleware in your Kernel file:
This middleware will allow any routes set on this stack to be protected by JWT
authorization.
By default, all routes in the routes/api.py file already have the api middleware
stack on them so there is no need to specify the stack on all your API routes.from masonite.api import Api
ROUTES += [
    #.. web routes
]
ROUTES += Api.routes(auth_route="/api/auth", 
reauth_route="/api/reauth")
# Kernel.py
from masonite.api.middleware import JWTAuthenticationMiddleware
#.. 
class Kernel:
    # ..
    route_middleware = {
        # ..
        "api": [
            JWTAuthenticationMiddleware
        ],
    }Middleware6/12/25, 3:02 AM Masonite Documentation