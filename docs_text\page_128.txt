=== Page 129 ===

129If a unknown gate is used a GateDoesNotExist exception will be raised.
Then anywhere (often in a controller) in your code you can use those gates to check if the
current authenticated user is authorized to perform the given action defined by the gate.
<PERSON> exposes different methods to perform verification: allows(), denies(), 
none(), any(), authorize()inspect().
allows(), denies(), none() and any() return a boolean indicating if user is
authorized
authorize() does not return a boolean but will raise an AuthorizationException
exception instead that will be rendered as an HTTP response with a 403 status code.
Finally for better control over the authorization check you can analyse the response with 
inspect():
if not Gate.allows("create-post"):
    return response.redirect("/")
# ...
post = Post.find(request.input("id"))
if Gate.denies("update-post", post):
    return response.redirect("/")
# ...
Gate.any(["delete-post", "update-post"], post)
# ...
Gate.none(["force-delete-post", "restore-post"], post)
Gate.authorize("update-post", post)
# if we reach this part user is authorized
# else an exception has been raised and be rendered as a 403 with 
content "Action not authorized".Authorizing Actions6/12/25, 3:02 AM Masonite Documentation