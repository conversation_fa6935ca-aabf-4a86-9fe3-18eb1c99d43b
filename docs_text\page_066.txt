=== Page 67 ===

67Response
The request and the response in Masonite work together to form a well formed response
that a browser can read and render. The Response class is responsible for what data is
returned and how it is formatted. In this case, a response can have cookies and headers.
The Request class can also have cookies and headers. In most times, generally, when you
have to fetch headers or cookies you should do so on the request class and when you set
cookies and headers you should do so on the response class.
Cookies on the response class are attached to the response and rendered as part of the
response headers. Any incoming cookies you would likely fetch from the request class
but you can fetch them from the response if you have a reason to:
You can also set cookies on the response:
You can also delete cookies:from masonite.response import Response
def show(self, response: Response):
    response.cookie("key")
from masonite.response import Response
def show(self, response: Response):
    response.cookie("Accepted-Cookies", "True")
from masonite.response import Response
def show(self, response: Response):
    response.delete_cookie("Accepted-Cookies")Cookies6/12/25, 3:02 AM Masonite Documentation