=== Page 20 ===

20Notice here we have a {{ csrf_field }} below the <form> open tag. Masonite
comes with CSRF protection so we need a token to render the hidden field with the CSRF
token.
Now we need to make sure the user is logged in before creating this so let's change up
our template a bit:
auth() is a view helper function that either returns the current user or returns None.
Masonite uses Jinja2 templating so if you don't understand this templating, be sure to Read
Their Documentation.templates/blog.html
<form action="/blog/create" method="POST">
    {{ csrf_field }}
    <input type="name" name="title">
    <textarea name="body"></textarea>
</form>
templates/blog.html
@if auth()
    <form action="/blog/create" method="POST">
        {{ csrf_field }}
        <label> Title </label>
        <input type="name" name="title"><br>
        <label> Body </label>
        <textarea name="body"></textarea>
        <input type="submit" value="Post!">
    </form>
@else
    <a href="/login">Please Login</a>
@endif
6/12/25, 3:02 AM Masonite Documentation