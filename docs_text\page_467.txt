=== Page 468 ===

468Because of the request singleton change we had to offset some other instantiation so we
added 2 new providers you need to add. The placement of the 
RequestHelpersProvider and CsrfProvider needs to be between the AppProvider
and the RouteProvider. The change is in the config/providers.py file.
We also need to add the orm provider from Masonite ORM. It doesn't really matter where
add this so we can add it at the bottom of the list somewhere:
There used to be a bootstrap/start.py file in Masonite apps. This file contained a
method was used to handle the request and response lifecycle. There was no reason to
keep the method inside the application since it was such a low level method and was
crucial for the framework to work.
So we need to import the method from the Masonite codebase instead of the start.py file.
We also renamed the method response_handler instead of appfrom masonite.providers import RequestHelperProvider
# ...
PROVIDERS = [
  # Framework Providers
  AppProvider, # <-- AppProvider Here
  RequestHelpersProvider, # <-- In the middle
  CsrfProvider, # <-- In the middle
  SessionProvider,
  RouteProvider, # <-- RouteProvider here
from masoniteorm.providers import ORMProvider
PROVIDERS = [
  # ..
  # Third Party Providers
  ORMProvider,
  # ..
]
WSGI.py File Change6/12/25, 3:02 AM Masonite Documentation