=== Page 94 ===

94For loop look similar to the regular python syntax.
Line Statements:
Using alternative Jinja2 syntax:
An include statement is useful for including other templates.
Line Statements:<div data-gb-custom-block data-tag="if">
    <p>do something</p>
<div data-gb-custom-block data-tag="elif"></div>
    <p>do something else</p>
<div data-gb-custom-block data-tag="else"></div>
    <p>above all are false</p>
</div>
@for item in items
    <p>{{ item }}</p>
@endfor
<div data-gb-custom-block data-tag="for">
    <p>{{ item }}</p>
</div>For Loops
Include statement6/12/25, 3:02 AM Masonite Documentation