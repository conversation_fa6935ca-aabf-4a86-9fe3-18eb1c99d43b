=== Page 470 ===

470Then add the migration:
Then run the migration
If you used any helper routes inside your web.py file, these have been removed. You will
now need to use only route classes:
If you have code inside your web.py
You will need to remove this import and use the class based routes:
This applied for the helpers: get, post, put, patch, delete and group helpers.
In previous versions of Masonite, Masonite would set flashed messages for a 2 second
expiration time. This caused numerous different issues like what happens when a page  def up(self):
      with self.schema.create("queue_jobs") as table:
          table.string("queue")
          table.timestamp("available_at").nullable()
          table.timestamp("reserved_at").nullable()
  def down(self):
      with self.schema.create("queue_jobs") as table:
          table.drop_column("queue", "available_at", "reserved_at")
$ python craft migrate
from masonite.helpers.routes import get
from masonite.routes import GetDropped route helpers
Flashed Messages6/12/25, 3:02 AM Masonite Documentation