=== Page 462 ===

462This will throw a StrictContainerError error. Now you have to import it so will have to
do something like this using the example above:
Now that we can no longer bind modules to the container we need to make some
changes to the wsgi.py file because we did that here.
Around line 16 you will see this:
Just completely remove that line. Its no longer needed.
Also around line 19 you will see this line:
You can completely remove that as well.
Lastly, around line 31 you can change this line:class ClassA:
    def __init__(self, container: Container):
        self.auth = container.make('AuthConfig')
from config import auth
class ClassA:
    def __init__(self, container: Container):
        self.auth = auth
container.bind('Application', application)
container.bind('ProvidersConfig', providers)
for provider in container.make('ProvidersConfig').PROVIDERS:Remove Modules from Container6/12/25, 3:02 AM Masonite Documentation