=== Page 322 ===

322Assert that given route has the given name.
Assert that given route has not the given name.
Assert that the response has the given HTTP status code:
Assert that the response returns a 200 status code:
Assert that the response returns a 201 status code:self.get("/").assertNoContent(status=204)
self.get("/").assertIsNamed("home")
self.get("/").assertIsNotNamed("admin")
self.get("/").assertIsStatus(201)
self.get("/").assertOk()
self.get("/").assertCreated()assertIsNamed
assertIsNotNamed
assertIsStatus
assertOk
assertCreated6/12/25, 3:02 AM Masonite Documentation