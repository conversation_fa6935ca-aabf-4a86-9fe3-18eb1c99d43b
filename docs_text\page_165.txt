=== Page 166 ===

166To fire an event with an event class you can use the fire method from the Event
class:
To fire a simple event without a class you will use the same method:
As an example, to build a listener that sends an email:
First, create the listener:
Then we can build out the listener.
To send an email we will need to import the mailable class and send the email using the 
mail key from the container:from app.events import UserAddedEvent
from masonite.events import Event
class RegisterController:
    def register(self, event: Event):
        # Register user
        event.fire(UserAddedEvent)
from app.events import UserAddedEvent
from masonite.events import Event
class RegisterController:
    def register(self, event: Event):
        # ...
        # Register user
        event.fire("users.added", user)
$ python craft listener WelcomeEmailBuilding a Welcome Email Listener6/12/25, 3:02 AM Masonite Documentation