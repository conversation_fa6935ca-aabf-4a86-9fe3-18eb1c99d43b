=== Page 162 ===

162If this token is changed or manipulated, Masonite will throw an InvalidCsrfToken
exception from inside the middleware.
If you attempt a POST request without the {{ csrf_field }} then you will receive a 
InvalidCsrfException exception. This just means you are either missing the Jinja2
tag or you are missing that route from the exempt class attribute in your middleware.
You can get also get the token that is generated. This is useful for JS frontends where
you need to pass a CSRF token to the backend for an AJAX call
For ajax calls, the best way to pass CSRF tokens is by setting the token inside a parent
template inside a meta tag like this:
And then you can fetch the token and put it wherever you need:
You can then pass the token via the X-CSRF-TOKEN header instead of the __token
input for ease of use.
Not all routes may require CSRF protection such as OAuth authentication or various
webhooks. In order to exempt routes from protection we can add it to the exempt class
attribute in the middleware located at app/middlewares/VerifyCsrfToken.py:<p> Token: {{ csrf_token }} </p>
<meta name="csrf-token" content="{{ csrf_token }}">
token = document.head.querySelector('meta[name="csrf-token"]')AJAX / Vue / Axios
Exempting Routes6/12/25, 3:02 AM Masonite Documentation