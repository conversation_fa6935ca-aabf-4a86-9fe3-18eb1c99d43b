=== Page 49 ===

49Routing
Masonite ships with a really powerful routing engine. Routing helps link a browser URL to
its controller and controller action.
Routes are created by importing the Route class and defining the HTTP verb you would
like with the URL and the controller you would like to use. These routes need to be
wrapped in a ROUTES list inside your routes file.
The first parameter is the URL you would like to be available in your application. In the
above example, this will allow anybody to go to the /welcome URL.
The second parameter is the Controller you want to bind this route to.
You may choose to define any one of the available verbs:from masonite.routes import Route
ROUTES = [
Route.get('/welcome', 'WelcomeController@show')
]
Route.get('/welcome', 'WelcomeController@show')
Route.post('/welcome', 'WelcomeController@show')
Route.put('/welcome', 'WelcomeController@show')
Route.patch('/welcome', 'WelcomeController@show')
Route.delete('/welcome', 'WelcomeController@show')
Route.options('/welcome', 'WelcomeController@show')
Route.view('/url', 'view.name', {'key': 'value'})
Route.resource('/users', 'UsersController')
Route.api('/users', 'UsersApiController')
Route.fallback("WelcomeController@fallback")Creating a Route
Available Route Methods6/12/25, 3:02 AM Masonite Documentation