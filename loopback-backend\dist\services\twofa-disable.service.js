"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorDisableService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const crypto = tslib_1.__importStar(require("crypto"));
const repositories_1 = require("../repositories");
const email_service_1 = require("./email.service");
/**
 * Service for handling secure 2FA disable requests via email
 * Provides secure token-based flow for disabling 2FA when recovery codes are exhausted
 */
let TwoFactorDisableService = class TwoFactorDisableService {
    constructor(userRepository, emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
        this.TOKEN_EXPIRY_HOURS = 1; // 1 hour expiry for security
        this.TOKEN_LENGTH = 32; // 32 bytes = 256 bits of entropy
    }
    /**
     * Generate and send a secure 2FA disable token via email
     * @param email - User's email address
     * @param reason - Optional reason for the request (e.g., 'recovery_codes_exhausted', 'lost_device')
     * @returns Promise<void>
     */
    async requestDisable2FA(email, reason) {
        try {
            console.log(`🔐 2FA disable request initiated for email: ${email}, reason: ${reason || 'not specified'}`);
            // Find user by email
            const user = await this.userRepository.findOne({
                where: { email: email.toLowerCase().trim() }
            });
            if (!user) {
                // Don't reveal if email exists - security best practice
                console.log(`⚠️ 2FA disable request for non-existent email: ${email}`);
                return; // Silently succeed to prevent email enumeration
            }
            if (!user.twoFactorEnabled) {
                console.log(`⚠️ 2FA disable request for user without 2FA enabled: ${email}`);
                throw new rest_1.HttpErrors.BadRequest('Two-factor authentication is not enabled on this account');
            }
            // Generate secure token
            const token = this.generateSecureToken();
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + this.TOKEN_EXPIRY_HOURS);
            // Store token in database
            await this.userRepository.updateById(user.id, {
                twofaDisableToken: token,
                twofaDisableExpires: expiresAt,
                updatedAt: new Date(),
            });
            // Send email with disable link
            await this.emailService.send2FADisableEmail(user.email, token, user.firstName);
            console.log(`✅ 2FA disable email sent to: ${email}, token expires at: ${expiresAt.toISOString()}`);
        }
        catch (error) {
            console.error(`❌ Failed to process 2FA disable request for: ${email}`, error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to process 2FA disable request');
        }
    }
    /**
     * Process a 2FA disable token and disable 2FA if valid
     * @param token - The disable token from the email link
     * @returns Promise<{success: boolean, message: string}>
     */
    async processDisable2FA(token) {
        try {
            console.log(`🔐 Processing 2FA disable token: ${token.substring(0, 8)}...`);
            if (!token || token.trim() === '') {
                throw new rest_1.HttpErrors.BadRequest('Invalid or missing disable token');
            }
            // Find user with this token
            const user = await this.userRepository.findOne({
                where: {
                    twofaDisableToken: token.trim(),
                }
            });
            if (!user) {
                console.log(`❌ Invalid 2FA disable token: ${token.substring(0, 8)}...`);
                throw new rest_1.HttpErrors.BadRequest('Invalid or expired disable token');
            }
            // Check if token has expired
            const now = new Date();
            if (!user.twofaDisableExpires || user.twofaDisableExpires < now) {
                console.log(`❌ Expired 2FA disable token for user: ${user.email}`);
                // Clear expired token
                await this.userRepository.updateById(user.id, {
                    twofaDisableToken: undefined,
                    twofaDisableExpires: undefined,
                    updatedAt: new Date(),
                });
                throw new rest_1.HttpErrors.BadRequest('Disable token has expired. Please request a new one.');
            }
            // Disable 2FA and clear all related data
            await this.userRepository.updateById(user.id, {
                twoFactorEnabled: false,
                twoFactorSecret: undefined,
                // Clear recovery codes
                backupCode1: undefined,
                backupCode2: undefined,
                backupCode3: undefined,
                backupCodesRemaining: 0,
                backupCodesGeneratedAt: undefined,
                // Clear disable token
                twofaDisableToken: undefined,
                twofaDisableExpires: undefined,
                updatedAt: new Date(),
            });
            console.log(`✅ 2FA successfully disabled for user: ${user.email}`);
            return {
                success: true,
                message: 'Two-factor authentication has been successfully disabled on your account.',
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                }
            };
        }
        catch (error) {
            console.error(`❌ Failed to process 2FA disable token: ${token.substring(0, 8)}...`, error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to process disable request');
        }
    }
    /**
     * Cancel/invalidate a pending 2FA disable request
     * @param userId - User ID to cancel request for
     */
    async cancelDisableRequest(userId) {
        try {
            await this.userRepository.updateById(userId, {
                twofaDisableToken: undefined,
                twofaDisableExpires: undefined,
                updatedAt: new Date(),
            });
            console.log(`✅ 2FA disable request cancelled for user: ${userId}`);
        }
        catch (error) {
            console.error(`❌ Failed to cancel 2FA disable request for user: ${userId}`, error);
            throw new rest_1.HttpErrors.InternalServerError('Failed to cancel disable request');
        }
    }
    /**
     * Check if a user has a pending 2FA disable request
     * @param userId - User ID to check
     * @returns Promise<boolean>
     */
    async hasPendingDisableRequest(userId) {
        try {
            const user = await this.userRepository.findById(userId);
            if (!user.twofaDisableToken || !user.twofaDisableExpires) {
                return false;
            }
            // Check if token has expired
            const now = new Date();
            if (user.twofaDisableExpires < now) {
                // Clean up expired token
                await this.userRepository.updateById(userId, {
                    twofaDisableToken: undefined,
                    twofaDisableExpires: undefined,
                    updatedAt: new Date(),
                });
                return false;
            }
            return true;
        }
        catch (error) {
            console.error(`❌ Failed to check pending disable request for user: ${userId}`, error);
            return false;
        }
    }
    /**
     * Generate a cryptographically secure token
     */
    generateSecureToken() {
        return crypto.randomBytes(this.TOKEN_LENGTH).toString('hex');
    }
};
exports.TwoFactorDisableService = TwoFactorDisableService;
exports.TwoFactorDisableService = TwoFactorDisableService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(1, (0, core_1.inject)('services.EmailService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository,
        email_service_1.EmailService])
], TwoFactorDisableService);
//# sourceMappingURL=twofa-disable.service.js.map