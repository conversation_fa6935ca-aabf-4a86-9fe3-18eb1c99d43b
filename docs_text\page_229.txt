=== Page 230 ===

230Then to associate a given key to this limit we can do:
This feature allows to simply limit calling a Python callable. Here we will limit the given
callable to be called 3 times per hour for the key sam.
Let's make one attempt:
Alternatively you can manually incrementing attempts:
We can get the number of attempts:
We can get the number of remaining attempts:
We can check if too many attempts have been made:username = f"send_mail-{user.id}"
Limit.per_hour(5).by(username)
def send_welcome_mail():
    # ...
RateLimiter.attempt(f"send_mail-{user.id}", send_welcome_mail, 
max_attempts=3, delay=60*60)
RateLimiter.hit(f"send_mail-{user.id}", delay=60*60)
RateLimiter.attempts(f"send_mail-{user.id}") #== 1
RateLimiter.remaining(f"send_mail-{user.id}", 3) #== 2Limit an action6/12/25, 3:02 AM Masonite Documentation