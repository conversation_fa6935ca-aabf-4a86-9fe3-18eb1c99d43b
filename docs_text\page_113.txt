=== Page 114 ===

114Now when your application throws a ZeroDivisionError, Masonite will use your
handler rather than Masonite's own exception handlers.
If you want to hook up an error tracking service such as Sentry or Rollbar you can do this
through event listeners: each time an exception is raised, a masonite.exception.
{TheExceptionType} is fired, allowing to run any custom logic.
First create a listener to run your custom logic:
In a Service Provider you then need to register this listener:class SentryListener:
    def handle(self, exception_type: str, exception: Exception):
        # process the exception with Sentry
        # ...
class AppProvider(Provider):
    def register(self):
        self.application.make("event").listen("masonite.exception.*", 
[SentryListener])Catch All Exceptions6/12/25, 3:02 AM Masonite Documentation