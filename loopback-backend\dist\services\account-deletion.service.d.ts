import { SecurityService, EmailService } from './';
import { UserRepository, PaymentRepository, AccountDeletionRecordRepository, PreservedUserDataRepository } from '../repositories';
import { AccountDeletionRecord } from '../models';
export interface DeletionPreferences {
    preservePaymentData: boolean;
    preserveTransactionHistory: boolean;
    preserveProfileData: boolean;
    preserveSecurityLogs: boolean;
    customRetentionPeriod?: number;
    reason?: string;
}
export interface DataRestoreOptions {
    restorePaymentData: boolean;
    restoreTransactionHistory: boolean;
    restoreProfileData: boolean;
    restoreSecurityLogs: boolean;
}
export declare class AccountDeletionService {
    userRepository: UserRepository;
    paymentRepository: PaymentRepository;
    deletionRepository: AccountDeletionRecordRepository;
    preservedDataRepository: PreservedUserDataRepository;
    securityService: SecurityService;
    emailService: EmailService;
    constructor(userRepository: UserRepository, paymentRepository: PaymentRepository, deletionRepository: AccountDeletionRecordRepository, preservedDataRepository: PreservedUserDataRepository, securityService: SecurityService, emailService: EmailService);
    /**
     * Request account deletion with preferences
     */
    requestAccountDeletion(userId: string, preferences: DeletionPreferences): Promise<{
        message: string;
        deletionId: string;
        confirmationRequired: boolean;
        confirmationToken?: string;
    }>;
    /**
     * Confirm account deletion with token
     */
    confirmAccountDeletion(token: string): Promise<{
        message: string;
        deletionId: string;
        preservedDataSummary?: object;
    }>;
    /**
     * Check if user has preserved data
     */
    checkPreservedData(email: string): Promise<{
        hasPreservedData: boolean;
        deletionRecord?: AccountDeletionRecord;
        preservedDataSummary?: object;
    }>;
    /**
     * Restore user data during signup
     */
    restoreUserData(newUserId: string, email: string, restoreOptions: DataRestoreOptions): Promise<{
        message: string;
        restoredData: object;
    }>;
    /**
     * Delete preserved data permanently
     */
    deletePreservedData(email: string): Promise<{
        message: string;
    }>;
    /**
     * Preserve user data based on preferences
     */
    private preserveUserData;
    /**
     * Perform actual account deletion
     */
    private performAccountDeletion;
    /**
     * Restore payment data
     */
    private restorePaymentData;
    /**
     * Restore profile data
     */
    private restoreProfileData;
    /**
     * Encrypt sensitive data
     */
    private encryptData;
    /**
     * Decrypt sensitive data
     */
    private decryptData;
    /**
     * Send custom email using the EmailService infrastructure
     */
    private sendCustomEmail;
    private sendDeletionConfirmationEmail;
    /**
     * Send deletion completed email
     */
    private sendDeletionCompletedEmail;
    /**
     * Send data restoration confirmation email
     */
    private sendDataRestorationEmail;
    /**
     * Export user data as downloadable JSON
     */
    exportUserData(userId: string): Promise<object>;
    /**
     * Request data export via email
     */
    requestDataExport(userId: string): Promise<{
        message: string;
    }>;
    /**
     * Clean up expired or invalid deletion records
     * This helps prevent accumulation of stale records
     */
    cleanupExpiredDeletionRecords(): Promise<{
        message: string;
        cleanedCount: number;
    }>;
}
