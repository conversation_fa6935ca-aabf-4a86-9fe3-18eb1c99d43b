=== Page 410 ===

410Added a new download class to make it very easy to force downloads or display files.
This is used like this:
Forcing will make the file download and not forcing will display the file in the browser.
You can now run a craft preset command which will generate some .json files and
example templates. There is a react, vue and bootstrap preset currently.
Instead of a url like /?filter[name]=Joe&filter[user]=bob&email=<EMAIL>
parsing to:
It will now parse into:from masonite.response import Download
def show(self):
    return Download('/path/to/file', force=True)
{
    "filter[name]": "<PERSON>",
    "filter[user]": "<PERSON>",
    "email": "<EMAIL>"
}Added Download Class
Added Preset Command
Changed How Query Strings Are Parsed6/12/25, 3:02 AM Masonite Documentation