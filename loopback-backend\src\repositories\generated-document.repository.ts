import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DbDataSource} from '../datasources';
import {GeneratedDocument, GeneratedDocumentRelations, CrawlJob, User} from '../models';
import {CrawlJobRepository} from './crawl-job.repository';
import {UserRepository} from './user.repository';

export class GeneratedDocumentRepository extends DefaultCrudRepository<
  GeneratedDocument,
  typeof GeneratedDocument.prototype.id,
  GeneratedDocumentRelations
> {
  public readonly crawlJob: BelongsToAccessor<CrawlJob, typeof GeneratedDocument.prototype.id>;
  public readonly user: BelongsToAccessor<User, typeof GeneratedDocument.prototype.id>;

  constructor(
    @inject('datasources.db') dataSource: DbDataSource,
    @repository.getter('CrawlJobRepository') protected crawlJobRepositoryGetter: Getter<CrawlJobRepository>,
    @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(GeneratedDocument, dataSource);
    
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
    
    this.crawlJob = this.createBelongsToAccessorFor('crawlJob', crawlJobRepositoryGetter);
    this.registerInclusionResolver('crawlJob', this.crawlJob.inclusionResolver);
  }

  /**
   * Find documents by crawl job ID
   */
  async findByCrawlJobId(crawlJobId: string): Promise<GeneratedDocument[]> {
    return this.find({
      where: {crawlJobId},
      order: ['createdAt DESC'],
    });
  }

  /**
   * Find documents by user ID
   */
  async findByUserId(userId: string): Promise<GeneratedDocument[]> {
    return this.find({
      where: {userId},
      order: ['createdAt DESC'],
      include: ['crawlJob'],
    });
  }

  /**
   * Find documents by status
   */
  async findByStatus(status: string): Promise<GeneratedDocument[]> {
    return this.find({
      where: {status},
      order: ['createdAt DESC'],
    });
  }

  /**
   * Find documents by format
   */
  async findByFormat(format: string, userId?: string): Promise<GeneratedDocument[]> {
    const where: any = {format};
    if (userId) {
      where.userId = userId;
    }
    
    return this.find({
      where,
      order: ['createdAt DESC'],
    });
  }

  /**
   * Update document generation progress
   */
  async updateProgress(
    id: string,
    processedPages: number,
    totalPages: number,
    status?: string,
  ): Promise<void> {
    const progressPercentage = totalPages > 0 ? Math.round((processedPages / totalPages) * 100) : 0;
    
    const updateData: Partial<GeneratedDocument> = {
      processedPages,
      totalPages,
      progressPercentage,
      updatedAt: new Date(),
    };

    if (status) {
      updateData.status = status;
      if (status === 'generating' && !updateData.startedAt) {
        updateData.startedAt = new Date();
      } else if (status === 'completed' || status === 'failed') {
        updateData.completedAt = new Date();
      }
    }

    await this.updateById(id, updateData);
  }

  /**
   * Record document download
   */
  async recordDownload(id: string): Promise<void> {
    const document = await this.findById(id);
    await this.updateById(id, {
      downloadCount: document.downloadCount + 1,
      lastDownloadedAt: new Date(),
      updatedAt: new Date(),
    });
  }

  /**
   * Find expired documents
   */
  async findExpiredDocuments(): Promise<GeneratedDocument[]> {
    const now = new Date();
    return this.find({
      where: {
        expiresAt: {lt: now},
        status: 'completed',
      },
    });
  }

  /**
   * Get user document statistics
   */
  async getUserDocumentStatistics(userId: string): Promise<object> {
    const documents = await this.find({where: {userId}});
    
    const stats = {
      totalDocuments: documents.length,
      completedDocuments: documents.filter(doc => doc.status === 'completed').length,
      failedDocuments: documents.filter(doc => doc.status === 'failed').length,
      pendingDocuments: documents.filter(doc => doc.status === 'pending').length,
      generatingDocuments: documents.filter(doc => doc.status === 'generating').length,
      totalDownloads: documents.reduce((sum, doc) => sum + doc.downloadCount, 0),
      totalFileSize: documents.reduce((sum, doc) => sum + doc.fileSize, 0),
      documentsByFormat: this.groupDocumentsByFormat(documents),
      documentsByOrganization: this.groupDocumentsByOrganization(documents),
    };

    return stats;
  }

  /**
   * Group documents by format
   */
  private groupDocumentsByFormat(documents: GeneratedDocument[]): object {
    const grouped: {[key: string]: number} = {};
    
    for (const doc of documents) {
      const format = doc.format || 'unknown';
      grouped[format] = (grouped[format] || 0) + 1;
    }
    
    return grouped;
  }

  /**
   * Group documents by organization type
   */
  private groupDocumentsByOrganization(documents: GeneratedDocument[]): object {
    const grouped: {[key: string]: number} = {};
    
    for (const doc of documents) {
      const orgType = doc.organizationType || 'unknown';
      grouped[orgType] = (grouped[orgType] || 0) + 1;
    }
    
    return grouped;
  }

  /**
   * Clean up expired documents
   */
  async cleanupExpiredDocuments(): Promise<number> {
    const expiredDocs = await this.findExpiredDocuments();
    
    // In a real implementation, you would also delete the actual files
    // For now, just delete the database records
    if (expiredDocs.length > 0) {
      const expiredIds = expiredDocs.map(doc => doc.id);
      await this.deleteAll({
        id: {inq: expiredIds},
      });
    }

    return expiredDocs.length;
  }

  /**
   * Find documents ready for cleanup (old completed documents)
   */
  async findDocumentsForCleanup(daysOld: number = 30): Promise<GeneratedDocument[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return this.find({
      where: {
        status: 'completed',
        completedAt: {lt: cutoffDate},
      },
    });
  }
}
