=== Page 334 ===

334Assert that a table in the database contains records matching the given query.
Assert that a table in the database does not contain records matching the given query.
Assert that the given model instance has been deleted from the database.
Assert that the given model instance has been soft deleted from the database.self.assertDatabaseHas(table, query_dict)
self.assertDatabaseCount("users", {"name": "<PERSON>"})
self.assertDatabaseMissing(table, query_dict)
self.assertDatabaseMissing("users", {"name": "<PERSON>"})
user=User.find(1)
user.delete()
self.assertDeleted(user)
self.assertSoftDeleted(user)assertDatabaseHas
assertDatabaseMissing
assertDeleted
assertSoftDeleted6/12/25, 3:02 AM Masonite Documentation