=== Page 149 ===

149Then you should define Memcached as default store and configure it with your
Memcached server parameters:
Finally ensure that the Memcached server is running and you're ready to start using
cache.
You can access Cache service via the Cache facade or by resolving it from the Service
Container.
Two methods are available: add and put.
You can easily add cache data using theadd method. This will either fetch the data
already in the cache, if it is not expired, or it will insert the new value.pip install pymemcache
STORES = {
    "default": "memcache",
    "memcache": {
        "driver": "memcache",
        "host": env("MEMCACHED_HOST", "127.0.0.1"),
        "port": env("MEMCACHED_PORT", "11211"),
        "password": env("MEMCACHED_PASSWORD"),
        "name": env("MEMCACHED_PREFIX", "project name"),
    },
}
Using the Cache
Storing Data
add6/12/25, 3:02 AM Masonite Documentation