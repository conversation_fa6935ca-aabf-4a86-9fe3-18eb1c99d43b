=== Page 226 ===

226Y<PERSON> can add throttling to HTTP requests easily by using the 
ThrottleRequestsMiddleware.
First register the middleware as a route middleware into your project:
This middleware is taking one argument which can be either a limit string or a limiter
name.
By using limit strings such as 100/day you will be able to add a global limit which does
not link this limit to a given user, view or IP address. It's really an absolute limit that you
will define on your HTTP requests.
Units available are: minute, hour and day.
We now just have to use it in our routes:# Kernel.py
from masonite.middleware import ThrottleRequestsMiddleware
class Kernel:
    route_middleware = {
        "web": [
            SessionMiddleware,
            LoadUserMiddleware,
            VerifyCsrfToken,
        ],
        "throttle": [ThrottleRequestsMiddleware],
    }Throttle HTTP Requests
Using Limit Strings6/12/25, 3:02 AM Masonite Documentation