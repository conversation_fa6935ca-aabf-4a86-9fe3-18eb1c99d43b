{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/progress-spinner\";\nexport let OAuthSuccessComponent = /*#__PURE__*/(() => {\n  class OAuthSuccessComponent {\n    constructor(route, router, authService, snackBar) {\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.isNewUser = false;\n    }\n    ngOnInit() {\n      console.log('OAuth Success Component - Starting...');\n      this.route.queryParams.subscribe(params => {\n        console.log('OAuth Success - Query params:', params);\n        const token = params['token'];\n        const isNewUser = params['isNewUser'] === 'true';\n        const provider = params['provider'] || 'unknown';\n        this.isNewUser = isNewUser;\n        if (token) {\n          console.log('Token found in URL parameters');\n          // Store authentication data\n          localStorage.setItem('authToken', token);\n          localStorage.setItem('isAuthenticated', 'true');\n          localStorage.setItem('oauthLogin', 'true');\n          localStorage.setItem('oauthProvider', provider);\n          // Show success message\n          this.snackBar.open(isNewUser ? 'Account created successfully!' : 'Login successful!', 'Close', {\n            duration: 3000\n          });\n          // Redirect to dashboard\n          setTimeout(() => {\n            this.router.navigate(['/dashboard'], {\n              queryParams: {\n                welcome: isNewUser ? 'true' : null,\n                oauth: 'true',\n                provider: provider\n              }\n            });\n          }, 1500);\n        } else {\n          console.log('No token found in URL parameters');\n          this.handleTokenError();\n        }\n      });\n    }\n    handleTokenError() {\n      this.snackBar.open('Authentication failed. Please try again.', 'Close', {\n        duration: 5000\n      });\n      setTimeout(() => {\n        this.router.navigate(['/auth/login']);\n      }, 2000);\n    }\n    static #_ = this.ɵfac = function OAuthSuccessComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthSuccessComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OAuthSuccessComponent,\n      selectors: [[\"app-oauth-success\"]],\n      standalone: false,\n      decls: 13,\n      vars: 2,\n      consts: [[1, \"oauth-success-container\"], [1, \"success-card\"], [1, \"success-icon\"], [1, \"loading-spinner\"], [\"diameter\", \"40\"], [1, \"loading-text\"]],\n      template: function OAuthSuccessComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"check_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"h2\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 3);\n          i0.ɵɵelement(10, \"mat-spinner\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 5);\n          i0.ɵɵtext(12, \"Redirecting to dashboard...\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isNewUser ? \"Account Created Successfully!\" : \"Login Successful!\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isNewUser ? \"Welcome to our platform!\" : \"Welcome back!\");\n        }\n      },\n      dependencies: [i4.MatIcon, i5.MatProgressSpinner],\n      styles: [\".oauth-success-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.success-card[_ngcontent-%COMP%]{background:#fff;padding:40px;border-radius:10px;box-shadow:0 10px 30px #0003;text-align:center;max-width:400px;width:100%}.success-icon[_ngcontent-%COMP%]{color:#4caf50;font-size:48px;margin-bottom:20px}.success-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px}h2[_ngcontent-%COMP%]{margin:20px 0;color:#333}p[_ngcontent-%COMP%]{color:#666;margin:10px 0}.loading-spinner[_ngcontent-%COMP%]{margin:20px 0}.loading-text[_ngcontent-%COMP%]{font-size:14px;color:#888}\"]\n    });\n  }\n  return OAuthSuccessComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}