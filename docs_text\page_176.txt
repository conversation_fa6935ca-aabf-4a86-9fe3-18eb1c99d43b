=== Page 177 ===

177The provider should also be added:
This will register a template helper and some other useful features.
You can use the helper directly to encode or decode integers easily:
Inside your templates you can use the hashid template helper:
When submitted to the backend the will now be the normal integer value of the user id:from masonite.providers import HashIDProvider
PROVIDERS = [
  # ..
  HashIDProvider,
]
from masonite.essentials.helpers import hashid
def show(self):
  hashid(10) #== l9avmeG
  hashid('l9avmeG', decode=True) #== 10
<!-- user.id == 10 -->
<input type="hidden" name="user_id" value="{{ hashid(user.id) }}"
def store(self, request: Request):
  request.input("user_id") #== 10Helper
Templates6/12/25, 3:02 AM Masonite Documentation