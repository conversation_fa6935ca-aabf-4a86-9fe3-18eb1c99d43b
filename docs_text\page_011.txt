=== Page 12 ===

12and open up http://localhost:8000/blog. You will see "This is a blog" in your web
browser.
Most applications will require some form of authentication. Masonite comes with a craft
command to scaffold out an authentication system for you. This should typically be ran
on fresh installations of Masonite since it will create controllers, routes, and views for
you.
For our blog, we will need to setup some form of registration so we can get new users to
start posting to our blog. We can create an authentication system by running the craft
command:
We should get a success message saying that some new assets were created. You can
check your controllers folder and you should see a few new controllers there that should
handle registrations.
You can then add the authentication routes to your project:
We will check what was created for us in a bit.terminal
$ python craft auth
from masonite.authentication import Auth
ROUTES = [
    Route.get('/', 'WelcomeController@show').name('welcome'),
    # Blog Routes
    Route.get('/blog', 'BlogController@show')
]
ROUTES += Auth.routes()Authentication6/12/25, 3:02 AM Masonite Documentation