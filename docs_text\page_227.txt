=== Page 228 ===

228Finally you can register your limiter(s) in your application provider:
We now just have to use it in our routes:
Now when making unauthenticated requests to our /api endpoints we will see some
new headers in the response:
•X-Rate-Limit-Limit : 5
•X-Rate-Limit-Remaining : 4
After reaching the limit two headers are added in the response, X-Rate-Limit-Reset
which is the timestamp in seconds defining when rate limit will be reset and when api
endpoint will be available again and Retry-After which is the number of seconds in
which rate limit will be reset:
•X-Rate-Limit-Limit : 5
•X-Rate-Limit-Remaining : 0
•X-Rate-Limit-Reset : **********
•Retry-After : 500from masonite.facades import RateLimiter
from masonite.rates import GuestsOnlyLimiter
from app.rates import PremiumUsersLimiter
class AppProvider(Provider):
    def register(self):
        RateLimiter.register("premium", PremiumUsersLimiter())
        # register an other limiter which gives unlimited access for 
authenticated users
        # and 2 request/day for guests users
        RateLimiter.register("guests", GuestsOnlyLimiter("2/hour"))
# web.py
Route.post("/api/uploads/", 
"UploadController@create").middleware("throttle:premium")
Route.post("/api/videos/", 
"UploadController@create").middleware("throttle:guests")6/12/25, 3:02 AM Masonite Documentation