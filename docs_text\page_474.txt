=== Page 475 ===

475Previously when we had to do a redirection we would use the request class:
This has now been changed to the response class:- from masonite.helpers import random_string
+ from masonite.utils.str import random_string
- from masonite import Mail
+ from masonite.mail import Mail
- from masonite.view import View
+ from masonite.views import View
- from masonite.auth import Auth
+ from masonite.authentication import Auth
- from masonite import env
+ from masonite.environment import env
- from masonite.helpers import password
+ from masonite.facades import Hash #== See Hashing documentation
- from masonite.middleware import CsrfMiddleware as Middleware
+ from masonite.middleware import VerifyCsrfToken as Middleware
- from masonite.helpers import config
+ from masonite.configuration import config
- from masonite.drivers import Mailable
+ from masonite.mail import Mailable
- from masonite.provider import ServiceProvider
+ from masonite.providers import Provider
def store(self, request: Request):
return request.redirect('/home')Request and Response redirects6/12/25, 3:02 AM Masonite Documentation