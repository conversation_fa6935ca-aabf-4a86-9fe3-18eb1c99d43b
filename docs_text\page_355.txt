=== Page 356 ===

356Add the .ajaxComplete() block the document.ready javascript function in your page
This is for JQuery but you can adjust this id for whatever JS framework you're using.
This adjustment is not strictly required but does provide a better user experience.
By default the VerifyCsrfToken middleware is set as part of the web middleware
group. This will prevent the auth middleware checking the request before processing the
page form data and may generate an invalid form error which is not what the user might
expect.
To resolve this you can easily rearrange the order of middlewares.def before(self, request, response):
    if not request.user():
        if request.is_ajax():
            response.content = "MASONITE_LOGIN_REQUIRED"
            response.status(403)
            return response
        return response.redirect(name="login")
    return request
$(document).ready(function() {
    $(document).ajaxComplete(
        function (event, request, options) {
            if (request.responseText === "MASONITE_LOGIN_REQUIRED") {
                window.location.href = "{{ route(name='login') }}";
            }
        }
    )
});Page Template
Middleware ordering6/12/25, 3:02 AM Masonite Documentation