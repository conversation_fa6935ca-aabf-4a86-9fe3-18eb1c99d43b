=== Page 39 ===

395.You must add unit testing for any changes made before the PR will be merged. If you
are unsure how to write unit tests of the repositories listed above, you may open the
pull request anyway and we will add the tests together.
6.Increase the version numbers in any example files (like setup.py) and to the new
version that this Pull Request would represent. The versioning scheme we use is 
SEMVER.
7.The PR must pass the GitHub actions that run on pull requests. The Pull Request can
be merged in once you have a successful review from two other collaborators, or one
review from a maintainer.
Branching is also important. Depending on what fixes or features you are making you will
need to branch from (and back into) the current branch. Branching for Masonite simple:
1) All of Masonite repositories, packages, etc. follow the same basic branching flow.
2) Each repository has: a current release branch, previous release branches, a master
branch and a develop branch.
3) The current release branch is what the current release is on. So for example, Masonite
is on version 2.3 so the current release branch will be 2.3.
4) Previous release branches are the same thing but instead are just previous versions.
So if Masonite is currently on 4.0 then the previous release branches could be 3.0, 
2.1, 2.2, etc.
5) The master branch is a staging branch that will eventually be merged into the current
release branch. Here is where all non breaking changes will be staged (new non breaking
features and bug fixes).
6) The develop branch is a staging branch to the next major version. So for example,
Masonite will be on version 4.0. If you have an idea for a feature but it will break the
existing feature then you will branch from (and back into) the develop branch. This
branch will eventually be merged into 4.1 branch and become apart of the next major
version when that is released.
Branching6/12/25, 3:02 AM Masonite Documentation