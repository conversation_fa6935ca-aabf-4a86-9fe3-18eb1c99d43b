=== Page 237 ===

237You must then register tasks to the Masonite scheduler. You can do so easily inside your
app Service Provider (if you don't have one, you should create one):
Tasks will run as often as you specify them to run using the time options.
You can specify the tasks to run as often as you need them to. Available options are:from app.tasks.SendInvoices import SendInvoices
class AppProvider(Provider):
    def register(self):
        self.application.make('scheduler').add(
          SendInvoices().daily()
        )
Option Description
every_minute() Specifies this task to run every minute
every_15_minutes() Specifies this task to run every 15 minutes
every_30_minutes() Specifies this task to run every 30 minutes
every_45_minutes() Specifies this task to run every 45 minutes
hourly() Specifies this task to run every hour.
daily() Specifies this task to run every day at midnight
weekly()Specifies this task to run every week on sunday
at 00:00
monthly()Specifies this task to run every first of the
month at 00:00Registering Tasks
Options6/12/25, 3:02 AM Masonite Documentation