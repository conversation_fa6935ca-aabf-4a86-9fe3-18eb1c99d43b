=== Page 381 ===

381Read more in the Database Seeding documentation.
Now all templates have a new static function in them to improve rendering of static
assets
Read more in the Static Files documentation.
You can use the password helper to hash passwords more simply than using straight
bcrypt:
Read more in the Encryption documentation.
You can now specify which location in your drivers you want to upload to using a new dot
notation:
This will use the directory stored in:
from masonite.helpers import password
password('secret') # returns bcrypt password
Upload.store(request().input('file'), 'disk.uploads')Added a New Static File Helper
Added a New Password Helper
Added Dot Notation To Upload Drivers And
Dictionary Support To Driver Locations.6/12/25, 3:02 AM Masonite Documentation