=== Page 160 ===

160Y<PERSON> can print messages to console with different formatting:
•self.info("Info Message"): will output a message in green
•self.warning("Warning Message"): will output a message in yellow
•self.error("Error Message"): will output a message in bold red
•self.comment("Comment Message"): will output a message in light blue
You can find more information and more features for creating commands in Cleo
documentation.
Masonite Command class is inheriting Cleo Command class so you should be able to use
all Cleo features when creating commands.
Once created you can register the command to Masonite's Service Container inside a 
Service Provider (if you don't have one, you should create one):
add() method takes one or multiple commands:
When you run python craft you will now see the command you added.
from some.place.YourCommand import YourCommand
class AppProvider(Provider):
    def __init__(self, application):
        self.application = application
    def register(self):
        self.application.make('commands').add(YourCommand())Printing Messages
Advanced
Registering Commands6/12/25, 3:02 AM Masonite Documentation