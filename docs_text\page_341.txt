=== Page 342 ===

342When mocking notifications it will prevent notifications from being really sent. Typically,
sending notification is unrelated to the code you are actually testing. Most likely, it is
sufficient to simply assert that <PERSON><PERSON> was instructed to send a given notification.
Here is an example of how to mock notifications sending in your tests:
Available assertions are:
•assertNothingSent()
•assertCount(count)def setUp(self):
    super().setUp()
    self.fake("notification")
def tearDown(self):
    super().tearDown()
    self.restore("notification")
def test_mock_notification(self):
    notification = self.application.make("notification")
    notification.assertNothingSent()
    notification.route("mail", 
"<EMAIL>").send(WelcomeNotification())
    notification.route("mail", 
"<EMAIL>").send(WelcomeNotification())
    notification.assertCount(2)
def test_mock_notification(self):
    self.application.make("notification").route("mail", 
"<EMAIL>").route(
        "slack", "#general"
    ).send(OrderNotification(10))
    self.application.make("notification").assertLast(
        lambda user, notif: (
            notif.assertSentVia("mail")
            .assertEqual(notif.order_id, 10)
            .assertEqual(
                notif.to_slack(user).get_options().get("text"),
                "Order 10 has been shipped !",
            )
        )
    )6/12/25, 3:02 AM Masonite Documentation