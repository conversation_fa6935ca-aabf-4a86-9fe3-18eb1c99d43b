{"version": 3, "file": "security.service.js", "sourceRoot": "", "sources": ["../../src/services/security.service.ts"], "names": [], "mappings": ";;;;AAAA,yCAAgE;AAChE,qDAAgD;AAChD,yCAA0C;AAC1C,6DAAuC;AACvC,uDAAiC;AACjC,uDAAiC;AACjC,uCAA8B;AAC9B,kDAA8D;AAE9D,mEAA4D;AAGrD,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,YACqC,cAA8B,EAC/B,aAA4B,EACG,mBAAyC;QAFvE,mBAAc,GAAd,cAAc,CAAgB;QAC/B,kBAAa,GAAb,aAAa,CAAe;QACG,wBAAmB,GAAnB,mBAAmB,CAAsB;IACzG,CAAC;IAEJ,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,iBAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC;YACtC,IAAI,EAAE,cAAc,IAAI,CAAC,KAAK,GAAG;YACjC,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,EAAE;SACX,CAAC,CAAC;QAEH,0BAA0B;QAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,eAAe,EAAE,MAAM,CAAC,MAAM;YAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAY,CAAC,CAAC;QAE3D,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM;SACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,KAAa;QACtD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACnC,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAEzD,sDAAsD;QACtD,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAE7C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YACrC,MAAM,EAAE,IAAI,CAAC,eAAe;YAC5B,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,UAAU;YACjB,MAAM,EAAE,CAAC,EAAE,oDAAoD;SAChE,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;QAEjD,uEAAuE;QACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC;gBAC3C,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC7C,MAAM,EAAE,IAAI,CAAC,eAAe;oBAC5B,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,UAAU;oBACjB,MAAM,EAAE,MAAM;iBACf,CAAC,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,aAAa,MAAM,KAAK,gBAAgB,EAAE,CAAC,CAAC;gBACxD,IAAI,gBAAgB,EAAE,CAAC;oBACrB,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;oBAC/E,OAAO,IAAI,CAAC,CAAC,8BAA8B;gBAC7C,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,KAAa;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,gBAAgB,EAAE,IAAI;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,KAAa;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,IAAY;QAChD,uBAAuB;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;QAEtE,wDAAwD;QACxD,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YACjC,UAAU;YACV,IAAI;YACJ,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9B,UAAU;YACV,IAAI;YACJ,IAAI;YACJ,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,IAAY,EAAE,IAAY;QAC5D,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE;gBACL,UAAU;gBACV,IAAI;gBACJ,IAAI;gBACJ,IAAI,EAAE,KAAK;gBACX,SAAS,EAAE,EAAC,EAAE,EAAE,IAAI,IAAI,EAAE,EAAC;aAC5B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,uCAAuC;YACvC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;gBACnD,KAAK,EAAE;oBACL,UAAU;oBACV,IAAI;oBACJ,IAAI,EAAE,KAAK;iBACZ;aACF,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,EAAE;oBAClD,QAAQ,EAAE,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC;iBAC1C,CAAC,CAAC;gBAEH,+BAA+B;gBAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,EAAE;wBAClD,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI,IAAI,EAAE;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mBAAmB;QACnB,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE;YAC1C,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI,IAAI,EAAE;SACnB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,0BAA0B;QAC9B,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,8BAA8B;QAClC,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,OAAO,IAAA,eAAI,EAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG;YACxB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,wBAAwB;SACzB,CAAC;QAEF,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;QAClD,OAAO,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,OAAO,CAAC,GAAG,CAAC,yDAAyD,EAAE,MAAM,CAAC,CAAC;QAC/E,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEhF,oEAAoE;QACpE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YACtE,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;QACvF,+CAA+C;QAC/C,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,qCAAqC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,IAAY;QACjD,sEAAsE;QACtE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,8BAA8B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACrF,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QACxF,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AAxQY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,iBAAU,EAAC,EAAC,KAAK,EAAE,mBAAY,CAAC,SAAS,EAAC,CAAC;IAGvC,mBAAA,IAAA,uBAAU,EAAC,6BAAc,CAAC,CAAA;IAC1B,mBAAA,IAAA,uBAAU,EAAC,4BAAa,CAAC,CAAA;IACzB,mBAAA,IAAA,aAAM,EAAC,8BAA8B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAA;6CAFN,6BAAc;QAChB,4BAAa;QACyB,2CAAmB;GAJjG,eAAe,CAwQ3B"}