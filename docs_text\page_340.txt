=== Page 341 ===

341Available assertions are:
•seeEmailWasSent()
•seeEmailWasNotSent()
•seeEmailCountEquals(count)
•seeEmailTo(string)
•seeEmailFrom(string)
•seeEmailReplyTo(string)
•seeEmailBcc(string)
•seeEmailCc(string)
•seeEmailSubjectEquals(string)
•seeEmailSubjectContains(string)
•seeEmailSubjectDoesNotContain(string)
•seeEmailContains(string)
•seeEmailDoesNotContain(string)
•seeEmailPriority(string)def setUp(self):
    super().setUp()
    self.fake("mail")
def tearDown(self):
    super().tearDown()
    self.restore("mail")
def test_mock_mail(self):
    welcome_email = 
self.application.make("mail").mailable(Welcome()).send()
    (
        welcome_email.seeEmailContains("Hello from Masonite!")
        .seeEmailFrom("<EMAIL>")
        .seeEmailCountEquals(1)
    )
Mocking Notification6/12/25, 3:02 AM Masonite Documentation