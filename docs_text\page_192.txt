=== Page 193 ===

193Y<PERSON> should define a to_slack method on the notification class to specify how to build
the slack notification content.
SlackComponent takes different options to configure your notification:class Welcome(Notification, Mailable):
    def to_mail(self, notifiable):
        return (
            self.to(notifiable.email)
            .subject("Welcome to our site!")
            .from_("<EMAIL>")
            .text(f"Hello {notifiable.name}")
            .driver("mailgun")
        )
    def via(self, notifiable):
        return ["mail"]
from masonite.notification.components import SlackComponent
class Welcome(Notification):
    def to_slack(self, notifiable):
        return SlackComponent().text('Masonite Notification: Read The 
Docs!, https://docs.masoniteproject.com/') \
            .channel('#bot') \
            .as_user('Masonite Bot') \
    def via(self, notifiable):
        return ["slack"]
Method Description Example
.text()The text you want to show in
the message.text('Welcome to Masonite!')Slack
Options6/12/25, 3:02 AM Masonite Documentation