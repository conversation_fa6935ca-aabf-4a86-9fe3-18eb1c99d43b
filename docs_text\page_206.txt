=== Page 207 ===

207Keep in mind that some simple packages do not need to register resources in your
project though.
We provide two ways to quickly scaffold your package layout:
•starter-package GitHub template
•cookiecutter template
The starter-package is just a GitHub template so you only have to click Use this 
template to create your own GitHub repository scaffolded with the default package
layout and then clone your repository to start developing locally.
The cookiecutter template is using cookiecutter package to scaffold your package
with configuration options (name, author, url...). The advantages is that you won't need to
edit all the occurences of your package name after generation.
Install cookiecutter globally (or locally) on your computer:
Then you just have to run (in the directory where you want your package repository to be
created):
$ pip install cookiecutter
$ cookiecutter https://github.com/girardinsamuel/cookiecutter-masonite-
package.gitCreating a Package
Package Scaffolding
starter-package
cookiecutter template6/12/25, 3:02 AM Masonite Documentation