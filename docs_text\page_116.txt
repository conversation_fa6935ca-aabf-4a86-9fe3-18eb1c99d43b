=== Page 117 ===

117Any routes inside this file will be grouped inside an api middleware stack.
Next, you must choose a model you want to be responsible for authentication. This is
typically your User model. You will have to inherit the AuthenticatesTokens class onto
this model:
This will allow the model to save tokens onto the table.
Next you will add a column to the models table for saving the token. Here we are naming
it api_token but this is configurable by adding a __TOKEN_COLUMN__ attribute to your
model. Your migration file should look like this:
Then migrate your migrations by running:
Next, you can create a new API config file. You can do so simply by running the install
command:def register_routes(self):
    #..
    self.application.bind("routes.api.location", "routes/api")
from masoniteorm.models import Model
from masonite.api.authentication import AuthenticatesTokens
class User(Model, AuthenticatesTokens):
    #..
with self.schema.table("users") as table:
    table.string("api_token").nullable()
$ python craft migrateModel and Migrations
Configuration6/12/25, 3:02 AM Masonite Documentation