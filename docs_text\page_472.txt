=== Page 473 ===

473Go to this file and copy and paste it into your own craft file in the root of your project. If
you are running Masonite 3 then you should already have this file.
Masonite 4 has a new Kernel.py file which is used to customize your application and
contains necessary bootstrapping to start your application.
You will also need to put this into the base of your project.
Go to this file and paste it into your own Kernel.py file in the root of your project.
Now go through this file and customize any of the locations. Masonite 4 uses a different
file structure than Masonite 3. For example, Masonite 3 put all views in 
resources/templates while Masonite 4 has them just in templates.
Because of this, you can either change where the files are located by moving the views to
a new templates directory or you can change the path they are registered in your
Kernel:
Go to your register_configurations method in your Kernel and inside your 
register_templates method you can change it to
Go through the rest of the methods and make sure the paths are set correctly.
Add the following providers to your project:
def register_templates(self):
  self.application.bind("views.location", "resources/templates")Kernel File
Providers6/12/25, 3:02 AM Masonite Documentation