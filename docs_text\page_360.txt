=== Page 361 ===

361Y<PERSON> can now specify anything that is in the container in your middleware constructor and
it will be resolved automatically from the container
Specify the subdomain you want to target with your route. It's common to want to have
separate routes for your public site and multi-tenant sites. This will now look something
like:
Which will target test.example.com/dashboard and not example.com/dashboard.
Read more about subdomains in the Routing documentation.
By default, masonite will look for routes in the app/http/controllers namespace but
you can change this for individual routes:
This will look for the controller in the thirdparty.routes module.def boot(self, View, Request):
    View.share({'request': Request})
Get().domain('test').route('/dashboard', 'DashboardController@show')
Get().module('thirdpary.routes').route('/dashboard', 
'DashboardController@show')Middleware is now resolved by the container
Added new domain method to the Get and Post
classes
Added new module method to Get and Post
routes6/12/25, 3:02 AM Masonite Documentation