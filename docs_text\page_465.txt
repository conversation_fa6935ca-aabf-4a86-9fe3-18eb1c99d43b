=== Page 466 ===

466That should be all you need to get the requirements up to date.
At this point Masonite will not work because our codebase is not updated to work with
Masonite 3
Now we can start changing the application to get our app to run again.
The request class is now not even initialized until a request is sent. Previously the request
class acted as a singleton but it is now a new class that is initialized on every request.
Because of this change, any place you previously had been fetching the request class
before the request is sent will no longer work. This could be in several places but is likely
most common to be in any class __init__ methods that are initialized before a request
was sent, like in a service provider where the wsgi attribute is False.
The response headers are now set on the response class. This makes much more sense
now. Previously we set them on the request class which really didn't make sense.
Any place in your code where you want a header to be on the response. The code
changes may look something like this:
Should now be written as:from masonite.request import Request
class CustomMiddleware(Middleware):
  def __init__(self, request: Request)
      self.request = request
  # ...
  def after(self):
  self.request.header('IS_INERTIA', 'true')Request init Offset
Headers On Response6/12/25, 3:02 AM Masonite Documentation