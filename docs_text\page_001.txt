=== Page 2 ===

2Introduction and Installation
Stop using old frameworks with just a few confusing features. Masonite is the developer
focused dev tool with all the features you need for the rapid development you deserve.
Masonite is perfect for beginners getting their first web app deployed or advanced
developers and businesses that need to reach for the full fleet of features available.
Masonite works hard to be fast and easy from install to deployment so developers can go
from concept to creation in as quick and efficiently as possible. Use it for your next SaaS!
Try it once and you’ll fall in love.
•Mail support for sending emails quickly.
•Queue support to speed your application up by sending jobs to run on a queue or
asynchronously.
•Notifications for sending notifications to your users simply and effectively.
•Task scheduling to run your jobs on a schedule (like everyday at midnight) so you can
set and forget your tasks.
•Events you can listen for to execute listeners that perform your tasks when certain
events happen in your app.
•A BEAUTIFUL Active Record style ORM called Masonite ORM. Amazingness at your
fingertips.
•Many more features you need which you can find in the docs!
These, among many other features, are all shipped out of the box and ready to go. Use
what you need when you need it.Introduction and Installation
Some Notable Features Shipped With Masonite
Requirements6/12/25, 3:02 AM Masonite Documentation