=== Page 272 ===

272Used when you need to check if an integer is within a given range of numbers
You can also check if the input is a valid IPv4 address:
Checks to see the date and time passed is in the future. This will pass even if the
datetime is 5 minutes in the future.
You may also pass in a timezone for this rule:"""
{
  'attendees': 54
}
"""
validate.in_range('attendees', min=24, max=64)
"""
{
  'address': '78.281.291.8'
}
"""
validate.ip('address')
"""
{
  'date': '2019-10-20', # Or date in the future
}
"""
validate.is_future('date')Ip
Is_future6/12/25, 3:02 AM Masonite Documentation