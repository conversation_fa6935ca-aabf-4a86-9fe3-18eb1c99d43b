=== Page 249 ===

249When validating a dictionary like this:
then
•the attribute will be the value (Masonite)
•the key will be the dictionary key (name)
•the dictionary will be the full dictionary in case you need to do any additional
checks.
Message method
The message method needs to return a string used as the error message. If you are
making the rule above then our rule may so far look something like:
Negated Message
The negated message method needs to return a message when this rule is negated. This
will basically be a negated statement of the message method:def passes(self, attribute, key, dictionary):
    """The passing criteria for this rule.
    ...
    """
    return attribute == 'Masonite'
{
  'name': 'Masonite'
}
def passes(self, attribute, key, dictionary):
    """The passing criteria for this rule.
    ...
    """
    return attribute == 'Masonite'
def message(self, key):
    return '{} must be equal to Masonite'.format(key)6/12/25, 3:02 AM Masonite Documentation