=== Page 239 ===

239Although the command to schedule:run above is useful and needed, we still need a
way to run it every minute. We can do this with a cronjob on the server
We'll show you an example cron job and then we will walk through how to build it.
When a cron job runs, it will typically run commands with a /bin/sh command instead of
the usual /bin/bash. Because of this, craft may not be found on the machine so we need
to tell the cron job the PATH that should be loaded in. We can simply find the PATH by
going to our project directory and running:
Which will show an output of something like:
If you are using a virtual environment for development purposes then you need to run
the env command inside your virtual environment.
We can then copy the PATH and put it in the cron job.
To enter into cron, just run:PATH=/Users/<USER>/Programming/project_name/venv/bin:/Library/Framewo
rks/Python.framework/Versions/3.7/bin:/usr/local/bin:/usr/bin:/bin:/usr
/sbin:/sbin:/Library/Frameworks/Python.framework/Versions/3.7/bin
* * * * * cd /Users/<USER>/Programming/project_name && source 
venv/bin/activate && python craft schedule:run
$ env
...
__CF_USER_TEXT_ENCODING=0x1F5:0x0:0x0
PATH=/Library/Frameworks/Python.framework/Versions/3.7/bin:/usr/local/b
in:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Frameworks/Python.framework/V
ersions/3.7/bin
PWD=/Users/<USER>/Programming/masonite
...Getting The Path6/12/25, 3:02 AM Masonite Documentation