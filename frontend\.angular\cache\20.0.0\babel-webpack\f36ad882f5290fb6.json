{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let OAuthStateService = /*#__PURE__*/(() => {\n  class OAuthStateService {\n    constructor() {\n      this.callbackData = null;\n      this.isProcessing = false;\n    }\n    setCallbackData(data) {\n      if (!this.callbackData || !this.callbackData.processed) {\n        this.callbackData = {\n          ...data\n        };\n        console.log('🔄 OAuth State Service - Callback data set:', this.callbackData);\n      }\n    }\n    getCallbackData() {\n      return this.callbackData;\n    }\n    markAsProcessed() {\n      if (this.callbackData) {\n        this.callbackData.processed = true;\n        console.log('✅ OAuth State Service - Marked as processed');\n      }\n    }\n    clear() {\n      this.callbackData = null;\n      this.isProcessing = false;\n      console.log('🧹 OAuth State Service - Cleared');\n    }\n    setProcessing(processing) {\n      this.isProcessing = processing;\n    }\n    getProcessing() {\n      return this.isProcessing;\n    }\n    isAlreadyProcessed() {\n      return this.callbackData?.processed === true;\n    }\n    static #_ = this.ɵfac = function OAuthStateService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthStateService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OAuthStateService,\n      factory: OAuthStateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return OAuthStateService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}