=== Page 255 ===

255Y<PERSON> can also use this in addition to other rules:from masonite.request import Request
from masonite.response import Response
from app.rules.LoginForm import AcceptedTerms
def show(self, request: Request, response: Response):
    """
    Incoming Input: {
        'user': 'username123',
        'email': '<EMAIL>',
        'terms': 'on'
    }
    """
    errors = request.validate(AcceptedTerms)
    if errors:
        request.session.flash('errors', errors)
        return response.back()
from app.rules.LoginForm import AcceptedTerms
from masonite.validations import email
from masonite.request import Request
from masonite.response import Response
def show(self, request: Request, response: Response):
    """
    Incoming Input: {
        'user': 'username123',
        'email': '<EMAIL>',
        'terms': 'on'
    }
    """
    errors = request.validate(
        AcceptedTerms,
        email('email')
    )
    if errors:
        return response.back().with_errors(errors)
Message Bag6/12/25, 3:02 AM Masonite Documentation