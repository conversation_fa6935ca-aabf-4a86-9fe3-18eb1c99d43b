version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: crawler_postgres
    environment:
      POSTGRES_DB: website_crawler
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: crawler_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./loopback-backend/database/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - crawler_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LoopBack Backend API
  backend:
    build:
      context: ./loopback-backend
      dockerfile: Dockerfile
    container_name: crawler_backend
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: website_crawler
      DB_USER: postgres
      DB_PASSWORD: crawler_password
      JWT_SECRET: your_production_jwt_secret_change_this
      PORT: 3002
      CORS_ORIGIN: http://localhost:4200
      STORAGE_PATH: /app/storage
    volumes:
      - backend_storage:/app/storage
      - ./loopback-backend/scripts:/app/scripts
    ports:
      - "3002:3002"
    networks:
      - crawler_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Angular Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: crawler_frontend
    environment:
      API_URL: http://localhost:3002
    ports:
      - "4200:80"
    networks:
      - crawler_network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: crawler_redis
    ports:
      - "6379:6379"
    networks:
      - crawler_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  backend_storage:
    driver: local

networks:
  crawler_network:
    driver: bridge
