=== Page 54 ===

54•integer
•int (alias for integer)
•string
•signed
•uuid
•any
You can also create your own route compilers if you want to be able to support specific
regex matches for routes.
All route compilers will need to be added to the top of your register_routes() method
in your Kernel.py file.
Note: The compile methods need to happen before the routes are loaded in this
method so make sure it is at the top. You may also put it in any method that appears
before the register_routes() method.
Sometimes you may want to default a route if no other routes are found. since it won't
require a url we just need to pass it a controller and action.    def register_routes(self):
        
Route.set_controller_locations(self.application.make("controllers.locat
ion"))
        Route.compile("handle", r"([\@\w\-=]+)")
        #..
Route.fallback("WelcomeController@fallback")Creating Route Compilers
Fallback Routes6/12/25, 3:02 AM Masonite Documentation