=== Page 15 ===

15Not surprisingly, we have a command to create migrations. You can read more about 
Database Migrations here but we'll simplify it down to the command and explain a little
bit of what's going on:
This command simply creates the start of a migration file that we will use to create the
posts table. By convention, table names should be plural (and model names should be
singular but more on this later).
This will create a migration in the databases/migrations folder. Let's open that up and
starting on line 6 we should see something that looks like:
Lets add a title, an author, and a body to our posts tables.
terminal
$ python craft migration create_posts_table --create posts
databases/migrations/20YY:MM:DD:ABCDEF:create:posts:table.py
def up(self):
    """
    Run the migrations.
    """
    with self.schema.create('posts') as table:
        table.increments('id')
        table.timestamps()
databases/migrations/2018:01:09:043202:create:posts:table.pyCraft Command6/12/25, 3:02 AM Masonite Documentation