=== Page 452 ===

452This will now resolve from the container when you resolve it as a parameter list. This
means that you will never get back a class inside places like controllers.
Now the above code would look something like this:
notice it now returns an object. This is because Masonite will check before it resolves the
class if the class itself needs to be resolved (if it is a class). If SomeClass requires the
request object, it will be passed automatically when you resolve it.
Masonite 2.2 focused a lot on new testing aspects of Masonite and has some big
rewrites of the package internally.
The UnitTest class has been completely removed in favor of the new 
masonite.testing.TestCase method.
An import that looked like this:
Should now look like this:from some.place import SomeClass
def show(self, request: Request, some: SomeClass):
    some #== <some.place.SomeClass x9279182>
from masonite.testing import UnitTest
class TestSomeUnit(UnitTest):
    ...Testing
UnitTest class6/12/25, 3:02 AM Masonite Documentation