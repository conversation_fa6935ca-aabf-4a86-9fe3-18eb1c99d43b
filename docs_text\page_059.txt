=== Page 60 ===

60If you had a parameter in your route, you may fetch it by specifying the parameter in the
controller's method:
Since the id parameter is in the route we can fetch the value of this parameter by
specifying it in the controller method signature:
Another way to fetch route parameters is through the request class:
Masonite will be able to pick up controllers (inheriting Controller class) using string
binding in the registered controllers locations.
The default registered controllers location is app/controllers and is defined in project 
Kernel.py configuration file:Route.get('/users/@user_id', 'UsersController@user')
def show(self, user_id):
  return User.find(user_id)
from masonite.request import Request
def show(self, request: Request):
  return User.find(request.param('user_id'))
self.application.bind("controllers.location", "app/controllers")
# ...
Route.set_controller_locations(self.application.make("controllers.locat
ion"))Request Parameters
Controller Locations
Setting locations6/12/25, 3:02 AM Masonite Documentation