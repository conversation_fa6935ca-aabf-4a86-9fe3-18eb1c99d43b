=== Page 454 ===

454Now becomes:
Again this is to prevent developers from needing to switch between snake_case and 
camelCase when using Masonite methods and unittest methods.
The route method that looked something like this:
Has now been replaced with the method name of the route. So to get a GET route you
would do:
or a POST route:
So be sure to update all methods of self.route() with the correct request methods.
In 2.1 you had to manually load your routes in like this:self.route('/some/protect/route').is_named()
self.get('/some/protect/route').isNamed()
def test_route_has_the_correct_name(self):
    assert self.route('/testing')
def test_route_has_the_correct_name(self):
    assert self.get('/testing')
def test_route_has_the_correct_name(self):
    assert self.post('/testing')Route method
Loading Routes6/12/25, 3:02 AM Masonite Documentation