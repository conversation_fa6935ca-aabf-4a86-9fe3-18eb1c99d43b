=== Page 262 ===

262Now if the required rule fails it will throw a ValueError. You can catch the message like
so:
You can also specify which exceptions should be thrown with which key being checked
by using a dictionary:"""
{
  'domain': 'http://google.com',
  'email': '<EMAIL>'
  'user': {
     'id': 1,
     'email': '<EMAIL>',
     'status': {
         'active': 1,
         'banned': 0
     }
  }
}
"""
errors = request.validate(
    validate.required('user.email', raises=True),
    validate.truthy('user.status.active')
)
try:
    errors = request.validate(
        validate.required('user.email', raises=True),
        validate.truthy('user.status.active')
    )
except ValueError as e:
    str(e) #== 'user.email is required'
Custom Exceptions6/12/25, 3:02 AM Masonite Documentation