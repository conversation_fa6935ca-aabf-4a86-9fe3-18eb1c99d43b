=== Page 267 ===

267This is used to verify that the value is a valid date. Pendulum module is used to verify
validity. It supports the RFC 3339 format, most ISO 8601 formats and some other
common formats.
Used to check that value is different from another field value. It is the opposite of 
matches validation rule.
Used to check that an array value contains distinct items."""
{
  'description': 'Masonite is an amazing framework'
}
"""
validate.contains('description', 'Masonite')
"""
{
  'date': '1975-05-21T22:00:00'
}
"""
validate.date('date')
"""
{
  'first_name': 'Sam',
  'last_name': '<PERSON><PERSON><PERSON>'
}
"""
validate.different('first_name', 'last_name')Date
Different
Distinct6/12/25, 3:02 AM Masonite Documentation