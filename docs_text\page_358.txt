=== Page 359 ===

359Masonite 1.3
Masonite 1.3 comes with a plethora of improvements over previous versioning. This
version brings new features such as Queue and Mail drivers as well as various bug fixes.
Previously when a you tried to redirect using the Request.redirect() method,
Masonite would sometimes send the browser to an infinite redirection. This was because
masonite was not resetting the redirection attributes of the Request class.
Previously the content length in the request header was not being set correctly which led
to the gunicorn server showing a warning that the content length did not match the
content of the output.
Previously the Request class simply got the input data on both POST and GET requests
by converting the wsgi.input WSGI parameter into a string and parsing. All POST input
data is now retrieved using FieldStorage which adds support for also getting files
from multipart/formdata requests.Introduction
Fixed infinite redirection
Fixed browser content length
Changed how request input data is retrieved
Added Upload drivers6/12/25, 3:02 AM Masonite Documentation