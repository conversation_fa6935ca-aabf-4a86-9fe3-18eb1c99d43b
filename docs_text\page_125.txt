=== Page 126 ===

126Y<PERSON> can get the current authenticated user:
If the user is not authenticated, this will return None.
You can register several routes quickly using the auth class:
This will register the following routes:from masonite.authentication import Auth
from masonite.request import Request
def logout(self, auth: Auth):
  user = auth.logout()
from masonite.authentication import Auth
from masonite.request import Request
def login(self, auth: Auth, request: Request):
  user = auth.user() #== <app.User.User>
from masonite.authentication import Auth
ROUTES = [
  #..
]
ROUTES += Auth.routes()
URI Description
GET /login Displays a login form for the userUser
Routes6/12/25, 3:02 AM Masonite Documentation