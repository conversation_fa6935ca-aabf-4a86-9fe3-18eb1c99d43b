=== Page 337 ===

337Console Tests
You can test what has been output to standard console during Masonite unit tests thanks
to useful console assertions.
Output here is the standard output often named stdout. Error here is the standard error
often named stderr.
External packages, prints in your code can output content in console (as output or error).
If you want to assert content output by a Masonite command you should use Commands
Tests assertions instead.
The following assertions are available:
•assertConsoleEmpty
•assertConsoleNotEmpty
•assertConsoleExactOutput
•assertConsoleOutputContains
•assertConsoleOutputMissing
•assertConsoleHasErrors
•assertConsoleExactError
•assertConsoleErrorContains
Assert that nothing has been printed to the console.
Assert that something has been printed to the console (output or error).
Available Assertions
assertConsoleEmpty
assertConsoleNotEmpty6/12/25, 3:02 AM Masonite Documentation