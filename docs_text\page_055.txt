=== Page 56 ===

56Y<PERSON> could optionally pass in the methods you want this to be able to support if you
needed to:
Application routes can be listed with the routes:list Masonite command. Routes will
be displayed in a table with relevant info such as route name, methods, controller and
enabled middlewares for this route.
Routes can be filtered by methods:
Routes can be filtered by name:ROUTES = [
  Route.view("/url", "view.name", {"key": "value"}, method=["get", 
"post"])
]
python craft routes:list -M POST,PUT
python craft routes:list -N usersList Routes6/12/25, 3:02 AM Masonite Documentation