=== Page 84 ===

84Similiar to view sharing, view composing allows you to share templates between specific
templates
You may also pass a list of templates:
You will typically do this inside your appService Provider (if you don't have one, you
should create one):
There are quite a few built in helpers in your views. Here is an extensive list of all view
helpers:from masonite.facades import View
class AppProvider(Provider):
    def register(self):
        View.share({'copyright': '2021'})
View.composer('dashboard', {'copyright': '2021'})
View.composer(['dashboard', 'dashboard/users'], {'copyright': '2021'})
from masonite.facades import View
class AppProvider(Provider):
    def register(self):
        View.composer('dashboard', {'copyright': '2021'})View Composing
Helpers
Request6/12/25, 3:02 AM Masonite Documentation