=== Page 316 ===

316HTTP Tests
To make a request in your tests, you may use the get, post, put, patch, or delete
methods within your test. These methods do not actually issue a "real" HTTP request to
your application. Instead of returning a Masonit class Response instance, test request
methods return a HTTPTestResponseinstance, which provides a variety of helpful
assertions that allow you to inspect and assert application's responses.
The request and responses of a test can be fetch by accessing the request and 
response attributes.
During tests you can register routes used only for testing purposes. For this you can use
the addRoutes() method at beginning of your tests:def test_basic_request(self):
    self.get("/").assertOk()
def test_request_and_response(self):
    request = self.get('/testing').request # 
<masonite.requests.Request>
    response = self.get('/testing').response # 
<masonite.response.Response>
@classmethod
def setUpClass(cls):
    super().setUpClass()
    cls.addRoutes(
        Route.get("/", "TestController@show"),
        Route.post("/", "TestController@show"),
    )Getting request and response
Registering routes6/12/25, 3:02 AM Masonite Documentation