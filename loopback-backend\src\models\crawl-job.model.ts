import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {User} from './user.model';
import {CrawledContent} from './crawled-content.model';
import {GeneratedDocument} from './generated-document.model';

@model({
  settings: {
    strict: true,
    indexes: {
      userIdIndex: {
        keys: {
          userId: 1,
        },
      },
      statusIndex: {
        keys: {
          status: 1,
        },
      },
      createdAtIndex: {
        keys: {
          createdAt: -1,
        },
      },
    },
    postgresql: {
      schema: 'public',
      table: 'crawl_job'
    }
  },
})
export class CrawlJob extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      format: 'uri',
      minLength: 10,
      maxLength: 2000,
      errorMessage: 'URL should be a valid URL',
    },
  })
  url: string;

  @property({
    type: 'string',
    required: true,
    default: 'pending',
    jsonSchema: {
      enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'paused'],
    },
  })
  status: string;

  @property({
    type: 'number',
    default: 2,
    jsonSchema: {
      minimum: 1,
      maximum: 10,
      errorMessage: 'Crawl depth should be between 1 and 10',
    },
  })
  maxDepth: number;

  @property({
    type: 'number',
    default: 100,
    jsonSchema: {
      minimum: 1,
      maximum: 10000,
      errorMessage: 'Max pages should be between 1 and 10000',
    },
  })
  maxPages: number;

  @property({
    type: 'array',
    itemType: 'string',
    default: ['text/html'],
    jsonSchema: {
      items: {
        type: 'string',
        enum: ['text/html', 'application/pdf', 'text/plain', 'application/json', 'text/css', 'application/javascript'],
      },
    },
  })
  allowedContentTypes: string[];

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
    jsonSchema: {
      items: {
        type: 'string',
        pattern: '^[a-zA-Z0-9*._-]+$',
      },
    },
  })
  excludePatterns: string[];

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
    jsonSchema: {
      items: {
        type: 'string',
        pattern: '^[a-zA-Z0-9*._-]+$',
      },
    },
  })
  includePatterns: string[];

  @property({
    type: 'boolean',
    default: true,
  })
  followExternalLinks: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  respectRobotsTxt: boolean;

  @property({
    type: 'number',
    default: 1000,
    jsonSchema: {
      minimum: 100,
      maximum: 10000,
      errorMessage: 'Delay should be between 100ms and 10000ms',
    },
  })
  delayBetweenRequests: number;

  @property({
    type: 'object',
    default: {},
  })
  crawlOptions: object;

  @property({
    type: 'number',
    default: 0,
  })
  totalPages: number;

  @property({
    type: 'number',
    default: 0,
  })
  processedPages: number;

  @property({
    type: 'number',
    default: 0,
  })
  failedPages: number;

  @property({
    type: 'number',
    default: 0,
    jsonSchema: {
      minimum: 0,
      maximum: 100,
    },
  })
  progressPercentage: number;

  @property({
    type: 'string',
  })
  errorMessage?: string;

  @property({
    type: 'object',
  })
  crawlStatistics?: object;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'started_at'
    }
  })
  startedAt?: Date;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'completed_at'
    }
  })
  completedAt?: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'created_at'
    }
  })
  createdAt: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'updated_at'
    }
  })
  updatedAt: Date;

  @belongsTo(() => User, {}, {
    postgresql: {
      columnName: 'user_id'
    }
  })
  userId: string;

  @hasMany(() => CrawledContent, {keyTo: 'crawlJobId'})
  crawledContents: CrawledContent[];

  @hasMany(() => GeneratedDocument, {keyTo: 'crawlJobId'})
  generatedDocuments: GeneratedDocument[];

  constructor(data?: Partial<CrawlJob>) {
    super(data);
  }
}

export interface CrawlJobRelations {
  user?: User;
  crawledContents?: CrawledContent[];
  generatedDocuments?: GeneratedDocument[];
}

export type CrawlJobWithRelations = CrawlJob & CrawlJobRelations;
