=== Page 400 ===

400If the incoming request is a JSON request, Masonite will now return all errors as JSON
This is more of an internal change for Core itself.
Before we had to specify that we wanted the server to auto-reload by specifying a -r flag:
Now we can just specify the serve command it will default to auto-reloading:
You can now specify it to NOT auto-reload by passing in 1 of these 2 commands:{
  "error": {
    "exception": "Invalid response type of <class 'set'>",
    "status": 500,
    "stacktrace": [
        "/Users/<USER>/Programming/core/bootstrap/start.py line 38 in 
app",
        "/Users/<USER>/Programming/core/masonite/app.py line 149 in 
resolve",
        
"/Users/<USER>/Programming/core/masonite/providers/RouteProvider.py 
line 92 in boot",
        "/Users/<USER>/Programming/core/masonite/response.py line 105 
in view"
    ]
  }
}
$ craft serve -r
$ craft serveRearranged Drivers into their own folders
Craft serve command defaults to auto-reloading6/12/25, 3:02 AM Masonite Documentation