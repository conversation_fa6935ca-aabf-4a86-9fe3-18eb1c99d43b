=== Page 328 ===

328Assert that the request has the given route middleware. The registration key of the
middleware should be provided.
Assert that the route used the given controller. A class or a string can be provided. If it's a
string it should be formatted as follow ControllerName@method.
Assert that the route used has the given parameter name and value (if provided).
Assert that response is JSON and contains the given data dictionary (if provided). The
assertion will pass even if it is not an exact match.self.get("/").assertHasRouteMiddleware(middleware_name)
# route_middleware = {"web": [SessionMiddleware, VerifyCsrfToken]}
self.get("/").assertHasRouteMiddleware("web")
self.get("/").assertHasController(controller)
from app.controllers import WelcomeController
self.get("/").assertHasController(WelcomeController)
self.get("/").assertHasController("WelcomeController@index")
self.get("/").assertRouteHasParameter(key, value=None)
self.get("/").assertJson(data={})
self.get("/").assertJson()  # check that response is JSON
self.get("/").assertJson({"key": "value", "other": "value"})assertHasController
assertRouteHasParameter
assertJson6/12/25, 3:02 AM Masonite Documentation