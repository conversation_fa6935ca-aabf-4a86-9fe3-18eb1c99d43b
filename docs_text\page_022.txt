=== Page 23 ===

23and create a new store method on our controller:
Now notice above in the form we are going to be receiving 2 form inputs: title and body.
So let's import the Post model and create a new post with the input.from masonite.routes import Route
# ...
ROUTES = [
    # ...
    Route.post('/blog/create', 'BlogController@store')
]
app/controllers/BlogController.py
...
def show(self, view: View):
    return view.render('blog')
# New store Method
def store(self):
    pass
app/controllers/BlogController.py
from app.models.Post import Post
from masonite.request import Request
# ...
def store(self, request: Request):
    Post.create(
        title=request.input('title'),
        body=request.input('body'),
        author_id=request.user().id
    )
    return 'post created'6/12/25, 3:02 AM Masonite Documentation