=== Page 22 ===

22Javascript files are the same exact thing:
For more information on static files, checkout the Static Files documentaton.
Notice that our action is going to /blog/create so we need to direct a route to our
controller method. In this case we will direct it to a store method.
Let's open back up the routes/web.py file and create a new route. Just add this to the 
ROUTES list:templates/blog.html
<link href="/static/blog.css" rel="stylesheet">
@if auth()
    <form action="/blog/create" method="POST">
        {{ csrf_field }}
        <label> Title </label>
        <input type="name" name="title"><br>
        <label> Body </label>
        <textarea name="body"></textarea>
        <input type="submit" value="Post!">
    </form>
@else
    <a href="/login">Please Login</a>
@endif
<script src="/static/script.js"></script>
routes/web.pyThe Controller For Creating And The Container6/12/25, 3:02 AM Masonite Documentation