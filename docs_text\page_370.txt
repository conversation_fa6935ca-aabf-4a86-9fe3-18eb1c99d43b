=== Page 371 ===

371Before, you had to use the Manager class associated with a driver to switch a driver. For
example:
Now you can switch drivers from the driver itself:
This version has been fine tuned for adding packages to Masonite. This version will come
along with a new Masonite Billing package. The development of Masonite Billing has
discovered some rough spots in package integrations. One of these rough spots were
adding controllers that were not in the project. For example, Masonite Billing allows
adding a controller that handles incoming Stripe webhooks. Although this was possible
before this release, Masonite 1.6 has added a new syntax:
Notice the new forward slash in the beginning where the string controller goes.def show(self, Upload, UploadManager):
    Upload.store(...) # default driver
    UploadManager.driver('s3').store(...) # switched drivers
def show(self, Upload):
    Upload.store(...) # default driver
    Upload.driver('s3').store(...) # switched drivers
ROUTES = [
    ...
    # Old Syntax:
    Get().route('/url/here', 
'Controller@show').module('billing.controllers')
    # New Syntax:
    Get().route('/url/here', '/billing.controllers.Controller@show')
    ...
]New Absolute Controllers Syntax
Changed How Controllers Are Created6/12/25, 3:02 AM Masonite Documentation