=== Page 416 ===

416Masonite 3.0
Masonite 3 is a big change from Masonite 2. The most obvious change is the fact that we
have dropped support for Orator and have created a new package called Masonite ORM
that is intended to be a drop in replacement of Orator.
Hopefully many of you may not even tell we are no longer using Orator.
Below is a list of high level changes of things that are new or changed in Masonite 3 from
Masonite 2. If you want to see how to upgrade from Masonite 2 to 3 then refer to the 
Upgrade Guide
Since Python 3.9 came out we are dropping support for Python 3.5. Masonite adds some
f string formats so Masonite will break on Python 3.5. Throughout Masonite 3.0 we will
be upgrading the codebase to use features available to us that we were unable to do
because of Python 3.5 support.
Masonite 3 supports all Python versions 3.6 and above.
In Masonite 2 the headers were largely controlled oddly. Internally they were just saved as
dictionaries and then attached to the response later. Also strangely, the response headers
were attached to the request class and not the response class, even though they really
had nothing to do with the response class. This also presented an issue because a
request header is one sent by a client and a response header is one set by your app but
they were both being saved in the same place so it was impossible to be able to tell who
set the header.
Now in Masonite 3 we refactored the headers to use a new HeaderBag class which is
used to maintain Header classes. We put the exact same class on the response class
as well so they can be managed separately.Python Support
Headers6/12/25, 3:02 AM Masonite Documentation