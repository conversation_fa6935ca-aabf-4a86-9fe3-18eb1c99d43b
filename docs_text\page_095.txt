=== Page 96 ===

96Blocks are sections of code that can be used as placeholders for a parent template.
These are only useful when used with the extends above. The "base.html" template is
the parent template and contains blocks, which are defined in the child template
"blocks.html".
Line Statements:<div data-gb-custom-block data-tag="extends" data-
0='components/base.html'></div>
<div data-gb-custom-block data-tag="block">
    <p> read below to find out what a block is </p>
</div>
<!-- components/base.html -->
<html>
    <head>
        @block css
        <!-- block named "css" defined in child template will be 
inserted here -->
        @endblock
    </head>
<body>
    <div class="container">
        @block content
        <!-- block named "content" defined in child template will be 
inserted here -->
        @endblock
    </div>
@block js
<!-- block named "js" defined in child template will be inserted here -
->
@endblock
</body>
</html>Blocks6/12/25, 3:02 AM Masonite Documentation