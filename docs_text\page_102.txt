=== Page 103 ===

103If you do not wish to cast the value then you can provide a third parameter cast=False:
The current Masonite environment is defined through the APP_ENV variable located in
your .env file. You can access it easily through the Masonite app environment()
helper:
When running tests the environment will be set to testing. You can use 
is_running_tests() helper to check if environment is testing:
You can also check if the environment is a production environment with:
The debug mode is controlled by the APP_DEBUG environment variable used in 
config/application.py configuration file. When crafting a new project, the debugFalse False (bool)
smtp smtp (string)
from masonite.environment import env
env('APP_DEBUG', False, cast=False) #== "False" (str)
APP_ENV=local
app.environment() #== local
app.is_running_tests() #== True if running tests
app.is_production() #== True if APP_ENV=productionGetting Current Environment
Debug Mode6/12/25, 3:02 AM Masonite Documentation