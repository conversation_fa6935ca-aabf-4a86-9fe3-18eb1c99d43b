=== Page 294 ===

294If you need to utilize a container outside the normal flow of Masonite like inside a
command then you can import the container directly.
This would look something like:
Sometimes when you resolve an object or class, you want a different value to be
returned.
We can pass a simple value as the second parameter to the swap method which will be
returned instead of the object being resolved. For example this is used currently when
resolving the Mail class like this:
but the class definition for the Mail class here looks like this:
How does it know to resolve the smtp driver instead? It's because we added a container
swap. Container swaps are simple, they take the object as the first parameter and either a
value or a callable as the second.from wsgi import container
from masonite import Queue
class SomeCommand:
    def handle(self):
        queue = container.make(Queue)
        queue.push(..)
from masonite import Mail
def show(self, mail: Mail):
    mail #== <masonite.drivers.MailSmtpDriver>
class Mail:
    passContainer Swapping
Using a value:6/12/25, 3:02 AM Masonite Documentation