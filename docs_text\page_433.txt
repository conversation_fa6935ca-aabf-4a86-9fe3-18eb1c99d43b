=== Page 434 ===

434By default this points to the app directory where models are stored by default but if you
moved your models to other directories like app/models or app/admin/models then add
those directories to your list:
Be caution that this will autoload all models into the Service Container with the class
name as the key and the class as the binding.
Because of a minor rewrite of the Request class, we now do not need the
RedirectionProvider. You can remove the RedirectionProvider completely in your 
PROVIDERS list.'''
|----------------------------------------------------------------------
----
| Autoload Directories
|----------------------------------------------------------------------
----
|
| List of directories that are used to find classes and autoload them 
into
| the Service Container. This is initially used to find models and load
| them in but feel free to autoload any directories
|
'''
AUTOLOAD = [
    'app',
]
....
AUTOLOAD = [
    'app',
    'app/models',
    'app/admin/models'
]
RedirectionProviderconfig/application.py6/12/25, 3:02 AM Masonite Documentation