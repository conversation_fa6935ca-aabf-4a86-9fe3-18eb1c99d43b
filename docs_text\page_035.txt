=== Page 36 ===

36will immediately be available in your project. You may need to restart your
development server.
•Any changes you make to this package just push to your feature branch on your fork
and follow the PR process below.
Comments are a vital part of any repository and should be used where needed. It is
important not to overcomment something. If you find you need to constantly add
comments, you're code may be too complex. Code should be self documenting (with
clearly defined variable and method names)
There are 3 main type of comments you should use when developing for Masonite:
All modules should have a docstring at the top of every module file and should look
something like:
Notice there are no spaces before and after the sentence.
All methods and functions should also contain a docstring with a brief description of
what the module does
For example:"""This is a module to add support for Billing users."""
from masonite.request import Request
...Comments
Types of comments to use
Module Docstrings
Method and Function Docstrings6/12/25, 3:02 AM Masonite Documentation