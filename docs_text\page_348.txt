=== Page 349 ===

349The queries collector shows all the queries made during that request as well as the time
it took to perform that query. Use this to see where bottle necks are in your application.
Slow queries lead to slow load times for your users.
The request collector shows you information related to the request such as inputs,
parameters and headers.
The message collector contains messages you can add in your application. Instead of
adding a bunch of print statements you can add a message:
You could also add tags which will create a colored tag in the content tab:
This collector adds all of your environment variables to your debugbar as well as the
Python and Masonite versions.from debugbar.debugger import Debugger
Debugger.get_collector('messages').add_message("a debug message")
Debugger.get_collector('messages').add_message("a debug message", tags=
{"color": "green", "message": "tag name"})Queries Collector
Request Collector
Message Collector
Environment Collector
Measures Collector6/12/25, 3:02 AM Masonite Documentation