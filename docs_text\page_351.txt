=== Page 352 ===

352from jinja2 import Template
class YourCollector:
    def __init__(self, name="Your Collector", 
description="Description"):
        self.messages = []
        self.name = name
        self.description = description
    def restart(self):
        self.messages = []
        return self
    def add(self, key, value):
        self.messages.append({key: value})
    
    def collect(self):
        collection = []
        for message in self.messages:
            for key, value in message.items():
                collection.append(
                    {
                        "key": key,
                        "value": value
                    }
                )
        # # render data to html
        template = Template(self.html())
        return {
            "description": self.description,
            "count": len(collection),
            "data": collection,
            "html": template.render({"data": collection}),
        }
    
    def html(self):
        return """
        <div>
            <div class="flex justify-between p-4">
                <p>{{ object.key }}: {{ object.value }}</p>
            </div>
        </div>
        """6/12/25, 3:02 AM Masonite Documentation