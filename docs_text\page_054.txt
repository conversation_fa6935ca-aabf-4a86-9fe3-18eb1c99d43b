=== Page 55 ===

55This route will be evaluated at the very end, after all the routes (including api routes) and 
should be added just once.
Route groups are a great way to group mulitple routes together that have similiar options
like a prefix, or multiple routes with the same middleware.
A route uses the group() method that accepts a list of routes and keyword arguments
for the options:
The prefix and name options will prefix the options set in the routes inside the group. In
the above example, the names of the routes would dashboard.settings with a URL of 
/dashboard/settings and dashboard.monitor and a URL of /dashboard/monitor.
Route views are a quick way to return a view quickly without needing to build a controller
just to return a view:ROUTES = [
  Route.group([
    Route.get('/settings', 
'DashboardController@settings').name('settings'),
    Route.get('/monitor', 
'DashboardController@monitor').name('monitor'),
  ],
  prefix="/dashboard",
  middleware=['web', 'cors'],
  name="dashboard."),
  domain="docs"
]
ROUTES = [
  Route.view("/url", "view.name", {"key": "value"})
]Route Groups
Route Views6/12/25, 3:02 AM Masonite Documentation